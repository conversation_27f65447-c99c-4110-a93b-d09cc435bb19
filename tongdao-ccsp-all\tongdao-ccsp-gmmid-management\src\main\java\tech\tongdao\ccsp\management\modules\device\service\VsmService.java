package tech.tongdao.ccsp.management.modules.device.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ccsp.gmmid.beans.form.Pager;
import tech.tongdao.ccsp.management.modules.device.entity.VsmEntity;
import tech.tongdao.ccsp.management.modules.device.form.VsmForm;
import tech.tongdao.ccsp.management.modules.device.form.third.dj.DjVsmForm;
import tech.tongdao.ccsp.management.modules.device.form.third.dj.DjVsmSummaryForm;
import tech.tongdao.ccsp.management.modules.device.form.third.dj.DjVsmStatisticsForm;
import tech.tongdao.ccsp.management.modules.device.form.third.SingleResult;

import java.util.List;

/**
 * <p>
 * 虚机 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-10
 */
public interface VsmService extends IService<VsmEntity> {

    VsmForm save(VsmForm form);

    VsmForm update(VsmForm form);

    Boolean deleteById(Long id);

    Pager<VsmForm> listPage(String key, int page, int pageSize);

    Pager<VsmForm> listPageByDeviceGroupId(Long deviceGroupId, int page, int pageSize);

    Boolean leaveGroup(Long deviceGroupId, Long vsmId);

    Boolean removeGroupDevice(Long id, Long vsmId);

    Pager<VsmForm> listAvailableDevices(int page, int pageSize);

    Boolean addToGroup(Long deviceGroupId, List<Long> vsmIds);

    Boolean reboot(VsmForm vsmForm);

    SingleResult checkVsmStatus(VsmForm vsmForm);

    Boolean start(VsmForm vsmForm);

    Boolean stop(VsmForm vsmForm);

    int countByChsmId(Long id);

    int countByDeviceGroupId(Long deviceGroupId);

    VsmForm updateNetwork(VsmForm vsmForm);

    DjVsmForm fetchVsmInfo(Long vsmId, Long chsmId);

    DjVsmStatisticsForm fetchVsmStatistics(String vsmCode, String chsmIp, Integer chsmPort);

    Boolean importVsm(Long chsmId, List<DjVsmSummaryForm> formList);

    /**
     * 获取可用的HSM设备资源列表
     * 
     * @return 可用设备列表
     */
    List<VsmForm> listAvailableHsmDevices();


    List<VsmForm> listHsmDevicesByStatus(String status, Long deviceGroupId);
    /**
     * 获取可用的HSM设备资源列表
     *
     * @param page 页码
     * @param pageSize 页大小
     * @return 可用设备列表
     */
    Pager<VsmForm> listAvailableHsmDevices(int page, int pageSize);

    /**
     * 根据设备状态和类型获取HSM设备资源
     *
     * @param status 设备状态
     * @param deviceGroupId 设备组ID（可选）
     * @param page 页码
     * @param pageSize 页大小
     * @return HSM设备列表
     */
    Pager<VsmForm> listHsmDevicesByStatus(String status, Long deviceGroupId, int page, int pageSize);

    /**
     * 获取HSM设备资源详细信息
     * 
     * @param vsmId 设备ID
     * @return 设备详细信息
     */
    VsmForm getHsmDeviceDetail(Long vsmId);

    /**
     * 根据IP地址和端口查找HSM设备
     * 
     * @param ip IP地址
     * @param port 端口
     * @return 设备信息
     */
    VsmForm findHsmDeviceByIpAndPort(String ip, Integer port);

    /**
     * 获取设备组下可用的HSM设备列表
     * 
     * @param deviceGroupId 设备组ID
     * @return HSM设备列表
     */
    List<VsmForm> listAvailableHsmDevicesByGroup(Long deviceGroupId);

    /**
     * 验证HSM设备是否可用
     * 
     * @param vsmId 设备ID
     * @return 是否可用
     */
    Boolean validateHsmDeviceAvailability(Long vsmId);

    /**
     * 获取HSM设备的连接配置信息
     * 
     * @param vsmId 设备ID
     * @return 连接配置信息
     */
    VsmForm getHsmDeviceConnectionConfig(Long vsmId);

}
