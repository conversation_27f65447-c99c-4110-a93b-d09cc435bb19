package tech.tongdao.ccsp.management.modules.swarm.controller;

import com.ccsp.gmmid.beans.form.Pager;
import com.ccsp.gmmid.beans.form.ServerResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import tech.tongdao.ccsp.management.modules.device.form.VsmForm;
import tech.tongdao.ccsp.management.modules.device.service.VsmService;
import tech.tongdao.ccsp.management.modules.swarm.form.*;
import tech.tongdao.ccsp.management.modules.swarm.service.ContainerDeviceResourceService;
import tech.tongdao.ccsp.management.modules.swarm.service.ContainerInstanceService;
import tech.tongdao.ccsp.management.modules.swarm.service.DockerImageService;
import tech.tongdao.ccsp.management.modules.swarm.service.SwarmManagementService;
import tech.tongdao.ccsp.management.modules.swarm.utils.SwarmPlacementHelper;
import tech.tongdao.ccsp.management.modules.swarm.utils.TraefikConfigHelper;
import tech.tongdao.ccsp.management.modules.swarm.utils.TraefikConfigHelper.TraefikConfig;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 容器实例管理控制器
 * 业务层控制器，提供容器实例的生命周期管理和业务操作
 * 底层调用SwarmManagementService进行Docker Swarm操作
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@RestController
@RequestMapping("/api-management/swarm/container/v1")
@Validated
public class ContainerInstanceController {

    private final Logger log = LoggerFactory.getLogger(ContainerInstanceController.class);

    private final ContainerInstanceService containerInstanceService;
    private final SwarmManagementService swarmManagementService;
    private final DockerImageService dockerImageService;
    private final VsmService vsmService;
    private final ContainerDeviceResourceService containerDeviceResourceService;

    public ContainerInstanceController(ContainerInstanceService containerInstanceService,
                                       SwarmManagementService swarmManagementService,
                                       DockerImageService dockerImageService,
                                       VsmService vsmService,
                                       ContainerDeviceResourceService containerDeviceResourceService) {
        this.containerInstanceService = containerInstanceService;
        this.swarmManagementService = swarmManagementService;
        this.dockerImageService = dockerImageService;
        this.vsmService = vsmService;
        this.containerDeviceResourceService = containerDeviceResourceService;
    }

    /**
     * 分页查询容器实例列表
     */
    @GetMapping("/list-page")
    @PreAuthorize("hasAnyRole('ROLE_ADMIN', 'ROLE_OPER')")
    public Object listPage(ContainerInstanceForm form) {
        log.info("查询容器实例列表，参数：{}", form);
        try {
            Pager<ContainerInstanceForm> result = containerInstanceService.listPage(form);
            return ServerResponse.success(result);
        } catch (Exception e) {
            log.error("查询容器实例列表失败", e);
            return ServerResponse.failed("查询容器实例列表失败：" + e.getMessage());
        }
    }

    /**
     * 根据ID查询容器实例详情
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasAnyRole('ROLE_ADMIN', 'ROLE_OPER')")
    public Object getById(@PathVariable Long id) {
        log.info("查询容器实例详情，ID：{}", id);
        try {
            ContainerInstanceForm result = containerInstanceService.getById(id);
            return ServerResponse.success(result);
        } catch (Exception e) {
            log.error("查询容器实例详情失败，ID：{}", id, e);
            return ServerResponse.failed("查询容器实例详情失败：" + e.getMessage());
        }
    }

    /**
     * 获取可用的HSM设备资源列表
     */
    @GetMapping("/hsm-devices")
    @PreAuthorize("hasAnyRole('ROLE_ADMIN', 'ROLE_OPER')")
    public Object getAvailableHsmDevices(
            @RequestParam(required = false) String status,
            @RequestParam(required = false) Long deviceGroupId) {
        log.info("获取可用的HSM设备资源列表，状态: {}, 设备组ID: {}",
                status, deviceGroupId);
        try {
            Pager<VsmForm> result;
            List<VsmForm> resultList = new ArrayList<>();

            if (status != null || deviceGroupId != null) {
                // 根据条件查询
                resultList = vsmService.listHsmDevicesByStatus(status, deviceGroupId);
            } else {
                // 查询所有可用设备
                resultList = vsmService.listAvailableHsmDevices();
            }

            // 转换为简化的返回格式
            List<Map<String, Object>> deviceList = resultList.stream()
                    .map(this::convertVsmToHsmDevice)
                    .collect(Collectors.toList());

            Map<String, Object> response = new HashMap<>();
            response.put("list", deviceList);
            response.put("total", resultList.size());

            return ServerResponse.success(response);
        } catch (Exception e) {
            log.error("获取HSM设备资源列表失败", e);
            return ServerResponse.failed("获取HSM设备资源列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取HSM设备详细信息
     */
    @GetMapping("/hsm-devices/{vsmId}")
    @PreAuthorize("hasAnyRole('ROLE_ADMIN', 'ROLE_OPER')")
    public Object getHsmDeviceDetail(@PathVariable Long vsmId) {
        log.info("获取HSM设备详细信息，设备ID: {}", vsmId);
        try {
            VsmForm device = vsmService.getHsmDeviceDetail(vsmId);
            Map<String, Object> deviceInfo = convertVsmToHsmDevice(device);
            return ServerResponse.success(deviceInfo);
        } catch (Exception e) {
            log.error("获取HSM设备详细信息失败，设备ID: {}", vsmId, e);
            return ServerResponse.failed("获取HSM设备详细信息失败：" + e.getMessage());
        }
    }

    /**
     * 部署容器（带HSM设备资源配置）
     */
    @PostMapping("/deploy-with-hsm")
    @PreAuthorize("hasAnyRole('ROLE_ADMIN', 'ROLE_OPER')")
    @CacheEvict(value = "container-instance-cache", allEntries = true)
    public Object deployContainerWithHsm(@RequestBody ContainerDeployForm deployForm) {
        log.info("部署容器（带HSM设备资源），参数：{}", deployForm);
        try {
            // 1. 获取镜像信息并自动填充
            if (deployForm.getImageId() != null) {
                DockerImageForm imageInfo = dockerImageService.getById(deployForm.getImageId());
                if (imageInfo == null) {
                    return ServerResponse.failed("指定的镜像不存在");
                }

                // 自动填充镜像名称和标签
                if (deployForm.getImageName() == null || deployForm.getImageName().trim().isEmpty() || "null".equals(deployForm.getImageName())) {
                    deployForm.setImageName(imageInfo.getName());
                }
                if (deployForm.getImageTag() == null || deployForm.getImageTag().trim().isEmpty() || "null".equals(deployForm.getImageTag())) {
                    deployForm.setImageTag(imageInfo.getTag());
                }
            }

            // 2. 处理HSM设备资源配置
            if (deployForm.getHsmDeviceConfig() != null) {
                // 验证HSM设备配置
                ContainerDeployForm.HsmDeviceConfig hsmConfig = deployForm.getHsmDeviceConfig();
                if (hsmConfig.getEncryptorId() != null) {
                    // 验证设备是否可用
                    VsmForm vsmDevice = vsmService.getHsmDeviceDetail(hsmConfig.getEncryptorId().longValue());
                    if (vsmDevice == null) {
                        return ServerResponse.failed("指定的HSM设备不存在");
                    }

                    if (!vsmService.validateHsmDeviceAvailability(vsmDevice.getId())) {
                        return ServerResponse.failed("指定的HSM设备不可用");
                    }
                    hsmConfig.setServerIpAddr(vsmDevice.getIp());
                    hsmConfig.setServerPort(vsmDevice.getManagementPort());
                    // 从设备信息中填充缺失的配置
                    if (hsmConfig.getEncryptorName() == null) {
                        hsmConfig.setEncryptorName(vsmDevice.getName());
                    }
                }

                Map<String, String> hsmEnvVars = generateHsmEnvironmentVars(hsmConfig);

                // 将HSM配置添加到环境变量中
                if (deployForm.getEnvironmentVars() == null) {
                    deployForm.setEnvironmentVars(new HashMap<>());
                }
                deployForm.getEnvironmentVars().putAll(hsmEnvVars);

                log.info("已添加HSM环境变量：{}", hsmEnvVars.keySet());
            }

            // 3. 调用标准部署方法
            ContainerInstanceForm result = containerInstanceService.deployContainer(deployForm);

            // 4. 返回结果并包含HSM配置信息
            Map<String, Object> response = new HashMap<>();
            response.put("container", result);
            response.put("hsmConfigured", deployForm.getHsmDeviceConfig() != null);
            if (deployForm.getHsmDeviceConfig() != null) {
                response.put("hsmDeviceConfig", deployForm.getHsmDeviceConfig());
            }

            return ServerResponse.success(response);
        } catch (Exception e) {
            log.error("部署容器失败", e);
            return ServerResponse.failed("部署容器失败：" + e.getMessage());
        }
    }

    /**
     * 部署容器（带分布策略）
     */
    @PostMapping("/deploy-with-strategy")
    @PreAuthorize("hasAnyRole('ROLE_ADMIN', 'ROLE_OPER')")
    @CacheEvict(value = "container-instance-cache", allEntries = true)
    public Object deployContainerWithStrategy(@RequestBody ContainerDeployWithStrategyForm deployForm) {
        log.info("部署容器（带分布策略），参数：{}", deployForm);
        try {
            // 1. 获取镜像信息并自动填充
            if (deployForm.getImageId() != null) {
                DockerImageForm imageInfo = dockerImageService.getById(deployForm.getImageId());
                if (imageInfo == null) {
                    return ServerResponse.failed("指定的镜像不存在");
                }

                // 自动填充镜像名称和标签
                if (deployForm.getImageName() == null || deployForm.getImageName().trim().isEmpty() || "null".equals(deployForm.getImageName())) {
                    deployForm.setImageName(imageInfo.getName());
                }
                if (deployForm.getImageTag() == null || deployForm.getImageTag().trim().isEmpty() || "null".equals(deployForm.getImageTag())) {
                    deployForm.setImageTag(imageInfo.getTag());
                }
            }

            // 2. 验证基本部署配置
            if (!deployForm.isValidDeployConfig()) {
                return ServerResponse.failed("部署配置无效");
            }

            // 3. 根据分布策略生成约束条件
            if (deployForm.getDistributionStrategy() != null) {
                List<String> strategyConstraints = SwarmPlacementHelper.generateDistributionConstraints(
                        deployForm.getReplicas() != null ? deployForm.getReplicas() : 1,
                        deployForm.getDistributionStrategy());

                // 合并约束条件
                List<String> existingConstraints = deployForm.getConstraints();
                List<String> mergedConstraints = SwarmPlacementHelper.mergeConstraints(
                        existingConstraints, strategyConstraints);
                deployForm.setConstraints(mergedConstraints);

                log.info("应用分布策略 {}，生成约束：{}",
                        deployForm.getDistributionStrategy(), strategyConstraints);
            }

            // 4. 调用标准部署方法
            ContainerInstanceForm result = containerInstanceService.deployContainer(deployForm);

            // 5. 返回结果并包含分布策略信息
            Map<String, Object> response = new HashMap<>();
            response.put("container", result);
            response.put("distributionStrategy", deployForm.getDistributionStrategy());
            response.put("appliedConstraints", deployForm.getConstraints());

            return ServerResponse.success(response);
        } catch (Exception e) {
            log.error("部署容器失败", e);
            return ServerResponse.failed("部署容器失败：" + e.getMessage());
        }
    }

    /**
     * 获取推荐的分布策略
     */
    @GetMapping("/recommended-strategy")
    @PreAuthorize("hasAnyRole('ROLE_ADMIN', 'ROLE_OPER')")
    public Object getRecommendedStrategy(@RequestParam Integer replicas,
                                         @RequestParam(required = false) Integer availableNodes) {
        log.info("获取推荐的分布策略，副本数：{}，可用节点数：{}", replicas, availableNodes);
        try {
            // 如果没有提供可用节点数，尝试从服务获取
            if (availableNodes == null) {
                try {
                    // 这里可以调用 SwarmNodeService 获取可用节点数
                    // availableNodes = swarmNodeService.getAvailableNodeCount();
                    availableNodes = 3; // 默认假设值
                } catch (Exception e) {
                    log.warn("获取可用节点数失败，使用默认值", e);
                    availableNodes = 3;
                }
            }

            SwarmPlacementHelper.DistributionStrategy recommended =
                    SwarmPlacementHelper.getRecommendedStrategy(replicas, availableNodes);

            Map<String, Object> result = new HashMap<>();
            result.put("recommendedStrategy", recommended);
            result.put("replicas", replicas);
            result.put("availableNodes", availableNodes);
            result.put("allStrategies", SwarmPlacementHelper.DistributionStrategy.values());

            return ServerResponse.success(result);
        } catch (Exception e) {
            log.error("获取推荐分布策略失败", e);
            return ServerResponse.failed("获取推荐分布策略失败：" + e.getMessage());
        }
    }

    /**
     * 启动容器
     */
    @PostMapping("/start")
    @PreAuthorize("hasAnyRole('ROLE_ADMIN', 'ROLE_OPER')")
    @CacheEvict(value = "container-instance-cache", allEntries = true)
    public Object startContainer(@RequestParam Long id) {
        log.info("启动容器，ID：{}", id);
        try {
            containerInstanceService.startContainer(id);
            return ServerResponse.success(true);
        } catch (Exception e) {
            log.error("启动容器失败，ID：{}", id, e);
            return ServerResponse.failed("启动容器失败：" + e.getMessage());
        }
    }

    /**
     * 停止容器
     */
    @PostMapping("/stop")
    @PreAuthorize("hasAnyRole('ROLE_ADMIN', 'ROLE_OPER')")
    @CacheEvict(value = "container-instance-cache", allEntries = true)
    public Object stopContainer(@RequestParam Long id) {
        log.info("停止容器，ID：{}", id);
        try {
            containerInstanceService.stopContainer(id);
            return ServerResponse.success(true);
        } catch (Exception e) {
            log.error("停止容器失败，ID：{}", id, e);
            return ServerResponse.failed("停止容器失败：" + e.getMessage());
        }
    }

    /**
     * 重启容器
     */
    @PostMapping("/restart")
    @PreAuthorize("hasAnyRole('ROLE_ADMIN', 'ROLE_OPER')")
    @CacheEvict(value = "container-instance-cache", allEntries = true)
    public Object restartContainer(@RequestParam Long id) {
        log.info("重启容器，ID：{}", id);
        try {
            containerInstanceService.restartContainer(id);
            return ServerResponse.success(true);
        } catch (Exception e) {
            log.error("重启容器失败，ID：{}", id, e);
            return ServerResponse.failed("重启容器失败：" + e.getMessage());
        }
    }

    /**
     * 删除容器
     */
    @PostMapping("/delete")
    @PreAuthorize("hasAnyRole('ROLE_ADMIN', 'ROLE_OPER')")
    @CacheEvict(value = "container-instance-cache", allEntries = true)
    public Object deleteContainer(@RequestParam Long id) {
        log.info("删除容器，ID：{}", id);
        try {
            containerInstanceService.deleteContainer(id);
            return ServerResponse.success(true);
        } catch (Exception e) {
            log.error("删除容器失败，ID：{}", id, e);
            return ServerResponse.failed("删除容器失败：" + e.getMessage());
        }
    }

    /**
     * 同步容器状态
     */
    @PostMapping("/sync-status")
    @PreAuthorize("hasAnyRole('ROLE_ADMIN', 'ROLE_OPER')")
    @CacheEvict(value = "container-instance-cache", allEntries = true)
    public Object syncContainerStatus() {
        log.info("同步容器状态");
        try {
            containerInstanceService.syncContainerStatus();
            return ServerResponse.success(true);
        } catch (Exception e) {
            log.error("同步容器状态失败", e);
            return ServerResponse.failed("同步容器状态失败：" + e.getMessage());
        }
    }

    /**
     * 根据镜像ID查询容器列表
     */
    @GetMapping("/by-image")
    @PreAuthorize("hasAnyRole('ROLE_ADMIN', 'ROLE_OPER')")
    public Object getContainersByImage(@RequestParam Long imageId) {
        log.info("根据镜像ID查询容器列表，镜像ID：{}", imageId);
        try {
            List<ContainerInstanceForm> result = containerInstanceService.getContainersByImage(imageId);
            return ServerResponse.success(result);
        } catch (Exception e) {
            log.error("根据镜像ID查询容器列表失败，镜像ID：{}", imageId, e);
            return ServerResponse.failed("根据镜像ID查询容器列表失败：" + e.getMessage());
        }
    }

    /**
     * 根据节点ID查询容器列表
     */
    @GetMapping("/by-node")
    @PreAuthorize("hasAnyRole('ROLE_ADMIN', 'ROLE_OPER')")
    public Object getContainersByNode(@RequestParam Long nodeId) {
        log.info("根据节点ID查询容器列表，节点ID：{}", nodeId);
        try {
            List<ContainerInstanceForm> result = containerInstanceService.getContainersByNode(nodeId);
            return ServerResponse.success(result);
        } catch (Exception e) {
            log.error("根据节点ID查询容器列表失败，节点ID：{}", nodeId, e);
            return ServerResponse.failed("根据节点ID查询容器列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取运行中的容器列表
     */
    @GetMapping("/running")
    @PreAuthorize("hasAnyRole('ROLE_ADMIN', 'ROLE_OPER')")
    public Object getRunningContainers() {
        log.info("获取运行中的容器列表");
        try {
            // 创建查询条件，只查询运行中的容器
            ContainerInstanceForm queryForm = new ContainerInstanceForm();
            queryForm.setStatus("running");

            Pager<ContainerInstanceForm> pager = containerInstanceService.listPage(queryForm);
            return ServerResponse.success(pager.getList());
        } catch (Exception e) {
            log.error("获取运行中的容器列表失败", e);
            return ServerResponse.failed("获取运行中的容器列表失败：" + e.getMessage());
        }
    }

    /**
     * 扩缩容操作
     * 通过底层SwarmManagementService调用Docker Swarm API
     */
    @PostMapping("/scale")
    @PreAuthorize("hasAnyRole('ROLE_ADMIN', 'ROLE_OPER')")
    @CacheEvict(value = "container-instance-cache", allEntries = true)
    public Object scaleContainer(@RequestParam Long id,
                                 @RequestParam Integer replicas) {
        log.info("扩缩容操作，容器ID：{}，副本数：{}", id, replicas);
        try {
            // 1. 根据容器ID获取服务信息
            ContainerInstanceForm container = containerInstanceService.getById(id);
            if (container == null || container.getServiceId() == null) {
                return ServerResponse.failed("容器不存在或服务ID为空");
            }

            // 2. 调用底层Swarm服务进行扩缩容
            String serviceId = container.getServiceId();
            swarmManagementService.scaleService(serviceId, replicas);

            // 3. 扩缩容成功后，可以选择同步容器状态
            containerInstanceService.syncContainerStatus();

            log.info("扩缩容操作成功，容器ID：{}，副本数：{}", id, replicas);
            return ServerResponse.success(true);
        } catch (Exception e) {
            log.error("扩缩容操作失败，容器ID：{}，副本数：{}", id, replicas, e);
            return ServerResponse.failed("扩缩容操作失败：" + e.getMessage());
        }
    }

    /**
     * 更新容器镜像
     * 通过底层SwarmManagementService调用Docker Swarm API
     */
    @PostMapping("/update-image")
    @PreAuthorize("hasAnyRole('ROLE_ADMIN', 'ROLE_OPER')")
    @CacheEvict(value = "container-instance-cache", allEntries = true)
    public Object updateContainerImage(@RequestParam Long id,
                                       @RequestParam Long newImageId) {
        log.info("更新容器镜像，容器ID：{}，新镜像ID：{}", id, newImageId);
        try {
            // 1. 获取容器信息
            ContainerInstanceForm container = containerInstanceService.getById(id);
            if (container == null || container.getServiceId() == null) {
                return ServerResponse.failed("容器不存在或服务ID为空");
            }

            // 2. 通过底层服务更新镜像（这里需要实现具体的更新逻辑）
            String serviceId = container.getServiceId();
            // 暂时记录操作日志，具体实现需要扩展服务层
            log.info("准备更新容器镜像，服务ID：{}，新镜像ID：{}", serviceId, newImageId);

            log.info("更新容器镜像成功，容器ID：{}，新镜像ID：{}", id, newImageId);
            return ServerResponse.success(true);
        } catch (Exception e) {
            log.error("更新容器镜像失败，容器ID：{}，新镜像ID：{}", id, newImageId, e);
            return ServerResponse.failed("更新容器镜像失败：" + e.getMessage());
        }
    }

    /**
     * 获取容器日志
     * 从SwarmController迁移的功能，通过底层SwarmManagementService获取服务日志
     */
    @GetMapping("/logs")
    @PreAuthorize("hasAnyRole('ROLE_ADMIN', 'ROLE_OPER')")
    public Object getContainerLogs(@RequestParam Long id,
                                   @RequestParam(defaultValue = "100") Integer lines) {
        log.info("获取容器日志，容器ID：{}，行数：{}", id, lines);
        try {
            // 1. 根据容器ID获取服务信息
            ContainerInstanceForm container = containerInstanceService.getById(id);
            if (container == null || container.getServiceId() == null) {
                return ServerResponse.failed("容器不存在或服务ID为空");
            }

            // 2. 调用底层Swarm服务获取日志
            String serviceId = container.getServiceId();
            String logs = swarmManagementService.getServiceLogs(serviceId, lines);

            log.info("获取容器日志成功，容器ID：{}，日志行数：{}", id, lines);
            return ServerResponse.success(logs);
        } catch (Exception e) {
            log.error("获取容器日志失败，容器ID：{}", id, e);
            return ServerResponse.failed("获取容器日志失败：" + e.getMessage());
        }
    }

    /**
     * 部署容器并配置Traefik路由
     */
    @PostMapping("/deploy-with-traefik")
    @PreAuthorize("hasAnyRole('ROLE_ADMIN', 'ROLE_OPER')")
    @CacheEvict(value = "container-instance-cache", allEntries = true)
    public Object deployContainerWithTraefik(@RequestBody ContainerDeployWithTraefikForm deployForm) {
        log.info("部署容器并配置Traefik，参数：{}", deployForm);
        try {
            // 1. 验证Traefik配置
            if (deployForm.getTraefikConfig() != null && !TraefikConfigHelper.isValidConfig(deployForm.getTraefikConfig())) {
                return ServerResponse.failed("Traefik配置无效");
            }

            // 2. 调用部署服务
            ContainerInstanceForm result = containerInstanceService.deployContainer(deployForm);

            // 3. 如果启用了Traefik，生成访问地址
            if (deployForm.getTraefikConfig() != null && Boolean.TRUE.equals(deployForm.getTraefikConfig().getEnabled())) {
                String accessUrl = TraefikConfigHelper.generateAccessUrl(deployForm.getTraefikConfig());
                result.setAccessUrl(accessUrl);
                result.setAccessDomain(deployForm.getTraefikConfig().getDomain());
                result.setTraefikEnabled(true);
                result.setTraefikStatus("enabled");

                log.info("容器部署成功，访问地址：{}", accessUrl);
            }

            return ServerResponse.success(result);
        } catch (Exception e) {
            log.error("部署容器失败", e);
            return ServerResponse.failed("部署容器失败：" + e.getMessage());
        }
    }

    /**
     * 获取容器访问地址
     */
    @GetMapping("/{id}/access-url")
    @PreAuthorize("hasAnyRole('ROLE_ADMIN', 'ROLE_OPER')")
    public Object getContainerAccessUrl(@PathVariable Long id) {
        log.info("获取容器访问地址，ID：{}", id);
        try {
            ContainerInstanceForm container = containerInstanceService.getById(id);
            if (container == null) {
                return ServerResponse.failed("容器不存在");
            }

            // 从 Docker Swarm 获取服务标签并解析访问地址
            String accessUrl = extractAccessUrlFromContainer(container);

            Map<String, Object> result = Map.of(
                    "containerId", container.getId(),
                    "containerName", container.getContainerName(),
                    "accessUrl", accessUrl != null ? accessUrl : "",
                    "traefikEnabled", accessUrl != null
            );

            return ServerResponse.success(result);
        } catch (Exception e) {
            log.error("获取容器访问地址失败，ID：{}", id, e);
            return ServerResponse.failed("获取访问地址失败：" + e.getMessage());
        }
    }

    /**
     * 配置容器Traefik路由
     */
    @PostMapping("/{id}/configure-traefik")
    @PreAuthorize("hasAnyRole('ROLE_ADMIN', 'ROLE_OPER')")
    @CacheEvict(value = "container-instance-cache", allEntries = true)
    public Object configureTraefik(@PathVariable Long id, @RequestBody TraefikConfig traefikConfig) {
        log.info("配置Traefik路由，容器ID：{}，配置：{}", id, traefikConfig);
        try {
            // 1. 验证配置
            if (!TraefikConfigHelper.isValidConfig(traefikConfig)) {
                return ServerResponse.failed("Traefik配置无效");
            }

            // 2. 获取容器信息
            ContainerInstanceForm container = containerInstanceService.getById(id);
            if (container == null || container.getServiceId() == null) {
                return ServerResponse.failed("容器不存在或服务ID为空");
            }

            // 3. 生成Traefik标签
            Map<String, String> traefikLabels = TraefikConfigHelper.generateTraefikLabels(container.getContainerName(), traefikConfig);

            // 4. 更新Docker Swarm服务标签
            String serviceId = container.getServiceId();
            swarmManagementService.updateServiceLabels(serviceId, traefikLabels);

            // 5. 生成访问地址
            String accessUrl = TraefikConfigHelper.generateAccessUrl(traefikConfig);

            Map<String, Object> result = Map.of(
                    "containerId", id,
                    "serviceId", serviceId,
                    "accessUrl", accessUrl != null ? accessUrl : "",
                    "traefikEnabled", Boolean.TRUE.equals(traefikConfig.getEnabled()),
                    "labelsCount", traefikLabels.size()
            );

            log.info("Traefik配置成功，容器ID：{}，访问地址：{}", id, accessUrl);
            return ServerResponse.success(result);
        } catch (Exception e) {
            log.error("Traefik配置失败，容器ID：{}", id, e);
            return ServerResponse.failed("Traefik配置失败：" + e.getMessage());
        }
    }

    /**
     * 生成默认Traefik配置
     */
    @PostMapping("/generate-traefik-config")
    @PreAuthorize("hasAnyRole('ROLE_ADMIN', 'ROLE_OPER')")
    public Object generateDefaultTraefikConfig(@RequestBody Map<String, Object> params) {
        try {
            String containerName = (String) params.get("containerName");
            Integer servicePort = (Integer) params.get("servicePort");
            String customDomain = (String) params.get("domain");

            if (containerName == null || servicePort == null) {
                return ServerResponse.failed("容器名称和服务端口为必填参数");
            }

            TraefikConfig config;
            if (customDomain != null) {
                config = TraefikConfigHelper.createSimpleConfig(customDomain, servicePort);
            } else {
                config = TraefikConfigHelper.createDefaultConfig(containerName, servicePort);
            }

            String accessUrl = TraefikConfigHelper.generateAccessUrl(config);

            Map<String, Object> result = Map.of(
                    "traefikConfig", config,
                    "accessUrl", accessUrl != null ? accessUrl : "",
                    "generatedDomain", config.getDomain()
            );

            return ServerResponse.success(result);
        } catch (Exception e) {
            log.error("生成Traefik配置失败", e);
            return ServerResponse.failed("生成Traefik配置失败：" + e.getMessage());
        }
    }

    // --- 私有辅助方法 ---

    /**
     * 从容器配置中提取访问地址
     */
    private String extractAccessUrlFromContainer(ContainerInstanceForm container) {
        if (container.getServiceId() == null) {
            return null;
        }

        try {
            // 从 Docker Swarm 获取服务标签
            String serviceId = container.getServiceId();
            Map<String, String> serviceLabels = swarmManagementService.getServiceLabels(serviceId);

            // 检查是否启用了Traefik
            boolean traefikEnabled = "true".equals(serviceLabels.get("traefik.enable"));
            if (!traefikEnabled) {
                return null;
            }

            // 提取域名
            String domain = extractDomainFromLabels(serviceLabels);
            if (domain == null) {
                return null;
            }

            // 判断是否启用HTTPS
            String routerName = container.getContainerName().toLowerCase().replaceAll("[^a-z0-9-]", "-");
            boolean httpsEnabled = serviceLabels.containsKey(String.format("traefik.http.routers.%s.tls", routerName));

            return (httpsEnabled ? "https://" : "http://") + domain;
        } catch (Exception e) {
            log.warn("提取容器访问地址失败，容器ID：{}", container.getId(), e);
            return null;
        }
    }

    /**
     * 从服务标签中提取域名
     */
    private String extractDomainFromLabels(Map<String, String> labels) {
        // 查找 Host 规则中的域名
        for (Map.Entry<String, String> entry : labels.entrySet()) {
            if (entry.getKey().contains(".rule") && entry.getValue().contains("Host(")) {
                String rule = entry.getValue();
                // 从 Host(`domain`) 中提取域名
                int start = rule.indexOf("Host(`") + 6;
                int end = rule.indexOf("`)", start);
                if (start > 5 && end > start) {
                    return rule.substring(start, end);
                }
            }
        }
        return null;
    }

    /**
     * 获取可用的设备资源列表
     */
    @GetMapping("/device-resources")
    @PreAuthorize("hasAnyRole('ROLE_ADMIN', 'ROLE_OPER')")
    public Object getAvailableDeviceResources(@RequestParam(required = false) String deviceResourceType,
                                              @RequestParam(required = false) Long excludeContainerId) {
        log.info("获取可用设备资源列表，类型：{}，排除容器：{}", deviceResourceType, excludeContainerId);
        try {
            List<DeviceResourceForm> resources = containerDeviceResourceService.getAvailableDeviceResources(
                    deviceResourceType, excludeContainerId);
            return ServerResponse.success(resources);
        } catch (Exception e) {
            log.error("获取可用设备资源列表失败", e);
            return ServerResponse.failed("获取可用设备资源列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取容器关联的设备资源列表
     */
    @GetMapping("/{id}/device-resources")
    @PreAuthorize("hasAnyRole('ROLE_ADMIN', 'ROLE_OPER')")
    public Object getContainerDeviceResources(@PathVariable Long id) {
        log.info("获取容器关联的设备资源列表，容器ID：{}", id);
        try {
            List<ContainerDeviceResourceRelForm> resources = containerDeviceResourceService.getContainerDeviceResources(id);
            return ServerResponse.success(resources);
        } catch (Exception e) {
            log.error("获取容器设备资源关联失败", e);
            return ServerResponse.failed("获取容器设备资源关联失败：" + e.getMessage());
        }
    }

    /**
     * 获取设备资源使用统计
     */
    @GetMapping("/device-resources/{deviceResourceId}/stats")
    @PreAuthorize("hasAnyRole('ROLE_ADMIN', 'ROLE_OPER')")
    public Object getDeviceResourceUsageStats(@PathVariable Long deviceResourceId,
                                              @RequestParam String deviceResourceType) {
        log.info("获取设备资源使用统计，设备ID：{}，类型：{}", deviceResourceId, deviceResourceType);
        try {
            Map<String, Object> stats = containerDeviceResourceService.getDeviceResourceUsageStats(
                    deviceResourceId, deviceResourceType);
            return ServerResponse.success(stats);
        } catch (Exception e) {
            log.error("获取设备资源使用统计失败", e);
            return ServerResponse.failed("获取设备资源使用统计失败：" + e.getMessage());
        }
    }

    /**
     * 获取推荐的设备资源
     */
    @GetMapping("/device-resources/recommend")
    @PreAuthorize("hasAnyRole('ROLE_ADMIN', 'ROLE_OPER')")
    public Object getRecommendedDeviceResources(@RequestParam Long applicationId,
                                                @RequestParam String deviceResourceType,
                                                @RequestParam(defaultValue = "1") Integer requiredCount) {
        log.info("获取推荐设备资源，应用ID：{}，类型：{}，数量：{}", applicationId, deviceResourceType, requiredCount);
        try {
            List<DeviceResourceForm> resources = containerDeviceResourceService.getRecommendedDeviceResources(
                    applicationId, deviceResourceType, requiredCount);
            return ServerResponse.success(resources);
        } catch (Exception e) {
            log.error("获取推荐设备资源失败", e);
            return ServerResponse.failed("获取推荐设备资源失败：" + e.getMessage());
        }
    }

    /**
     * 获取HSM设备资源列表
     * 此处模拟返回，实际中可以从数据库或配置中获取
     */
    private List<Map<String, Object>> getHsmDeviceList() {
        List<Map<String, Object>> devices = new ArrayList<>();

        // 设备组1
        Map<String, Object> device1 = new HashMap<>();
        device1.put("groupId", 1);
        device1.put("deviceId", 1);
        device1.put("deviceName", "gmmid_vsm-1");
        device1.put("ipAddress", "************");
        device1.put("port", 8018);
        device1.put("tcpConnNum", 5);
        device1.put("msgHeadLen", 4);
        device1.put("msgTailLen", 0);
        device1.put("encoding", 0); // 0-ASCII
        device1.put("status", "available");
        device1.put("description", "主用gmmid_vsm设备");
        devices.add(device1);

        // 设备组2
        Map<String, Object> device2 = new HashMap<>();
        device2.put("groupId", 1);
        device2.put("deviceId", 2);
        device2.put("deviceName", "gmmid_vsm-2");
        device2.put("ipAddress", "************");
        device2.put("port", 8018);
        device2.put("tcpConnNum", 5);
        device2.put("msgHeadLen", 4);
        device2.put("msgTailLen", 0);
        device2.put("encoding", 0); // 0-ASCII
        device2.put("status", "available");
        device2.put("description", "备用gmmid_vsm设备");
        devices.add(device2);

        return devices;
    }

    /**
     * 将VsmForm转换为HSM设备格式
     */
    private Map<String, Object> convertVsmToHsmDevice(VsmForm vsm) {
        Map<String, Object> device = new HashMap<>();

        device.put("deviceId", vsm.getId());
        device.put("deviceName", vsm.getName());
        device.put("deviceCode", vsm.getCode());
        device.put("ipAddress", vsm.getIp());
        device.put("managementPort", vsm.getManagementPort());
        device.put("servicePort", vsm.getServicePort());
        device.put("status", vsm.getStatus() != null ? vsm.getStatus() : "available");
        device.put("description", vsm.getRemark());
        device.put("createTime", vsm.getCreateTime());

        // 设备组信息
        device.put("deviceGroupId", vsm.getDeviceGroupId());
        device.put("deviceGroupName", vsm.getDeviceGroupName());

        // 云密码机信息
        device.put("chsmId", vsm.getChsmId());
        device.put("chsmName", vsm.getChsmName());
        device.put("chsmIp", vsm.getChsmIp());
        device.put("chsmPort", vsm.getChsmPort());

        // 租户信息
        device.put("tenantId", vsm.getTenantId());
        device.put("tenantName", vsm.getTenantName());

        // 网络配置
        device.put("mask", vsm.getMask());
        device.put("gateway", vsm.getGateway());

        // HSM相关的默认配置
        device.put("tcpConnNum", 5);
        device.put("msgHeadLen", 4);
        device.put("msgTailLen", 0);
        device.put("encoding", 0); // 0-ASCII

        return device;
    }

    /**
     * 生成HSM环境变量
     * 将HSM设备配置转换为环境变量，用于在容器启动时生成DJHsmAPI.ini文件
     */
    private Map<String, String> generateHsmEnvironmentVars(ContainerDeployForm.HsmDeviceConfig hsmConfig) {
        Map<String, String> envVars = new HashMap<>();

        if (hsmConfig == null) {
            return envVars;
        }

        // HSM基本配置
        envVars.put("HSM_ENABLED", "true");

        // 加密机组配置
        if (hsmConfig.getEncryptorGroupId() != null) {
            envVars.put("HSM_ENCRYPTOR_GROUP_NUM", "1");
            envVars.put("HSM_ENCRYPTOR_GROUP_ID", hsmConfig.getEncryptorGroupId().toString());
        }

        // 加密机设备配置
        if (hsmConfig.getEncryptorId() != null) {
            envVars.put("HSM_ENCRYPTOR_NUM", "1");
            envVars.put("HSM_ENCRYPTOR_ID", hsmConfig.getEncryptorId().toString());
        }

        if (hsmConfig.getEncryptorName() != null) {
            envVars.put("HSM_ENCRYPTOR_NAME", hsmConfig.getEncryptorName());
        }

        if (hsmConfig.getServerIpAddr() != null) {
            envVars.put("HSM_SERVER_IP", hsmConfig.getServerIpAddr());
        }

        if (hsmConfig.getServerPort() != null) {
            envVars.put("HSM_SERVER_PORT", hsmConfig.getServerPort().toString());
        }

        if (hsmConfig.getTcpConnNum() != null) {
            envVars.put("HSM_TCP_CONN_NUM", hsmConfig.getTcpConnNum().toString());
        }

        if (hsmConfig.getMsgHeadLen() != null) {
            envVars.put("HSM_MSG_HEAD_LEN", hsmConfig.getMsgHeadLen().toString());
        }

        if (hsmConfig.getMsgTailLen() != null) {
            envVars.put("HSM_MSG_TAIL_LEN", hsmConfig.getMsgTailLen().toString());
        }

        if (hsmConfig.getAsciiOrEbcdic() != null) {
            envVars.put("HSM_ASCII_OR_EBCDIC", hsmConfig.getAsciiOrEbcdic().toString());
        }

        if (hsmConfig.getDynamicLibPath() != null) {
            envVars.put("HSM_DYNAMIC_LIB_PATH", hsmConfig.getDynamicLibPath());
        } else {
            // 默认动态库路径
            envVars.put("HSM_DYNAMIC_LIB_PATH", "./libdeviceapi.so");
        }

        log.info("生成HSM环境变量：{}", envVars);
        return envVars;
    }

    /**
     * 带Traefik配置的部署表单
     */
    public static class ContainerDeployWithTraefikForm extends ContainerDeployForm {
        private TraefikConfig traefikConfig;

        public TraefikConfig getTraefikConfig() {
            return traefikConfig;
        }

        public void setTraefikConfig(TraefikConfig traefikConfig) {
            this.traefikConfig = traefikConfig;
        }
    }

    /**
     * 带分布策略的部署表单
     */
    public static class ContainerDeployWithStrategyForm extends ContainerDeployForm {
        private SwarmPlacementHelper.DistributionStrategy distributionStrategy;

        public SwarmPlacementHelper.DistributionStrategy getDistributionStrategy() {
            return distributionStrategy;
        }

        public void setDistributionStrategy(SwarmPlacementHelper.DistributionStrategy distributionStrategy) {
            this.distributionStrategy = distributionStrategy;
        }

        @Override
        public String toString() {
            return "ContainerDeployWithStrategyForm{" +
                    "distributionStrategy=" + distributionStrategy +
                    ", " + super.toString() +
                    '}';
        }
    }
}