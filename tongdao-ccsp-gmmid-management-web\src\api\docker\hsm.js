import request from '@/utils/request'

/**
 * 获取可用的HSM设备资源列表
 * @param {Object} params 查询参数
 * @param {number} params.page 页码
 * @param {number} params.pageSize 页大小
 * @param {string} params.status 设备状态
 * @param {number} params.deviceGroupId 设备组ID
 */
export function getAvailableHsmDevices(params) {
  return request({
    url: '/api-management/swarm/container/v1/hsm-devices',
    method: 'get',
    params
  })
}

/**
 * 获取HSM设备详细信息
 * @param {number} vsmId 设备ID
 */
export function getHsmDeviceDetail(vsmId) {
  return request({
    url: `/api-management/swarm/container/v1/hsm-devices/${vsmId}`,
    method: 'get'
  })
}

/**
 * 部署容器（带HSM配置）
 * @deprecated 请使用 container.js 中的统一 deployContainer 方法
 * @param {Object} data 部署数据
 */
export function deployContainerWithHsm(data) {
  console.warn('deployContainerWithHsm 已弃用，请使用统一的 deployContainer')
  // 导入并调用统一的部署方法
  import('./container').then(containerApi => {
    return containerApi.deployContainer(data)
  })
}