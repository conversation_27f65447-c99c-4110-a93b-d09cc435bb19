package tech.tongdao.ccsp.management.modules.swarm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import tech.tongdao.ccsp.management.modules.swarm.entity.DeviceResourceUsageStatsEntity;

import java.util.List;
import java.util.Map;

/**
 * 设备资源使用统计Mapper接口
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Mapper
public interface DeviceResourceUsageStatsMapper extends BaseMapper<DeviceResourceUsageStatsEntity> {

    /**
     * 查询可用的设备资源统计信息
     *
     * @param deviceResourceType 设备资源类型
     * @param minAvailableCapacity 最小可用容量
     * @return 可用设备资源统计列表
     */
    @Select("SELECT * FROM gmmid_device_resource_usage_stats " +
            "WHERE device_resource_type = #{deviceResourceType} " +
            "AND available_capacity >= #{minAvailableCapacity} " +
            "ORDER BY available_capacity DESC, last_allocated_at ASC")
    List<DeviceResourceUsageStatsEntity> selectAvailableDeviceResources(@Param("deviceResourceType") String deviceResourceType,
                                                                       @Param("minAvailableCapacity") Integer minAvailableCapacity);

    /**
     * 批量更新设备资源使用统计
     *
     * @param deviceResourceId 设备资源ID
     * @param deviceResourceType 设备资源类型
     * @param usedCapacity 已使用容量
     * @param availableCapacity 可用容量
     * @param activeContainersCount 活跃容器数量
     * @return 更新行数
     */
    @Update("UPDATE gmmid_device_resource_usage_stats " +
            "SET used_capacity = #{usedCapacity}, " +
            "available_capacity = #{availableCapacity}, " +
            "active_containers_count = #{activeContainersCount}, " +
            "update_time = NOW() " +
            "WHERE device_resource_id = #{deviceResourceId} " +
            "AND device_resource_type = #{deviceResourceType}")
    int updateUsageStats(@Param("deviceResourceId") Long deviceResourceId,
                        @Param("deviceResourceType") String deviceResourceType,
                        @Param("usedCapacity") Integer usedCapacity,
                        @Param("availableCapacity") Integer availableCapacity,
                        @Param("activeContainersCount") Integer activeContainersCount);

    /**
     * 查询设备资源类型的统计汇总
     *
     * @param deviceResourceType 设备资源类型
     * @return 统计汇总信息
     */
    @Select("SELECT " +
            "device_resource_type, " +
            "COUNT(*) as total_devices, " +
            "SUM(total_capacity) as total_capacity, " +
            "SUM(used_capacity) as total_used_capacity, " +
            "SUM(available_capacity) as total_available_capacity, " +
            "SUM(active_containers_count) as total_active_containers " +
            "FROM gmmid_device_resource_usage_stats " +
            "WHERE device_resource_type = #{deviceResourceType} " +
            "GROUP BY device_resource_type")
    Map<String, Object> selectDeviceResourceTypeSummary(@Param("deviceResourceType") String deviceResourceType);

    /**
     * 查询所有设备资源类型的统计汇总
     *
     * @return 所有设备资源类型的统计汇总列表
     */
    @Select("SELECT " +
            "device_resource_type, " +
            "COUNT(*) as total_devices, " +
            "SUM(total_capacity) as total_capacity, " +
            "SUM(used_capacity) as total_used_capacity, " +
            "SUM(available_capacity) as total_available_capacity, " +
            "SUM(active_containers_count) as total_active_containers " +
            "FROM gmmid_device_resource_usage_stats " +
            "GROUP BY device_resource_type")
    List<Map<String, Object>> selectAllDeviceResourceTypeSummary();

    /**
     * 查询使用率最高的设备资源
     *
     * @param deviceResourceType 设备资源类型
     * @param limit 限制数量
     * @return 使用率最高的设备资源列表
     */
    @Select("SELECT *, " +
            "CASE WHEN total_capacity > 0 THEN (used_capacity * 100.0 / total_capacity) ELSE 0 END as usage_rate " +
            "FROM gmmid_device_resource_usage_stats " +
            "WHERE device_resource_type = #{deviceResourceType} " +
            "ORDER BY usage_rate DESC " +
            "LIMIT #{limit}")
    List<Map<String, Object>> selectTopUsedDeviceResources(@Param("deviceResourceType") String deviceResourceType,
                                                          @Param("limit") Integer limit);

    /**
     * 重置设备资源使用统计
     *
     * @param deviceResourceId 设备资源ID
     * @param deviceResourceType 设备资源类型
     * @return 更新行数
     */
    @Update("UPDATE gmmid_device_resource_usage_stats " +
            "SET used_capacity = 0, " +
            "available_capacity = total_capacity, " +
            "active_containers_count = 0, " +
            "last_released_at = NOW(), " +
            "update_time = NOW() " +
            "WHERE device_resource_id = #{deviceResourceId} " +
            "AND device_resource_type = #{deviceResourceType}")
    int resetUsageStats(@Param("deviceResourceId") Long deviceResourceId,
                       @Param("deviceResourceType") String deviceResourceType);

    /**
     * 使用行锁查询设备资源统计（防止并发问题）
     */
    @Select("SELECT * FROM gmmid_device_resource_usage_stats " +
            "WHERE device_resource_id = #{deviceResourceId} " +
            "AND device_resource_type = #{deviceResourceType} " +
            "FOR UPDATE")
    DeviceResourceUsageStatsEntity selectForUpdate(@Param("deviceResourceId") Long deviceResourceId,
                                                  @Param("deviceResourceType") String deviceResourceType);

    /**
     * UPSERT操作：插入或更新设备资源使用统计
     */
    @Update("INSERT INTO gmmid_device_resource_usage_stats " +
            "(device_resource_id, device_resource_type, total_capacity, used_capacity, " +
            "available_capacity, active_containers_count, update_time, create_time) " +
            "VALUES (#{deviceResourceId}, #{deviceResourceType}, #{totalCapacity}, #{activeCount}, " +
            "#{totalCapacity} - #{activeCount}, #{activeCount}, NOW(), NOW()) " +
            "ON DUPLICATE KEY UPDATE " +
            "used_capacity = #{activeCount}, " +
            "available_capacity = #{totalCapacity} - #{activeCount}, " +
            "active_containers_count = #{activeCount}, " +
            "last_allocated_at = CASE WHEN #{activeCount} > 0 THEN NOW() ELSE last_allocated_at END, " +
            "last_released_at = CASE WHEN #{activeCount} = 0 THEN NOW() ELSE last_released_at END, " +
            "update_time = NOW()")
    int upsertUsageStats(@Param("deviceResourceId") Long deviceResourceId,
                        @Param("deviceResourceType") String deviceResourceType,
                        @Param("activeCount") Integer activeCount,
                        @Param("totalCapacity") Integer totalCapacity);
}
