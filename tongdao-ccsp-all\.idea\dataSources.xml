<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="DataSourceManagerImpl" format="xml" multifile-model="true">
    <data-source source="LOCAL" name="*************" uuid="1c2bbf3f-00b5-486f-8e7b-55d559aab232">
      <driver-ref>mysql.8</driver-ref>
      <synchronize>true</synchronize>
      <jdbc-driver>com.mysql.cj.jdbc.Driver</jdbc-driver>
      <jdbc-url>***********************************</jdbc-url>
      <jdbc-additional-properties>
        <property name="com.intellij.clouds.kubernetes.db.host.port" />
        <property name="com.intellij.clouds.kubernetes.db.enabled" value="false" />
        <property name="com.intellij.clouds.kubernetes.db.container.port" />
      </jdbc-additional-properties>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
    <data-source source="LOCAL" name="********************************************************************************************************************************** [DEBUG]" group="​CcspGmmidManagementApplication" uuid="bcc5e561-077b-4e6d-b1b0-e66de4a26f49">
      <driver-ref>mysql.8</driver-ref>
      <synchronize>true</synchronize>
      <imported>true</imported>
      <jdbc-driver>com.mysql.cj.jdbc.Driver</jdbc-driver>
      <jdbc-url>**********************************************************************************************************************************</jdbc-url>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
    <data-source source="LOCAL" name="********************************************************************************************************************************** [DEBUG]" group="​CcspGmmidManagementApplication" uuid="e8a15bdb-3aa2-49b6-9e27-871e4d137523">
      <driver-ref>mysql.8</driver-ref>
      <synchronize>true</synchronize>
      <imported>true</imported>
      <jdbc-driver>com.mysql.cj.jdbc.Driver</jdbc-driver>
      <jdbc-url>**********************************************************************************************************************************</jdbc-url>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
  </component>
</project>