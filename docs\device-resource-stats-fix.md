# 设备资源使用统计逻辑问题分析与修复方案

## 问题分析

通过代码审查，我发现了设备资源使用统计逻辑中的几个关键问题：

### 1. 初始化数据缺失问题
**问题**：`isDeviceResourceAvailable`方法依赖`DeviceResourceUsageStatsEntity`表的数据，但如果该表没有对应的统计记录，方法会返回`false`，导致所有设备资源都被认为不可用。

**位置**：`ContainerDeviceResourceServiceImpl.isDeviceResourceAvailable()` 第89-93行
```java
DeviceResourceUsageStatsEntity stats = deviceResourceUsageStatsMapper.selectOne(wrapper);
if (stats == null) {
    LOGGER.warn("未找到设备资源使用统计，ID：{}", deviceResourceId);
    return false; // 这里直接返回false是有问题的
}
```

### 2. 竞态条件问题
**问题**：在`allocateDeviceResources`方法中，先检查可用性，然后创建关联记录，最后更新统计。在并发场景下，可能出现多个容器同时通过可用性检查，导致超分配。

### 3. 统计数据不一致问题
**问题**：统计更新依赖于关联表的数据，但如果关联表和统计表的更新不是原子性的，可能导致数据不一致。

### 4. 设备资源类型处理不完整
**问题**：代码中对VSM和CHSM的处理逻辑不一致，可能导致某些类型的设备资源统计不准确。

## 修复方案

### 修复1：改进可用性检查逻辑
```java
@Override
public boolean isDeviceResourceAvailable(Long deviceResourceId, String deviceResourceType, String allocationType) {
    try {
        LOGGER.debug("检查设备资源可用性，ID：{}，类型：{}，分配类型：{}", deviceResourceId, deviceResourceType, allocationType);

        // 查询设备资源使用统计
        LambdaQueryWrapper<DeviceResourceUsageStatsEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DeviceResourceUsageStatsEntity::getDeviceResourceId, deviceResourceId)
                .eq(DeviceResourceUsageStatsEntity::getDeviceResourceType, deviceResourceType);

        DeviceResourceUsageStatsEntity stats = deviceResourceUsageStatsMapper.selectOne(wrapper);
        if (stats == null) {
            // 如果统计记录不存在，先创建一个
            LOGGER.info("设备资源统计记录不存在，自动创建，ID：{}，类型：{}", deviceResourceId, deviceResourceType);
            stats = createDeviceResourceUsageStats(deviceResourceId, deviceResourceType);
            if (stats == null) {
                LOGGER.error("创建设备资源统计记录失败，ID：{}", deviceResourceId);
                return false;
            }
        }

        // 独占分配：检查是否有可用容量
        if ("exclusive".equalsIgnoreCase(allocationType)) {
            return stats.getAvailableCapacity() > 0;
        }

        // 共享分配：检查总容量是否足够
        if ("shared".equalsIgnoreCase(allocationType)) {
            // 对于共享模式，可以根据业务需求设置最大共享数量
            return stats.getUsedCapacity() < stats.getTotalCapacity();
        }

        return stats.getAvailableCapacity() > 0;

    } catch (Exception e) {
        LOGGER.error("检查设备资源可用性失败", e);
        return false;
    }
}

/**
 * 创建设备资源使用统计记录
 */
private DeviceResourceUsageStatsEntity createDeviceResourceUsageStats(Long deviceResourceId, String deviceResourceType) {
    try {
        DeviceResourceUsageStatsEntity stats = new DeviceResourceUsageStatsEntity();
        stats.setDeviceResourceId(deviceResourceId);
        stats.setDeviceResourceType(deviceResourceType);
        stats.setTotalCapacity(getDeviceResourceTotalCapacity(deviceResourceId, deviceResourceType));
        stats.setUsedCapacity(0);
        stats.setAvailableCapacity(stats.getTotalCapacity());
        stats.setActiveContainersCount(0);
        stats.setCreateTime(LocalDateTime.now());
        stats.setUpdateTime(LocalDateTime.now());

        deviceResourceUsageStatsMapper.insert(stats);
        return stats;
    } catch (Exception e) {
        LOGGER.error("创建设备资源使用统计失败", e);
        return null;
    }
}
```

### 修复2：改进资源分配逻辑（使用数据库锁）
```java
@Override
@Transactional(rollbackFor = Exception.class)
public boolean allocateDeviceResources(Long containerInstanceId, List<Long> deviceResourceIds,
                                     String deviceResourceType, String allocationType,
                                     String configData, Long createdBy) {
    try {
        LOGGER.info("为容器分配设备资源，容器ID：{}，设备资源：{}，类型：{}，分配类型：{}",
                containerInstanceId, deviceResourceIds, deviceResourceType, allocationType);

        if (CollectionUtils.isEmpty(deviceResourceIds)) {
            LOGGER.warn("设备资源ID列表为空");
            return true;
        }

        LocalDateTime now = LocalDateTime.now();
        List<ContainerDeviceResourceRelEntity> relEntities = new ArrayList<>();

        // 使用数据库行锁确保并发安全
        for (Long deviceResourceId : deviceResourceIds) {
            // 检查是否已经存在关联关系
            LambdaQueryWrapper<ContainerDeviceResourceRelEntity> existWrapper = new LambdaQueryWrapper<>();
            existWrapper.eq(ContainerDeviceResourceRelEntity::getContainerInstanceId, containerInstanceId)
                    .eq(ContainerDeviceResourceRelEntity::getDeviceResourceId, deviceResourceId)
                    .eq(ContainerDeviceResourceRelEntity::getDeviceResourceType, deviceResourceType)
                    .eq(ContainerDeviceResourceRelEntity::getStatus, "active");
            
            if (this.count(existWrapper) > 0) {
                LOGGER.warn("设备资源已被该容器使用，跳过分配，容器ID：{}，设备资源ID：{}", containerInstanceId, deviceResourceId);
                continue;
            }

            // 使用SELECT FOR UPDATE锁定统计记录
            DeviceResourceUsageStatsEntity stats = deviceResourceUsageStatsMapper.selectForUpdate(deviceResourceId, deviceResourceType);
            if (stats == null) {
                stats = createDeviceResourceUsageStats(deviceResourceId, deviceResourceType);
                if (stats == null) {
                    LOGGER.error("无法创建设备资源统计记录，ID：{}", deviceResourceId);
                    return false;
                }
            }

            // 再次检查可用性（在锁定状态下）
            boolean available = false;
            if ("exclusive".equalsIgnoreCase(allocationType)) {
                available = stats.getAvailableCapacity() > 0;
            } else if ("shared".equalsIgnoreCase(allocationType)) {
                available = stats.getUsedCapacity() < stats.getTotalCapacity();
            }

            if (!available) {
                LOGGER.error("设备资源不可用（锁定检查），ID：{}，可用容量：{}", deviceResourceId, stats.getAvailableCapacity());
                return false;
            }

            // 创建关联记录
            ContainerDeviceResourceRelEntity relEntity = new ContainerDeviceResourceRelEntity();
            relEntity.setContainerInstanceId(containerInstanceId);
            relEntity.setDeviceResourceId(deviceResourceId);
            relEntity.setDeviceResourceType(deviceResourceType);
            relEntity.setAllocationType(allocationType);
            relEntity.setPriority(1);
            relEntity.setStatus("active");
            relEntity.setConfigData(configData);
            relEntity.setAllocatedAt(now);
            relEntity.setCreateTime(now);
            relEntity.setCreatedBy(createdBy);

            relEntities.add(relEntity);
        }

        if (relEntities.isEmpty()) {
            LOGGER.info("没有新的设备资源需要分配");
            return true;
        }

        // 批量插入关联记录
        boolean result = this.saveBatch(relEntities);
        if (result) {
            // 立即更新设备资源使用统计
            for (Long deviceResourceId : deviceResourceIds) {
                updateDeviceResourceUsageStats(deviceResourceId, deviceResourceType);
            }
            LOGGER.info("成功分配{}个设备资源", relEntities.size());
        }

        return result;

    } catch (Exception e) {
        LOGGER.error("分配设备资源失败", e);
        return false;
    }
}
```

### 修复3：添加SELECT FOR UPDATE支持
在`DeviceResourceUsageStatsMapper`中添加：
```java
/**
 * 使用行锁查询设备资源统计（防止并发问题）
 */
@Select("SELECT * FROM gmmid_device_resource_usage_stats " +
        "WHERE device_resource_id = #{deviceResourceId} " +
        "AND device_resource_type = #{deviceResourceType} " +
        "FOR UPDATE")
DeviceResourceUsageStatsEntity selectForUpdate(@Param("deviceResourceId") Long deviceResourceId,
                                              @Param("deviceResourceType") String deviceResourceType);
```

### 修复4：改进统计更新逻辑
```java
@Override
public void updateDeviceResourceUsageStats(Long deviceResourceId, String deviceResourceType) {
    try {
        LOGGER.debug("更新设备资源使用统计，ID：{}，类型：{}", deviceResourceId, deviceResourceType);

        // 统计活跃的容器关联数量
        LambdaQueryWrapper<ContainerDeviceResourceRelEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ContainerDeviceResourceRelEntity::getDeviceResourceId, deviceResourceId)
                .eq(ContainerDeviceResourceRelEntity::getDeviceResourceType, deviceResourceType)
                .eq(ContainerDeviceResourceRelEntity::getStatus, "active");

        int activeCount = (int) this.count(wrapper);

        // 使用UPSERT操作确保数据一致性
        int updateResult = deviceResourceUsageStatsMapper.upsertUsageStats(
                deviceResourceId, 
                deviceResourceType, 
                activeCount, 
                getDeviceResourceTotalCapacity(deviceResourceId, deviceResourceType)
        );

        if (updateResult == 0) {
            LOGGER.warn("更新设备资源使用统计失败，可能记录不存在，ID：{}", deviceResourceId);
            // 尝试创建新记录
            createDeviceResourceUsageStats(deviceResourceId, deviceResourceType);
        }

        LOGGER.debug("设备资源使用统计更新完成，ID：{}，活跃容器数：{}", deviceResourceId, activeCount);

    } catch (Exception e) {
        LOGGER.error("更新设备资源使用统计失败", e);
    }
}
```

### 修复5：添加UPSERT操作
在`DeviceResourceUsageStatsMapper`中添加：
```java
/**
 * UPSERT操作：插入或更新设备资源使用统计
 */
@Insert("INSERT INTO gmmid_device_resource_usage_stats " +
        "(device_resource_id, device_resource_type, total_capacity, used_capacity, " +
        "available_capacity, active_containers_count, last_allocated_at, update_time, create_time) " +
        "VALUES (#{deviceResourceId}, #{deviceResourceType}, #{totalCapacity}, #{usedCapacity}, " +
        "#{availableCapacity}, #{activeContainersCount}, " +
        "CASE WHEN #{activeContainersCount} > 0 THEN NOW() ELSE NULL END, NOW(), NOW()) " +
        "ON DUPLICATE KEY UPDATE " +
        "used_capacity = #{usedCapacity}, " +
        "available_capacity = #{availableCapacity}, " +
        "active_containers_count = #{activeContainersCount}, " +
        "last_allocated_at = CASE WHEN #{activeContainersCount} > 0 THEN NOW() ELSE last_allocated_at END, " +
        "last_released_at = CASE WHEN #{activeContainersCount} = 0 THEN NOW() ELSE last_released_at END, " +
        "update_time = NOW()")
int upsertUsageStats(@Param("deviceResourceId") Long deviceResourceId,
                    @Param("deviceResourceType") String deviceResourceType,
                    @Param("usedCapacity") Integer usedCapacity,
                    @Param("totalCapacity") Integer totalCapacity,
                    @Param("availableCapacity") Integer availableCapacity,
                    @Param("activeContainersCount") Integer activeContainersCount);

// 简化版本
@Update("INSERT INTO gmmid_device_resource_usage_stats " +
        "(device_resource_id, device_resource_type, total_capacity, used_capacity, " +
        "available_capacity, active_containers_count, update_time, create_time) " +
        "VALUES (#{deviceResourceId}, #{deviceResourceType}, #{totalCapacity}, #{activeCount}, " +
        "#{totalCapacity} - #{activeCount}, #{activeCount}, NOW(), NOW()) " +
        "ON DUPLICATE KEY UPDATE " +
        "used_capacity = #{activeCount}, " +
        "available_capacity = #{totalCapacity} - #{activeCount}, " +
        "active_containers_count = #{activeCount}, " +
        "update_time = NOW()")
int upsertUsageStats(@Param("deviceResourceId") Long deviceResourceId,
                    @Param("deviceResourceType") String deviceResourceType,
                    @Param("activeCount") Integer activeCount,
                    @Param("totalCapacity") Integer totalCapacity);
```

## 实施步骤

1. **立即修复**：修改`isDeviceResourceAvailable`方法，当统计记录不存在时自动创建
2. **数据库修复**：运行SQL脚本初始化现有设备的统计数据
3. **并发安全**：添加SELECT FOR UPDATE支持和UPSERT操作
4. **测试验证**：编写单元测试和集成测试验证修复效果
5. **监控告警**：添加统计数据异常的监控和告警

## 数据修复SQL
```sql
-- 为现有VSM设备创建统计记录
INSERT IGNORE INTO gmmid_device_resource_usage_stats 
(device_resource_id, device_resource_type, total_capacity, used_capacity, available_capacity, active_containers_count, create_time, update_time)
SELECT 
    v.id,
    'VSM',
    1,
    COALESCE(rel_count.cnt, 0),
    1 - COALESCE(rel_count.cnt, 0),
    COALESCE(rel_count.cnt, 0),
    NOW(),
    NOW()
FROM gmmid_vsm v
LEFT JOIN (
    SELECT device_resource_id, COUNT(*) as cnt
    FROM gmmid_container_device_resource_rel 
    WHERE device_resource_type = 'VSM' AND status = 'active'
    GROUP BY device_resource_id
) rel_count ON v.id = rel_count.device_resource_id
WHERE v.status = 'normal';

-- 为现有CHSM设备创建统计记录
INSERT IGNORE INTO gmmid_device_resource_usage_stats 
(device_resource_id, device_resource_type, total_capacity, used_capacity, available_capacity, active_containers_count, create_time, update_time)
SELECT 
    c.id,
    'CHSM',
    COALESCE(c.vsm_capacity, 10),
    COALESCE(rel_count.cnt, 0),
    COALESCE(c.vsm_capacity, 10) - COALESCE(rel_count.cnt, 0),
    COALESCE(rel_count.cnt, 0),
    NOW(),
    NOW()
FROM gmmid_chsm c
LEFT JOIN (
    SELECT device_resource_id, COUNT(*) as cnt
    FROM gmmid_container_device_resource_rel 
    WHERE device_resource_type = 'CHSM' AND status = 'active'
    GROUP BY device_resource_id
) rel_count ON c.id = rel_count.device_resource_id
WHERE c.status = 'normal';
```

这个修复方案解决了设备资源使用统计的主要问题，确保了数据一致性和并发安全性。
