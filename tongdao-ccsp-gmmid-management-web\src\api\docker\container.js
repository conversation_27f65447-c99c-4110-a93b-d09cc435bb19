import request from '@/utils/request'

/**
 * Docker Swarm 容器实例管理 API
 * 
 * 本模块对应后端 ContainerInstanceController
 * 基础路径: /api-management/swarm/container/v1
 * 
 * 功能包括：
 * - 容器生命周期管理（部署、启动、停止、重启、删除）
 * - 容器扩缩容和镜像更新
 * - 容器状态同步和日志获取
 * - 按条件查询容器（镜像、节点）
 */

// 分页查询容器实例列表
export function getContainerList(params) {
  return request({
    url: '/api-management/swarm/container/v1/list-page',
    method: 'get',
    params
  })
}

// 部署容器
export function deployContainer(data) {
  return request({
    url: '/api-management/swarm/container/v1/deploy',
    method: 'post',
    data
  })
}

// 启动容器
export function startContainer(id) {
  return request({
    url: '/api-management/swarm/container/v1/start',
    method: 'post',
    params: { id }
  })
}

// 停止容器
export function stopContainer(id) {
  return request({
    url: '/api-management/swarm/container/v1/stop',
    method: 'post',
    params: { id }
  })
}

// 重启容器
export function restartContainer(id) {
  return request({
    url: '/api-management/swarm/container/v1/restart',
    method: 'post',
    params: { id }
  })
}

// 删除容器
export function removeContainer(id) {
  return request({
    url: '/api-management/swarm/container/v1/delete',
    method: 'post',
    params: { id }
  })
}

// 同步容器状态
export function syncContainerStatus() {
  return request({
    url: '/api-management/swarm/container/v1/sync-status',
    method: 'post'
  })
}

// 根据镜像ID查询容器列表
export function getContainersByImage(imageId) {
  return request({
    url: '/api-management/swarm/container/v1/by-image',
    method: 'get',
    params: { imageId }
  })
}

// 根据节点ID查询容器列表
export function getContainersByNode(nodeId) {
  return request({
    url: '/api-management/swarm/container/v1/by-node',
    method: 'get',
    params: { nodeId }
  })
}

// 获取运行中的容器列表
export function getRunningContainers() {
  return request({
    url: '/api-management/swarm/container/v1/running',
    method: 'get'
  })
}

// 扩缩容操作
export function scaleContainer(id, replicas) {
  return request({
    url: '/api-management/swarm/container/v1/scale',
    method: 'post',
    params: { id, replicas }
  })
}

// 更新容器镜像
export function updateContainerImage(id, newImageId) {
  return request({
    url: '/api-management/swarm/container/v1/update-image',
    method: 'post',
    params: { id, newImageId }
  })
}

// 获取容器日志
export function getContainerLogs(id, lines = 100) {
  return request({
    url: '/api-management/swarm/container/v1/logs',
    method: 'get',
    params: { id, lines }
  })
}

// 根据ID查询容器实例详情
export function getContainerDetail(id) {
  return request({
    url: `/api-management/swarm/container/v1/${id}`,
    method: 'get'
  })
}

// Traefik相关API

// 带Traefik配置的容器部署
// @deprecated 请使用统一的 deployContainer 方法
export function deployContainerWithTraefik(data) {
  console.warn('deployContainerWithTraefik 已弃用，请使用 deployContainer')
  return deployContainer(data)
}

// 获取容器访问地址
export function getContainerAccessUrl(id) {
  return request({
    url: `/api-management/swarm/container/v1/${id}/access-url`,
    method: 'get'
  })
}

// 获取可用的设备资源列表
export function getAvailableDeviceResources(params) {
  return request({
    url: '/api-management/swarm/container/v1/device-resources',
    method: 'get',
    params
  })
}

// 获取容器关联的设备资源列表
export function getContainerDeviceResources(id) {
  return request({
    url: `/api-management/swarm/container/v1/${id}/device-resources`,
    method: 'get'
  })
}

// 获取设备资源使用统计
export function getDeviceResourceUsageStats(deviceResourceId, deviceResourceType) {
  return request({
    url: `/api-management/swarm/container/v1/device-resources/${deviceResourceId}/stats`,
    method: 'get',
    params: { deviceResourceType }
  })
}

// 获取推荐的设备资源
export function getRecommendedDeviceResources(params) {
  return request({
    url: '/api-management/swarm/container/v1/device-resources/recommend',
    method: 'get',
    params
  })
}

// 配置容器Traefik路由
export function configContainerRoute(data) {
  return request({
    url: '/api-management/swarm/container/v1/configure-traefik',
    method: 'post',
    data
  })
}

// 更新容器Traefik路由
export function updateContainerRoute(data) {
  return request({
    url: '/api-management/swarm/container/v1/configure-traefik',
    method: 'post',
    data
  })
}

// 移除容器Traefik配置
export function removeContainerRoute(id) {
  return request({
    url: '/api-management/swarm/container/v1/remove-traefik',
    method: 'post',
    params: { id }
  })
}

// 分布策略相关API

// 带分布策略的容器部署
// @deprecated 请使用统一的 deployContainer 方法
export function deployContainerWithStrategy(data) {
  console.warn('deployContainerWithStrategy 已弃用，请使用 deployContainer')
  return deployContainer(data)
}

// 获取推荐的分布策略
export function getRecommendedStrategy(replicas, availableNodes) {
  const params = { replicas }
  if (availableNodes !== undefined) {
    params.availableNodes = availableNodes
  }
  
  return request({
    url: '/api-management/swarm/container/v1/recommended-strategy',
    method: 'get',
    params
  })
}