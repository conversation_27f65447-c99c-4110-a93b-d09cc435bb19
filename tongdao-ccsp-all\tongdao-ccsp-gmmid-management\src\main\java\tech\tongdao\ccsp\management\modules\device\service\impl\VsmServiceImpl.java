package tech.tongdao.ccsp.management.modules.device.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ccsp.gmmid.beans.consts.Consts;
import com.ccsp.gmmid.beans.enums.ScriptHandleType;
import com.ccsp.gmmid.beans.exceptions.BusinessException;
import com.ccsp.gmmid.beans.form.Pager;
import com.google.common.collect.Lists;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tech.tongdao.ccsp.management.modules.device.entity.ChsmEntity;
import tech.tongdao.ccsp.management.modules.device.entity.DeviceGroupEntity;
import tech.tongdao.ccsp.management.modules.device.entity.VsmEntity;
import tech.tongdao.ccsp.management.modules.device.form.ChsmForm;
import tech.tongdao.ccsp.management.modules.device.form.DeviceGroupForm;
import tech.tongdao.ccsp.management.modules.device.form.VendorForm;
import tech.tongdao.ccsp.management.modules.device.form.VsmForm;
import tech.tongdao.ccsp.management.modules.device.form.third.Gm88Response;
import tech.tongdao.ccsp.management.modules.device.form.third.GmOperateEnum;
import tech.tongdao.ccsp.management.modules.device.form.third.GmRuntimeStatus;
import tech.tongdao.ccsp.management.modules.device.form.third.SingleResult;
import tech.tongdao.ccsp.management.modules.device.form.third.dj.DjVsmForm;
import tech.tongdao.ccsp.management.modules.device.form.third.dj.DjVsmStatisticsForm;
import tech.tongdao.ccsp.management.modules.device.form.third.dj.DjVsmSummaryForm;
import tech.tongdao.ccsp.management.modules.device.helper.DjDeviceHelper;
import tech.tongdao.ccsp.management.modules.device.mapper.VsmRepository;
import tech.tongdao.ccsp.management.modules.device.service.*;
import tech.tongdao.ccsp.management.modules.tenant.entity.TenantEntity;
import tech.tongdao.ccsp.management.modules.tenant.form.TenantForm;
import tech.tongdao.ccsp.management.modules.tenant.service.TenantService;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * <p>
 * 虚机 服务实现类
 * </p>
 *
 * <AUTHOR> Zheng
 * @since 2023-07-10
 */
@Service
public class VsmServiceImpl extends ServiceImpl<VsmRepository, VsmEntity> implements VsmService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ChsmServiceImpl.class);

    private final VendorService vendorService;
    private final ChsmService chsmService;
    private final DeviceGroupService deviceGroupService;
    private final TenantService tenantService;
    private final DjDeviceHelper deviceHelper;
    private final ScriptService scriptService;
    @Resource
    private VsmRepository repository;

    public VsmServiceImpl(VendorService vendorService, ChsmService chsmService, DeviceGroupService deviceGroupService, TenantService tenantService, DjDeviceHelper deviceHelper, ScriptService scriptService) {
        this.vendorService = vendorService;
        this.chsmService = chsmService;
        this.deviceGroupService = deviceGroupService;
        this.tenantService = tenantService;
        this.deviceHelper = deviceHelper;
        this.scriptService = scriptService;
    }

    @Transactional
    @Override
    public VsmForm save(VsmForm form) {

        if (checkIpExist(form.getIp())) {
            throw new BusinessException("该IP已存在");
        }

        Long chsmId = form.getChsmId();

        ChsmEntity chsm = chsmService.getById(chsmId);

        if (chsm == null) {
            throw new BusinessException("没找到此云密码机");
        }

        Gm88Response<SingleResult> gm88Response = deviceHelper.createVsm(chsm.getManagementIp(), chsm.getManagementPort(), form.getFlavor());

        if (gm88Response.getStatus() != 200) {
            throw new BusinessException(gm88Response.getMessage());
        }

        String vsmCode = gm88Response.getVsmId();

        Gm88Response<SingleResult> networkConfigResponse = deviceHelper.configureVsmNetwork(chsm.getManagementIp(), chsm.getManagementPort(), vsmCode,
                form.getIp(), form.getMask(), form.getGateway());

        if (networkConfigResponse.getStatus() != 200) {
            throw new BusinessException("虚拟密码机已创建，但无法配置网络，请重新配置网络");
        }

        VsmEntity db = new VsmEntity();

        String ip = form.getIp();
        String name = "vsm_%s_%d".formatted(ip, form.getServicePort());
        db.setName(name);
        db.setCode(vsmCode);
        db.setChsmId(chsmId);
        db.setTenantId(form.getTenantId());
        db.setDeviceGroupId(form.getDeviceGroupId());
        db.setManagementPort(form.getManagementPort());
        db.setServicePort(form.getServicePort());
        db.setRemark(form.getRemark());
        db.setIp(ip);
        db.setMask(form.getMask());
        db.setGateway(form.getGateway());
        db.setCreateTime(LocalDateTime.now());

        try {
            this.save(db);
            return assembleForm(db);
        } catch (Exception e) {
            LOGGER.error("保存虚机失败", e);
        }

        return null;
    }

    private boolean checkIpExist(String ip) {
        return this.count(Wrappers.<VsmEntity>lambdaQuery().eq(VsmEntity::getIp, ip)) > 0;
    }

    private boolean checkIpExist(String ip, Long id) {
        return this.count(Wrappers.<VsmEntity>lambdaQuery().eq(VsmEntity::getIp, ip).ne(VsmEntity::getIp, ip)) > 0;
    }

    private VsmForm assembleForm(VsmEntity db) {
        VsmForm form = new VsmForm();
        form.setId(db.getId());
        form.setName(db.getName());
        form.setCode(db.getCode());
        form.setChsmId(db.getChsmId());
        form.setTenantId(db.getTenantId());
        form.setDeviceGroupId(db.getDeviceGroupId());
        form.setManagementPort(db.getManagementPort());
        form.setServicePort(db.getServicePort());
        form.setRemark(db.getRemark());
        form.setIp(db.getIp());
        form.setMask(db.getMask());
        form.setGateway(db.getGateway());
        form.setCreateTime(db.getCreateTime());
        return form;
    }

    @Override
    public VsmForm update(VsmForm form) {

        if (form.getId() == null) {
            return null;
        }

        VsmEntity db = this.getById(form.getId());

        if (db != null) {
            UpdateWrapper<VsmEntity> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().eq(VsmEntity::getId, form.getId());
            updateWrapper.lambda().set(VsmEntity::getDeviceGroupId, form.getDeviceGroupId());
            updateWrapper.lambda().set(VsmEntity::getServicePort, form.getServicePort());
            updateWrapper.lambda().set(VsmEntity::getManagementPort, form.getManagementPort());
            updateWrapper.lambda().set(VsmEntity::getRemark, form.getRemark());
            try {
                this.update(updateWrapper);
                return assembleForm(db);
            } catch (Exception e) {
                LOGGER.error("更新虚机失败", e);
            }
        }

        return null;
    }

    @Override
    public Boolean deleteById(Long id) {
        return repository.deleteById(id) > 0;
    }

    @Override
    public Pager<VsmForm> listPage(String key, int page, int pageSize) {
        Page<VsmEntity> paging = new Page<>(page, pageSize);
        QueryWrapper<VsmEntity> wrapper = new QueryWrapper<>();

        if (StringUtils.isNotBlank(key)) {
            wrapper.like("name", key);
        }
        return commonPager(paging, wrapper);
    }

    @Override
    public Pager<VsmForm> listPageByDeviceGroupId(Long deviceGroupId, int page, int pageSize) {

        if (deviceGroupId == null) {
            return new Pager<>(Lists.newArrayList(), 0);
        }

        Page<VsmEntity> paging = new Page<>(page, pageSize);
        QueryWrapper<VsmEntity> wrapper = new QueryWrapper<>();

        wrapper.lambda().eq(VsmEntity::getDeviceGroupId, deviceGroupId);

        return commonPager(paging, wrapper);
    }

    @Override
    public Boolean leaveGroup(Long deviceGroupId, Long vsmId) {
        VsmEntity vsmEntity = this.getById(vsmId);
        Long vsmGroupId = vsmEntity.getDeviceGroupId();
        if (vsmGroupId != null && vsmGroupId.equals(deviceGroupId)) {
            UpdateWrapper<VsmEntity> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("id", vsmId);
            updateWrapper.set("device_group_id", null);
            boolean updateResult = this.update(updateWrapper);
//            scriptService.restartConsoleServer(Collections.singletonList(deviceGroupId), ScriptHandleType.STOP);
            scriptService.stopConsoleServer(null, vsmId, deviceGroupId);
            return updateResult;
        }
        return false;
    }

    @NotNull
    private Pager<VsmForm> commonPager(Page<VsmEntity> paging, QueryWrapper<VsmEntity> wrapper) {
        Page<VsmEntity> infoPage = repository.selectPage(paging, wrapper);

        List<VsmEntity> list = infoPage.getRecords();

        List<VsmForm> infoForms = list.stream().map(this::assembleForm).collect(Collectors.toList());

        List<Long> chsmIds = list.stream().map(VsmEntity::getChsmId).toList();
        List<Long> tenantIds = list.stream().map(VsmEntity::getTenantId).toList();
        List<Long> deviceGroupIds = list.stream().map(VsmEntity::getDeviceGroupId).toList();
        List<Long> vendorIds = new ArrayList<>();

        Map<Long, ChsmForm> chsmFormMap = chsmService.getMapByIds(chsmIds);
        Map<Long, TenantForm> tenantFormMap = tenantService.getMapByIds(tenantIds);
        Map<Long, DeviceGroupForm> deviceGroupFormMap = deviceGroupService.getMapByIds(deviceGroupIds);

        infoForms.forEach(vsmForm -> {
            Long chsmId = vsmForm.getChsmId();
            ChsmForm chsmForm = chsmFormMap.get(chsmId);
            if (chsmForm != null) {
                vsmForm.setChsmName(chsmForm.getName());
                vsmForm.setChsmIp(chsmForm.getManagementIp());
                vsmForm.setChsmPort(chsmForm.getManagementPort());
                vsmForm.setVendorId(chsmForm.getVendorId());
                vendorIds.add(chsmForm.getVendorId());
            }
            Long tenantId = vsmForm.getTenantId();
            TenantForm tenantForm = tenantFormMap.get(tenantId);
            if (tenantForm != null) {
                vsmForm.setTenantName(tenantForm.getName());
            }
            Long deviceGroupId = vsmForm.getDeviceGroupId();
            DeviceGroupForm deviceGroupForm = deviceGroupFormMap.get(deviceGroupId);
            if (deviceGroupForm != null) {
                vsmForm.setDeviceGroupName(deviceGroupForm.getName());
            }
        });

        Map<Long, VendorForm> vendorFormMap = vendorService.getMapByIds(vendorIds);
        infoForms.forEach(vsmForm -> {
            Long vendorId = vsmForm.getVendorId();
            VendorForm vendorForm = vendorFormMap.get(vendorId);
            if (vendorForm != null) {
                vsmForm.setVendorName(vendorForm.getName());
            }
        });

        return new Pager<>(infoForms, infoPage.getTotal());
    }

    @Override
    public Boolean removeGroupDevice(Long id, Long vsmId) {
        return this.leaveGroup(id, vsmId);
    }

    @Override
    public Pager<VsmForm> listAvailableDevices(int page, int pageSize) {
        Page<VsmEntity> paging = new Page<>(page, pageSize);
        QueryWrapper<VsmEntity> wrapper = new QueryWrapper<>();

        wrapper.isNull("device_group_id");

        return commonPager(paging, wrapper);
    }

    @Override
    public Boolean addToGroup(Long deviceGroupId, List<Long> vsmIds) {
        if (deviceGroupId == null || CollectionUtils.isEmpty(vsmIds)) {
            return false;
        }
        vsmIds.forEach(vsmId -> {
            VsmEntity vsmEntity = this.getById(vsmId);
            if (vsmEntity != null) {
                UpdateWrapper<VsmEntity> updateWrapper = new UpdateWrapper<>();
                updateWrapper.lambda().eq(VsmEntity::getId, vsmId);
                updateWrapper.lambda().set(VsmEntity::getDeviceGroupId, deviceGroupId);
                this.update(updateWrapper);
                scriptService.restartConsoleServer(Collections.singletonList(deviceGroupId), Collections.singletonList(vsmId), ScriptHandleType.RESTART);
            }
        });

        return true;
    }

    @Override
    public Boolean reboot(VsmForm vsmForm) {
        ChsmEntity chsm = chsmService.getById(vsmForm.getChsmId());
        if (chsm == null) {
            return false;
        }
        String code = vsmForm.getCode();
        String managementIp = chsm.getManagementIp();
        Integer managementPort = chsm.getManagementPort();
        Gm88Response<SingleResult> gm88Response = deviceHelper.vsmOperation(code, managementIp, managementPort, GmOperateEnum.RESTART.value());
        return gm88Response.getStatus() == 200;
    }

    @Override
    public SingleResult checkVsmStatus(VsmForm vsmForm) {
        ChsmEntity chsm = chsmService.getById(vsmForm.getChsmId());
        if (chsm == null) {
            return SingleResult.failed(vsmForm.getId());
        }
        String managementIp = chsm.getManagementIp();
        Integer managementPort = chsm.getManagementPort();
        String code = vsmForm.getCode();
        Gm88Response<SingleResult> gm88Response = deviceHelper.checkVsmStatus(code, managementIp, managementPort);
        if (gm88Response.getStatus() == 200) {
            SingleResult result = gm88Response.getResult();
            result.setId(vsmForm.getId());
            updateStatus(vsmForm, result.getStatus());
            return result;
        } else {
            updateStatus(vsmForm, GmRuntimeStatus.EXITED.value());
            return SingleResult.failed(vsmForm.getId());
        }
    }

    @Override
    public Boolean start(VsmForm vsmForm) {
        ChsmEntity chsm = chsmService.getById(vsmForm.getChsmId());
        if (chsm == null) {
            return false;
        }
        String code = vsmForm.getCode();
        String managementIp = chsm.getManagementIp();
        Integer managementPort = chsm.getManagementPort();
        Gm88Response<SingleResult> gm88Response = deviceHelper.vsmOperation(code, managementIp, managementPort, GmOperateEnum.START.value());
        return gm88Response.getStatus() == 200;
    }

    public Gm88Response<SingleResult> createVsm(String chsmIp, Integer chsmPort, Integer flavor) {
        return deviceHelper.createVsm(chsmIp, chsmPort, flavor);
    }

    @Override
    public Boolean stop(VsmForm vsmForm) {
        ChsmEntity chsm = chsmService.getById(vsmForm.getChsmId());
        if (chsm == null) {
            return false;
        }
        String code = vsmForm.getCode();
        String managementIp = chsm.getManagementIp();
        Integer managementPort = chsm.getManagementPort();
        Gm88Response<SingleResult> gm88Response = deviceHelper.vsmOperation(code, managementIp, managementPort, GmOperateEnum.STOP.value());
        return gm88Response.getStatus() == 200;
    }

    @Override
    public int countByChsmId(Long chsmId) {
        QueryWrapper<VsmEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(VsmEntity::getChsmId, chsmId);
        return (int) this.count(wrapper);
    }

    @Override
    public int countByDeviceGroupId(Long deviceGroupId) {
        QueryWrapper<VsmEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(VsmEntity::getDeviceGroupId, deviceGroupId);
        return (int) this.count(wrapper);
    }

    @Override
    public VsmForm updateNetwork(VsmForm form) {
        if (form.getId() == null) {
            return null;
        }

        if (checkIpExist(form.getIp(), form.getId())) {
            throw new BusinessException("该IP已存在");
        }
        ChsmEntity chsm = chsmService.getById(form.getChsmId());

        VsmEntity db = this.getById(form.getId());

        if (db != null) {

            Gm88Response<SingleResult> gm88Response = deviceHelper.configureVsmNetwork(chsm.getManagementIp(), chsm.getManagementPort(), db.getCode(),
                    form.getIp(), form.getMask(), form.getGateway());

            if (gm88Response.getStatus() != 200) {
                throw new BusinessException(gm88Response.getMessage());
            }

            String ip = form.getIp();
            String name = "vsm_%s_%d".formatted(ip, form.getServicePort());
            db.setName(name);
            db.setCode(form.getCode());
            db.setIp(ip);
            db.setMask(form.getMask());
            db.setGateway(form.getGateway());

            try {
                this.updateById(db);
                return assembleForm(db);
            } catch (Exception e) {
                LOGGER.error("更新虚机失败", e);
            }
        }

        return null;
    }

    @Override
    public DjVsmForm fetchVsmInfo(Long vsmId, Long chsmId) {

        ChsmEntity chsm = chsmService.getById(chsmId);

        VsmEntity vsmEntity = this.getById(vsmId);

        Gm88Response<DjVsmForm> vsmInfo = deviceHelper.getVsmInfo(chsm.getManagementIp(), chsm.getManagementPort(), vsmEntity.getCode());

        if (vsmInfo.getStatus() == 200) {
            return vsmInfo.getResult();
        } else {
            throw new BusinessException(vsmInfo.getMessage());
        }
    }

    @Override
    public DjVsmStatisticsForm fetchVsmStatistics(String vsmCode, String chsmIp, Integer chsmPort) {
        Gm88Response<DjVsmStatisticsForm> response = deviceHelper.getVsmStatistics(chsmIp, chsmPort, vsmCode);
        if (response.getStatus() == 200) {
            DjVsmStatisticsForm result = response.getResult();
            result.setTimestamp(response.getTimestamp().toInstant(ZoneOffset.of(Consts.ZONE_OFFSET)).toEpochMilli());
            return result;
        } else {
            throw new BusinessException(response.getMessage());
        }
    }

    @Override
    public Boolean importVsm(Long chsmId, List<DjVsmSummaryForm> formList) {

        ChsmEntity chsm = chsmService.getById(chsmId);

        formList.forEach(form -> {
            String vsmId = form.getVsmId();
            Gm88Response<DjVsmForm> vsmInfo = deviceHelper.getVsmInfo(chsm.getManagementIp(), chsm.getManagementPort(), vsmId);

            if (vsmInfo.getStatus() == 200) {
                DjVsmForm djVsmForm = vsmInfo.getResult();


                VsmEntity db = new VsmEntity();

                String ip = djVsmForm.getIp();
                String name = "vsm_%s_%d".formatted(ip, 8018);
                db.setName(name);
                db.setCode(vsmId);
                db.setChsmId(chsmId);
                db.setManagementPort(8018);
                db.setServicePort(chsm.getManagementPort());
                db.setRemark(djVsmForm.getCreated());
                db.setIp(ip);
                db.setMask(djVsmForm.getMask());
                db.setGateway(djVsmForm.getGateway());
                db.setCreateTime(LocalDateTime.now());

                try {
                    this.save(db);
                } catch (Exception e) {
                    LOGGER.error("保存虚机失败", e);
                }
            }
        });


        return true;
    }

    public Boolean updateStatus(VsmForm vsmForm, String status) {

        UpdateWrapper<VsmEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(VsmEntity::getId, vsmForm.getId());
        updateWrapper.lambda().set(VsmEntity::getStatus, status);

        return this.update(updateWrapper);
    }

    // === HSM设备资源管理方法 ===


    /**
     * 获取可用的HSM设备资源列表
     *
     * @return 可用设备列表
     */
    @Override
    public List<VsmForm> listAvailableHsmDevices() {
        LOGGER.info("获取可用的HSM设备资源列");
        QueryWrapper<VsmEntity> wrapper = new QueryWrapper<>();

        // 只查询状态为running的设备
        wrapper.lambda().eq(VsmEntity::getStatus, "running")
                .orderByDesc(VsmEntity::getCreateTime);

        List<VsmEntity> entityList = this.list(wrapper);
        List<VsmForm> forms = assembleDetailForms(entityList);

        return forms;
    }

    @Override
    public List<VsmForm> listHsmDevicesByStatus(String status, Long deviceGroupId) {
        LOGGER.info("根据状态获取HSM设备，状态: {}, 设备组ID: {}", status, deviceGroupId);

        QueryWrapper<VsmEntity> wrapper = new QueryWrapper<>();

        if (StringUtils.isNotBlank(status)) {
            wrapper.lambda().eq(VsmEntity::getStatus, status);
        }

        if (deviceGroupId != null) {
            wrapper.lambda().eq(VsmEntity::getDeviceGroupId, deviceGroupId);
        }

        wrapper.lambda().orderByDesc(VsmEntity::getCreateTime);

        List<VsmEntity> vsmEntities = this.list(wrapper);
        List<VsmForm> forms = assembleDetailForms(vsmEntities);

        return forms;
    }

    @Override
    public Pager<VsmForm> listAvailableHsmDevices(int page, int pageSize) {
        LOGGER.info("获取可用的HSM设备资源列表，页码: {}, 页大小: {}", page, pageSize);

        Page<VsmEntity> paging = new Page<>(page, pageSize);
        QueryWrapper<VsmEntity> wrapper = new QueryWrapper<>();

        // 只查询状态为running的设备
        wrapper.lambda().eq(VsmEntity::getStatus, "running")
                .orderByDesc(VsmEntity::getCreateTime);

        Page<VsmEntity> entityPage = this.page(paging, wrapper);
        List<VsmForm> forms = assembleDetailForms(entityPage.getRecords());

        return new Pager<>(forms, (int) entityPage.getTotal(), page, pageSize);
    }

    @Override
    public Pager<VsmForm> listHsmDevicesByStatus(String status, Long deviceGroupId, int page, int pageSize) {
        LOGGER.info("根据状态获取HSM设备，状态: {}, 设备组ID: {}", status, deviceGroupId);

        Page<VsmEntity> paging = new Page<>(page, pageSize);
        QueryWrapper<VsmEntity> wrapper = new QueryWrapper<>();

        if (StringUtils.isNotBlank(status)) {
            wrapper.lambda().eq(VsmEntity::getStatus, status);
        }

        if (deviceGroupId != null) {
            wrapper.lambda().eq(VsmEntity::getDeviceGroupId, deviceGroupId);
        }

        wrapper.lambda().orderByDesc(VsmEntity::getCreateTime);

        Page<VsmEntity> entityPage = this.page(paging, wrapper);
        List<VsmForm> forms = assembleDetailForms(entityPage.getRecords());

        return new Pager<>(forms, (int) entityPage.getTotal(), page, pageSize);
    }

    @Override
    public VsmForm getHsmDeviceDetail(Long vsmId) {
        LOGGER.info("获取HSM设备详细信息，设备ID: {}", vsmId);

        if (vsmId == null) {
            throw new BusinessException("HSM设备ID不能为空");
        }

        VsmEntity entity = this.getById(vsmId);
        if (entity == null) {
            throw new BusinessException("HSM设备不存在");
        }

        return assembleDetailForm(entity);
    }

    @Override
    public VsmForm findHsmDeviceByIpAndPort(String ip, Integer port) {
        LOGGER.info("根据IP和端口查找HSM设备，IP: {}, 端口: {}", ip, port);

        if (StringUtils.isBlank(ip)) {
            return null;
        }

        QueryWrapper<VsmEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(VsmEntity::getIp, ip);

        if (port != null) {
            wrapper.lambda().eq(VsmEntity::getServicePort, port);
        }

        VsmEntity entity = this.getOne(wrapper);
        return entity != null ? assembleDetailForm(entity) : null;
    }

    @Override
    public List<VsmForm> listAvailableHsmDevicesByGroup(Long deviceGroupId) {
        LOGGER.info("获取设备组下可用的HSM设备，设备组ID: {}", deviceGroupId);

        QueryWrapper<VsmEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(VsmEntity::getDeviceGroupId, deviceGroupId)
                .eq(VsmEntity::getStatus, "running")
                .orderByDesc(VsmEntity::getCreateTime);

        List<VsmEntity> entities = this.list(wrapper);
        return assembleDetailForms(entities);
    }

    @Override
    public Boolean validateHsmDeviceAvailability(Long vsmId) {
        LOGGER.info("验证HSM设备是否可用，设备ID: {}", vsmId);

        if (vsmId == null) {
            return false;
        }

        VsmEntity entity = this.getById(vsmId);
        if (entity == null) {
            return false;
        }

        // 检查设备状态，只有running状态的设备才可用
        String status = entity.getStatus();
        boolean statusValid = "running".equals(status);

        if (!statusValid) {
            LOGGER.warn("HSM设备状态不可用，设备ID: {}, 状态: {}", vsmId, status);
            return false;
        }

        // 可以添加更多验证逻辑，比如检查网络连通性等

        return true;
    }

    @Override
    public VsmForm getHsmDeviceConnectionConfig(Long vsmId) {
        LOGGER.info("获取HSM设备连接配置，设备ID: {}", vsmId);

        VsmForm device = getHsmDeviceDetail(vsmId);
        if (device == null) {
            throw new BusinessException("HSM设备不存在");
        }

        // 验证设备是否可用
        if (!validateHsmDeviceAvailability(vsmId)) {
            throw new BusinessException("HSM设备不可用");
        }

        // 返回连接配置信息
        return device;
    }

    /**
     * 组装详细表单信息（包含关联数据）
     */
    private VsmForm assembleDetailForm(VsmEntity entity) {
        if (entity == null) {
            return null;
        }

        VsmForm form = assembleForm(entity);

        try {
            // 填充云密码机信息
            if (entity.getChsmId() != null) {
                ChsmEntity chsm = chsmService.getById(entity.getChsmId());
                if (chsm != null) {
                    form.setChsmName(chsm.getName());
                    form.setChsmIp(chsm.getManagementIp());
                    form.setChsmPort(chsm.getManagementPort());
                }
            }

            // 填充设备组信息
            if (entity.getDeviceGroupId() != null) {
                DeviceGroupEntity deviceGroup = deviceGroupService.getById(entity.getDeviceGroupId());
                if (deviceGroup != null) {
                    form.setDeviceGroupName(deviceGroup.getName());
                }
            }

            // 填充租户信息
            if (entity.getTenantId() != null) {
                TenantEntity tenant = tenantService.getById(entity.getTenantId());
                if (tenant != null) {
                    form.setTenantName(tenant.getName());
                }
            }

        } catch (Exception e) {
            LOGGER.warn("组装HSM设备详细信息失败，设备ID: {}", entity.getId(), e);
        }

        return form;
    }

    /**
     * 批量组装详细表单信息
     */
    private List<VsmForm> assembleDetailForms(List<VsmEntity> entities) {
        if (CollectionUtils.isEmpty(entities)) {
            return new ArrayList<>();
        }

        return entities.stream()
                .map(this::assembleDetailForm)
                .collect(Collectors.toList());
    }

}
