{"remainingRequest": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\src\\views\\docker\\container\\index.vue?vue&type=style&index=0&id=32efffef&lang=scss&scoped=true", "dependencies": [{"path": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\src\\views\\docker\\container\\index.vue", "mtime": 1756804572859}, {"path": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\node_modules\\css-loader\\index.js", "mtime": 1749118079736}, {"path": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1729062152633}, {"path": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1729062152378}, {"path": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1713955874142}, {"path": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1729062151209}, {"path": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1729062152627}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLmZpbHRlci1pbm5lciB7CiAgZGlzcGxheTogZmxleDsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIGZsZXgtd3JhcDogd3JhcDsKICAKICAuZWwtZm9ybS1pdGVtIHsKICAgIG1hcmdpbi1ib3R0b206IDEwcHg7CiAgICBtYXJnaW4tcmlnaHQ6IDE1cHg7CiAgfQp9CgoucHVsbC1yaWdodCB7CiAgZmxvYXQ6IHJpZ2h0Owp9CgouY2xlYXJmaXg6OmFmdGVyIHsKICBjb250ZW50OiAiIjsKICBkaXNwbGF5OiB0YWJsZTsKICBjbGVhcjogYm90aDsKfQoKLmZvcm0taGVscCB7CiAgZm9udC1zaXplOiAxMnB4OwogIGNvbG9yOiAjOTA5Mzk5OwogIG1hcmdpbi10b3A6IDRweDsKfQoKLmVsLWxpbmsgewogIGZvbnQtc2l6ZTogMTJweDsKICBtYXgtd2lkdGg6IDE1MHB4OwogIG92ZXJmbG93OiBoaWRkZW47CiAgdGV4dC1vdmVyZmxvdzogZWxsaXBzaXM7CiAgd2hpdGUtc3BhY2U6IG5vd3JhcDsKICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7Cn0KCi5pbWFnZS1pbmZvIHsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmN2ZhOwogIHBhZGRpbmc6IDEycHg7CiAgYm9yZGVyLXJhZGl1czogNHB4OwogIGJvcmRlcjogMXB4IHNvbGlkICNlNGU3ZWQ7CiAgCiAgcCB7CiAgICBtYXJnaW46IDRweCAwOwogICAgZm9udC1zaXplOiAxM3B4OwogICAgCiAgICBzdHJvbmcgewogICAgICBjb2xvcjogIzMwMzEzMzsKICAgICAgZm9udC13ZWlnaHQ6IDUwMDsKICAgIH0KICB9Cn0KCi5kZXZpY2UtaW5mbyB7CiAgYmFja2dyb3VuZC1jb2xvcjogI2YwZjlmZjsKICBwYWRkaW5nOiAxMnB4OwogIGJvcmRlci1yYWRpdXM6IDRweDsKICBib3JkZXI6IDFweCBzb2xpZCAjYTdmM2QwOwogIAogIHAgewogICAgbWFyZ2luOiA0cHggMDsKICAgIGZvbnQtc2l6ZTogMTNweDsKICAgIAogICAgc3Ryb25nIHsKICAgICAgY29sb3I6ICMzMDMxMzM7CiAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7CiAgICB9CiAgfQp9Cgouc3RyYXRlZ3ktaW5mbyB7CiAgZm9udC1zaXplOiAxMXB4OwogIGNvbG9yOiAjOTA5Mzk5OwogIG1hcmdpbi10b3A6IDJweDsKfQoKLmhzbS1pbmZvIHsKICBmb250LXNpemU6IDExcHg7CiAgY29sb3I6ICM2N2MyM2E7CiAgbWFyZ2luLXRvcDogMnB4Owp9Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2gCA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/docker/container", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-card shadow=\"never\">\n      <div class=\"filter-container clearfix\">\n        <el-form ref=\"searchForm\" :model=\"listQuery\" inline @submit.native.prevent=\"getList\">\n          <div class=\"filter-inner\">\n            <el-form-item label=\"容器名称\">\n              <el-input v-model.trim=\"listQuery.containerName\" clearable placeholder=\"容器名称\" />\n            </el-form-item>\n            <el-form-item label=\"状态\">\n              <el-select v-model=\"listQuery.status\" clearable placeholder=\"请选择状态\">\n                <el-option label=\"运行中\" value=\"running\" />\n                <el-option label=\"已停止\" value=\"stopped\" />\n                <el-option label=\"已暂停\" value=\"paused\" />\n              </el-select>\n            </el-form-item>\n          </div>\n          <div class=\"pull-right\">\n            <el-button-group>\n              <el-form-item>\n                <el-button type=\"primary\" @click=\"handleSearch\"><i class=\"el-icon-search\" /> 查询</el-button>\n                <el-button type=\"info\" @click=\"handleResetSearch\"><i class=\"el-icon-refresh\" /> 重置</el-button>\n              </el-form-item>\n            </el-button-group>\n          </div>\n        </el-form>\n      </div>\n    </el-card>\n    \n    <el-card shadow=\"never\">\n      <div class=\"filter-container clearfix\">\n        <div class=\"filter-inner\">\n          <el-button type=\"primary\" @click=\"handleDeployContainer\">部署容器</el-button>\n          <el-button type=\"success\" @click=\"handleSyncStatus\">同步状态</el-button>\n        </div>\n      </div>\n      \n      <el-table\n        ref=\"dataTable\"\n        v-loading=\"loading\"\n        :data=\"tableList\"\n        style=\"width: 100%;\"\n      >\n        <el-table-column align=\"center\" label=\"序号\" type=\"index\" width=\"50\" />\n        <el-table-column align=\"center\" label=\"容器ID\" min-width=\"120\" prop=\"id\" />\n        <el-table-column align=\"center\" label=\"容器名称\" min-width=\"150\" prop=\"containerName\" />\n        <el-table-column align=\"center\" label=\"所属应用\" min-width=\"140\">\n          <template v-slot=\"{row}\">\n            <span>{{ row.applicationName || '-' }}</span>\n          </template>\n        </el-table-column>\n        <el-table-column align=\"center\" label=\"镜像\" min-width=\"150\">\n          <template v-slot=\"{row}\">\n            <span>{{ row.imageName }}:{{ row.imageTag || 'latest' }}</span>\n          </template>\n        </el-table-column>\n        <el-table-column align=\"center\" label=\"状态\" min-width=\"100\" prop=\"status\">\n          <template v-slot=\"{row}\">\n            <el-tag v-if=\"row.status === 'running'\" type=\"success\">运行中</el-tag>\n            <el-tag v-else-if=\"row.status === 'stopped'\" type=\"danger\">已停止</el-tag>\n            <el-tag v-else-if=\"row.status === 'paused'\" type=\"warning\">已暂停</el-tag>\n            <el-tag v-else>{{ row.status }}</el-tag>\n          </template>\n        </el-table-column>\n        <el-table-column align=\"center\" label=\"端口\" min-width=\"120\">\n          <template v-slot=\"{row}\">\n            <span>{{ formatPorts(row.portMappings) }}</span>\n          </template>\n        </el-table-column>\n        <el-table-column align=\"center\" label=\"副本信息\" min-width=\"120\">\n          <template v-slot=\"{row}\">\n            <div v-if=\"row.replicas\">\n              <div>副本数：{{ row.replicas || '-' }}</div>\n              <div v-if=\"row.distributionStrategy\" class=\"strategy-info\">\n                分布：{{ getStrategyDisplayName(row.distributionStrategy) }}\n              </div>\n            </div>\n            <span v-else>-</span>\n          </template>\n        </el-table-column>\n        <el-table-column align=\"center\" label=\"访问地址\" min-width=\"180\">\n          <template v-slot=\"{row}\">\n            <div v-if=\"row.accessUrl\">\n              <el-link :href=\"row.accessUrl\" target=\"_blank\" type=\"primary\">\n                {{ row.accessUrl }}\n              </el-link>\n              <el-button \n                type=\"text\" \n                size=\"mini\" \n                @click=\"copyToClipboard(row.accessUrl)\"\n                style=\"margin-left: 5px;\"\n              >\n                <i class=\"el-icon-copy-document\"></i>\n              </el-button>\n            </div>\n            <el-button \n              v-else-if=\"row.status === 'running'\" \n              type=\"text\" \n              size=\"mini\" \n              @click=\"handleConfigRoute(row)\"\n            >\n              配置访问\n            </el-button>\n            <span v-else>-</span>\n          </template>\n        </el-table-column>\n        <el-table-column align=\"center\" label=\"HSM设备\" min-width=\"120\">\n          <template v-slot=\"{row}\">\n            <div v-if=\"row.hsmDeviceId || row.hsmConfigured\">\n              <el-tag type=\"success\" size=\"mini\">已配置</el-tag>\n              <div v-if=\"row.hsmDeviceName\" class=\"hsm-info\">\n                {{ row.hsmDeviceName }}\n              </div>\n            </div>\n            <span v-else>-</span>\n          </template>\n        </el-table-column>\n        <el-table-column align=\"center\" label=\"节点\" min-width=\"100\" prop=\"nodeName\" />\n        <el-table-column align=\"center\" label=\"创建时间\" min-width=\"150\">\n          <template v-slot=\"{row}\">\n            <span>{{ formatDate(row.createTime) }}</span>\n          </template>\n        </el-table-column>\n        <el-table-column align=\"center\" fixed=\"right\" label=\"操作\" width=\"250\">\n          <template v-slot=\"{row}\">\n            <el-button v-if=\"row.status === 'running'\" type=\"text\" @click=\"handleStopContainer(row)\">停止</el-button>\n            <el-button v-else type=\"text\" @click=\"handleStartContainer(row)\">启动</el-button>\n            <el-button type=\"text\" @click=\"handleRestartContainer(row)\">重启</el-button>\n            <el-button type=\"text\" @click=\"handleScaleContainer(row)\">扩缩容</el-button>\n            <el-button type=\"text\" @click=\"handleViewLogs(row)\">日志</el-button>\n            <el-button v-if=\"row.accessUrl\" type=\"text\" @click=\"handleUpdateRoute(row)\">更新路由</el-button>\n            <el-button type=\"text\" @click=\"handleRemoveContainer(row)\">删除</el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n      \n      <pagination\n        v-show=\"total > listQuery.pageSize\"\n        :limit.sync=\"listQuery.pageSize\"\n        :page.sync=\"listQuery.page\"\n        :total=\"total\"\n        layout=\"prev, pager, next\"\n        @pagination=\"getList\"\n      />\n    </el-card>\n    \n    <!-- 部署容器对话框 -->\n    <el-dialog :visible.sync=\"deployDialogVisible\" title=\"部署容器\" width=\"600px\">\n      <el-form ref=\"deployForm\" :model=\"deployForm\" :rules=\"deployRules\" label-width=\"120px\">\n        <el-form-item label=\"关联应用\" prop=\"applicationId\">\n          <el-select v-model=\"deployForm.applicationId\" filterable placeholder=\"请选择应用\" style=\"width: 100%;\" :loading=\"appOptionsLoading\">\n            <el-option v-for=\"opt in appOptions\" :key=\"opt.value\" :label=\"opt.label\" :value=\"String(opt.value)\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"容器名称\" prop=\"containerName\">\n          <el-input v-model.trim=\"deployForm.containerName\" placeholder=\"例如: my-nginx\" />\n        </el-form-item>\n        <el-form-item label=\"镜像选择\" prop=\"imageId\">\n          <el-select \n            v-model=\"deployForm.imageId\" \n            filterable \n            placeholder=\"请选择镜像\"\n            @change=\"handleImageChange\"\n            style=\"width: 100%;\"\n            :loading=\"imageListLoading\"\n            :disabled=\"imageList.length === 0\"\n          >\n            <el-option\n              v-for=\"image in imageList\"\n              :key=\"image.id\"\n              :label=\"`${image.name}:${image.tag}`\"\n              :value=\"image.id\"\n            >\n              <span style=\"float: left\">{{ image.name }}:{{ image.tag }}</span>\n              <span style=\"float: right; color: #8492a6; font-size: 13px\">{{ formatImageSize(image.sizeMb) }}</span>\n            </el-option>\n            <div v-if=\"imageList.length === 0\" slot=\"empty\">\n              <span v-if=\"imageListLoading\">加载中...</span>\n              <span v-else>暂无可用镜像，请先构建或导入镜像</span>\n            </div>\n          </el-select>\n          <div class=\"form-help\">\n            选择已有的Docker镜像进行部署\n            <el-button \n              type=\"text\" \n              size=\"mini\" \n              @click=\"loadImageList\"\n              style=\"margin-left: 8px;\"\n            >\n              <i class=\"el-icon-refresh\"></i> 刷新\n            </el-button>\n          </div>\n        </el-form-item>\n        <el-form-item label=\"镜像信息\" v-if=\"selectedImage\">\n          <div class=\"image-info\">\n            <p><strong>名称：</strong>{{ selectedImage.name }}</p>\n            <p><strong>标签：</strong>{{ selectedImage.tag }}</p>\n            <p><strong>大小：</strong>{{ formatImageSize(selectedImage.sizeMb) }}</p>\n            <p v-if=\"selectedImage.description\"><strong>描述：</strong>{{ selectedImage.description }}</p>\n          </div>\n        </el-form-item>\n        <el-form-item label=\"服务端口\" prop=\"servicePort\">\n          <el-input-number v-model=\"deployForm.servicePort\" placeholder=\"容器内部端口，如: 80\" :min=\"1\" :max=\"65535\" />\n          <div class=\"form-help\">容器内应用监听的端口号</div>\n        </el-form-item>\n        <el-form-item label=\"副本数\" prop=\"replicas\">\n          <el-input-number v-model=\"deployForm.replicas\" :min=\"1\" />\n        </el-form-item>\n        <el-form-item label=\"分布策略\" v-if=\"deployForm.replicas > 1\">\n          <el-select v-model=\"deployForm.distributionStrategy\" placeholder=\"请选择分布策略\" style=\"width: 100%;\">\n            <el-option label=\"跨节点分散（推荐）\" value=\"SPREAD_ACROSS_NODES\" />\n            <el-option label=\"仅Worker节点\" value=\"WORKER_NODES_ONLY\" />\n            <el-option label=\"仅Manager节点\" value=\"MANAGER_NODES_ONLY\" />\n            <el-option label=\"平衡分布\" value=\"BALANCED\" />\n            <el-option label=\"跨可用区分散\" value=\"SPREAD_ACROSS_ZONES\" />\n          </el-select>\n          <div class=\"form-help\">多副本时的部署分布策略，推荐选择跨节点分散</div>\n        </el-form-item>\n        <el-form-item label=\"启用路由\">\n          <el-switch v-model=\"deployForm.enableTraefik\" active-text=\"启用\" inactive-text=\"禁用\" />\n          <div class=\"form-help\">启用后可通过内网地址访问服务</div>\n        </el-form-item>\n        <el-form-item label=\"HSM设备资源\">\n          <el-switch v-model=\"deployForm.enableHsm\" active-text=\"启用\" inactive-text=\"禁用\" @change=\"handleHsmToggle\" />\n          <div class=\"form-help\">启用后可配置HSM加密设备资源</div>\n        </el-form-item>\n        <el-form-item v-if=\"deployForm.enableHsm\" label=\"选择设备\" prop=\"hsmDeviceId\">\n          <el-select \n            v-model=\"deployForm.hsmDeviceId\" \n            filterable \n            placeholder=\"请选择HSM设备\"\n            @change=\"handleHsmDeviceChange\"\n            style=\"width: 100%;\"\n            :loading=\"hsmDeviceListLoading\"\n            :disabled=\"hsmDeviceList.length === 0\"\n          >\n            <el-option\n              v-for=\"device in hsmDeviceList\"\n              :key=\"device.deviceId\"\n              :label=\"device.deviceName\"\n              :value=\"device.deviceId\"\n            >\n              <span style=\"float: left\">{{ device.deviceName }}</span>\n              <span style=\"float: right; color: #8492a6; font-size: 13px\">{{ device.ipAddress }}:{{ device.servicePort }}</span>\n            </el-option>\n            <div v-if=\"hsmDeviceList.length === 0\" slot=\"empty\">\n              <span v-if=\"hsmDeviceListLoading\">加载中...</span>\n              <span v-else>暂无可用设备</span>\n            </div>\n          </el-select>\n          <div class=\"form-help\">\n            选择可用的HSM加密设备\n            <el-button \n              type=\"text\" \n              size=\"mini\" \n              @click=\"loadHsmDeviceList\"\n              style=\"margin-left: 8px;\"\n            >\n              <i class=\"el-icon-refresh\"></i> 刷新\n            </el-button>\n          </div>\n        </el-form-item>\n        <el-form-item v-if=\"deployForm.enableHsm && selectedHsmDevice\" label=\"设备信息\">\n          <div class=\"device-info\">\n            <p><strong>设备名称：</strong>{{ selectedHsmDevice.deviceName }}</p>\n            <p><strong>IP地址：</strong>{{ selectedHsmDevice.ipAddress }}</p>\n            <p><strong>服务端口：</strong>{{ selectedHsmDevice.servicePort }}</p>\n            <p><strong>管理端口：</strong>{{ selectedHsmDevice.managementPort }}</p>\n            <p><strong>状态：</strong>\n              <el-tag v-if=\"selectedHsmDevice.status === 'available'\" type=\"success\" size=\"mini\">可用</el-tag>\n              <el-tag v-else-if=\"selectedHsmDevice.status === 'running'\" type=\"success\" size=\"mini\">运行中</el-tag>\n              <el-tag v-else-if=\"selectedHsmDevice.status === 'active'\" type=\"success\" size=\"mini\">活跃</el-tag>\n              <el-tag v-else type=\"warning\" size=\"mini\">{{ selectedHsmDevice.status }}</el-tag>\n            </p>\n            <p v-if=\"selectedHsmDevice.description\"><strong>描述：</strong>{{ selectedHsmDevice.description }}</p>\n            <p v-if=\"selectedHsmDevice.deviceGroupName\"><strong>设备组：</strong>{{ selectedHsmDevice.deviceGroupName }}</p>\n          </div>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"deployDialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"confirmDeployContainer\">部署</el-button>\n      </div>\n    </el-dialog>\n    \n    <!-- 容器日志对话框 -->\n    <el-dialog :visible.sync=\"logsDialogVisible\" title=\"容器日志\" width=\"800px\">\n      <el-input\n        v-model=\"containerLogs\"\n        :rows=\"20\"\n        readonly\n        type=\"textarea\"\n      />\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"logsDialogVisible = false\">关闭</el-button>\n      </div>\n    </el-dialog>\n    \n    <!-- 扩缩容对话框 -->\n    <el-dialog :visible.sync=\"scaleDialogVisible\" title=\"容器扩缩容\" width=\"500px\">\n      <el-form ref=\"scaleForm\" :model=\"scaleForm\" :rules=\"scaleRules\" label-width=\"100px\">\n        <el-form-item label=\"副本数\" prop=\"replicas\">\n          <el-input-number v-model=\"scaleForm.replicas\" :min=\"1\" />\n          <div class=\"form-help\">当前运行的容器实例数量</div>\n        </el-form-item>\n        <el-form-item label=\"分布策略\" v-if=\"scaleForm.replicas > 1\">\n          <el-select v-model=\"scaleForm.distributionStrategy\" placeholder=\"请选择分布策略\" style=\"width: 100%;\">\n            <el-option label=\"跨节点分散（推荐）\" value=\"SPREAD_ACROSS_NODES\" />\n            <el-option label=\"仅Worker节点\" value=\"WORKER_NODES_ONLY\" />\n            <el-option label=\"仅Manager节点\" value=\"MANAGER_NODES_ONLY\" />\n            <el-option label=\"平衡分布\" value=\"BALANCED\" />\n          </el-select>\n          <div class=\"form-help\">多副本时的部署分布策略</div>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"scaleDialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"confirmScaleContainer\">确认</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { mapGetters } from 'vuex'\nimport Pagination from '@/components/Pagination'\nimport { \n  getContainerList, \n  deployContainer, \n  deployContainerWithTraefik,\n  deployContainerWithStrategy,\n  getRecommendedStrategy,\n  startContainer, \n  stopContainer, \n  restartContainer, \n  removeContainer, \n  getContainerLogs, \n  syncContainerStatus, \n  scaleContainer, \n  updateContainerImage,\n  configContainerRoute,\n  updateContainerRoute\n} from '@/api/docker/container'\nimport { getImageList } from '@/api/docker/image'\nimport { fetchApplicationOptions, fetchUserApps } from '@/api/application'\nimport { getAvailableHsmDevices, getHsmDeviceDetail, deployContainerWithHsm } from '@/api/docker/hsm'\n\nexport default {\n  name: 'DockerContainerIndex',\n  components: {\n    Pagination\n  },\n  data() {\n    return {\n      tableList: [],\n      loading: false,\n      total: 0,\n      listQuery: {\n        page: 1,\n        pageSize: 20,\n        containerName: undefined,\n        status: undefined\n      },\n      logsDialogVisible: false,\n      containerLogs: '',\n      currentContainer: null,\n      deployDialogVisible: false,\n      deployForm: {\n        containerName: '',\n        imageId: null,\n        imageName: '',\n        imageTag: 'latest',\n        servicePort: 80,\n        replicas: 1,\n        distributionStrategy: 'SPREAD_ACROSS_NODES',\n        enableTraefik: true,\n        enableHsm: false,\n        hsmDeviceId: null,\n        deployedBy: null,\n        applicationId: null,\n        applicationName: ''\n      },\n      deployRules: {\n        applicationId: [{ required: true, message: '请选择关联应用', trigger: 'change' }],\n        containerName: [{ required: true, message: '请输入容器名称', trigger: 'blur' }],\n        imageId: [{ required: true, message: '请选择镜像', trigger: 'change' }],\n        servicePort: [{ required: true, message: '请输入服务端口', trigger: 'blur' }]\n      },\n      scaleDialogVisible: false,\n      scaleForm: {\n        id: '',\n        replicas: 1,\n        distributionStrategy: 'SPREAD_ACROSS_NODES'\n      },\n      scaleRules: {\n        replicas: [{ required: true, message: '请输入副本数量', trigger: 'blur' }]\n      },\n      // 镜像相关数据\n      imageList: [],\n      imageListLoading: false,\n      selectedImage: null,\n      // HSM设备相关数据\n      hsmDeviceList: [],\n      hsmDeviceListLoading: false,\n      selectedHsmDevice: null,\n      // 应用下拉\n      appOptions: [],\n      appOptionsLoading: false\n    }\n  },\n  methods: {\n    handleSearch() {\n      this.getList()\n    },\n    handleResetSearch() {\n      this.listQuery = {\n        page: 1,\n        pageSize: 20,\n        containerName: undefined,\n        status: undefined\n      }\n      this.getList()\n    },\n    handleDeployContainer() {\n      // 首先加载镜像列表\n      Promise.all([this.loadImageList(), this.loadAppOptions()]).then(() => {\n        this.deployForm = {\n          containerName: '',\n          imageId: null,\n          imageName: '',\n          imageTag: 'latest',\n          servicePort: 80,\n          replicas: 1,\n          distributionStrategy: 'SPREAD_ACROSS_NODES',\n          enableTraefik: true,\n          enableHsm: false,\n          hsmDeviceId: null,\n          deployedBy: this.user ? this.user.id : null,\n          applicationId: null,\n          applicationName: ''\n        }\n        this.selectedImage = null\n        this.selectedHsmDevice = null\n        this.deployDialogVisible = true\n        this.$nextTick(() => {\n          this.$refs['deployForm'].clearValidate()\n        })\n      })\n    },\n    confirmDeployContainer() {\n      this.$refs['deployForm'].validate((valid) => {\n        if (valid) {\n          // 检查HSM设备配置\n          if (this.deployForm.enableHsm && !this.deployForm.hsmDeviceId) {\n            this.$message.error('请选择HSM设备')\n            return\n          }\n          \n          const deployData = { ...this.deployForm }\n          \n          // 确保镜像信息完整\n          if (this.selectedImage) {\n            deployData.imageName = this.selectedImage.imageName\n            deployData.imageTag = this.selectedImage.imageTag\n          }\n          \n          // 构建HSM设备配置\n          if (this.deployForm.enableHsm && this.selectedHsmDevice) {\n            deployData.hsmDeviceConfig = {\n              encryptorGroupId: 1, // 默认组ID\n              encryptorId: this.selectedHsmDevice.deviceId,\n              encryptorName: this.selectedHsmDevice.deviceName,\n              serverIpAddr: this.selectedHsmDevice.ipAddress,\n              serverPort: this.selectedHsmDevice.servicePort,\n              tcpConnNum: this.selectedHsmDevice.tcpConnNum || 5,\n              msgHeadLen: this.selectedHsmDevice.msgHeadLen || 4,\n              msgTailLen: this.selectedHsmDevice.msgTailLen || 0,\n              asciiOrEbcdic: this.selectedHsmDevice.encoding || 0,\n              dynamicLibPath: './libdeviceapi.so'\n            }\n          }\n          \n          // 根据配置选择部署接口\n          let deployFn\n          if (this.deployForm.enableHsm) {\n            // 使用带HSM配置的部署接口\n            console.log('使用带HSM配置的部署接口')\n            deployFn = this.deployWithHsm\n          } else if (deployData.replicas > 1 && deployData.distributionStrategy) {\n            // 使用带分布策略的部署接口\n            console.log('使用带分布策略的部署接口')\n            deployFn = deployContainerWithStrategy\n          } else if (deployData.enableTraefik) {\n            // 使用带Traefik的部署接口\n            console.log('使用带Traefik的部署接口')\n            deployFn = this.deployWithTraefik\n          } else {\n            // 使用基本部署接口\n            console.log('使用基本部署接口')\n            deployFn = deployContainer\n          }\n          \n          deployFn(deployData).then((response) => {\n            this.deployDialogVisible = false\n            if (response.code === 20000) {\n              if (response.data) {\n                // 处理不同的响应结构\n                let message = '容器部署成功！'\n                \n                if (response.data.accessUrl) {\n                  message += `访问地址：${response.data.accessUrl}`\n                } else if (response.data.container && response.data.container.accessUrl) {\n                  message += `访问地址：${response.data.container.accessUrl}`\n                }\n                \n                if (response.data.distributionStrategy) {\n                  message += `，分布策略：${this.getStrategyDisplayName(response.data.distributionStrategy)}`\n                }\n                \n                if (response.data.hsmConfigured) {\n                  message += `，HSM设备：${this.selectedHsmDevice.deviceName}`\n                }\n                \n                this.$message.success(message)\n              } else {\n                this.$message.success('容器部署任务已提交')\n              }\n              this.getList()\n            } else {\n              this.$message.error(response.message || '部署失败')\n            }\n          }).catch(() => {\n            // 错误处理已在拦截器中处理\n          })\n        }\n      })\n    },\n\n    // 加载应用选项\n    async loadAppOptions() {\n      this.appOptionsLoading = true\n      try {\n        let resp\n        // 租户角色使用 user/list-options，其它使用 fetch-options\n        if (this.user && Array.isArray(this.user.roles) && this.user.roles.includes('ROLE_TENANT')) {\n          resp = await fetchUserApps()\n        } else {\n          resp = await fetchApplicationOptions()\n        }\n        if (resp && resp.code === 20000) {\n          const data = Array.isArray(resp.data) ? resp.data : (resp.data && resp.data.list) ? resp.data.list : []\n          this.appOptions = data.map(item => {\n            if (item.label && (item.value !== undefined)) return item\n            return { label: item.name || item.applicationName || item.label, value: String(item.id || item.value) }\n          })\n        } else {\n          this.appOptions = []\n        }\n      } catch (e) {\n        this.appOptions = []\n      } finally {\n        this.appOptionsLoading = false\n      }\n    },\n    handleStartContainer(row) {\n      startContainer(row.id).then(() => {\n        this.$message.success('容器启动成功')\n        this.getList()\n      })\n    },\n    handleStopContainer(row) {\n      stopContainer(row.id).then(() => {\n        this.$message.success('容器停止成功')\n        this.getList()\n      })\n    },\n    handleRestartContainer(row) {\n      restartContainer(row.id).then(() => {\n        this.$message.success('容器重启成功')\n        this.getList()\n      })\n    },\n    handleRemoveContainer(row) {\n      this.$msgbox.confirm(`确认要删除容器 ${row.containerName} 吗？`, '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        removeContainer(row.id).then(() => {\n          this.$message.success('删除成功')\n          this.getList()\n        })\n      }).catch(() => {\n        // 取消删除\n      })\n    },\n    handleScaleContainer(row) {\n      this.scaleForm = {\n        id: row.id,\n        replicas: row.replicas || 1, // 使用当前副本数或默认值\n        distributionStrategy: row.distributionStrategy || 'SPREAD_ACROSS_NODES'\n      }\n      this.scaleDialogVisible = true\n      this.$nextTick(() => {\n        this.$refs['scaleForm'].clearValidate()\n      })\n    },\n    confirmScaleContainer() {\n      this.$refs['scaleForm'].validate((valid) => {\n        if (valid) {\n          scaleContainer(this.scaleForm.id, this.scaleForm.replicas).then(() => {\n            this.scaleDialogVisible = false\n            this.$message.success('容器扩缩容任务已提交')\n            this.getList()\n          })\n        }\n      })\n    },\n    handleViewLogs(row) {\n      this.currentContainer = row\n      getContainerLogs(row.id).then(response => {\n        if (response.code === 20000) {\n          this.containerLogs = response.data || ''\n          this.logsDialogVisible = true\n        }\n      })\n    },\n    handleSyncStatus() {\n      syncContainerStatus().then(() => {\n        this.$message.success('容器状态同步任务已提交')\n        this.getList()\n      })\n    },\n    getList() {\n      this.loading = true\n      getContainerList(this.listQuery).then(response => {\n        if (response.code === 20000) {\n          this.tableList = response.data.list\n          this.total = response.data.totalCount\n        }\n        this.loading = false\n      }).catch(() => {\n        this.loading = false\n      })\n    },\n    // 格式化端口映射显示\n    formatPorts(portMappings) {\n      if (!portMappings) return '-'\n      try {\n        const ports = JSON.parse(portMappings)\n        if (Array.isArray(ports) && ports.length > 0) {\n          return ports.map(p => `${p.hostPort || ''}:${p.containerPort}/${p.protocol || 'tcp'}`).join(', ')\n        }\n        return '-'\n      } catch (e) {\n        return portMappings\n      }\n    },\n    // 格式化日期显示\n    formatDate(dateTime) {\n      if (!dateTime) return '-'\n      return new Date(dateTime).toLocaleString('zh-CN')\n    },\n    \n    // 复制到剪贴板\n    copyToClipboard(text) {\n      if (navigator.clipboard) {\n        navigator.clipboard.writeText(text).then(() => {\n          this.$message.success('地址已复制到剪贴板')\n        })\n      } else {\n        // 降级方案\n        const textArea = document.createElement('textarea')\n        textArea.value = text\n        document.body.appendChild(textArea)\n        textArea.select()\n        document.execCommand('copy')\n        document.body.removeChild(textArea)\n        this.$message.success('地址已复制到剪贴板')\n      }\n    },\n    \n    // 配置访问路由\n    handleConfigRoute(row) {\n      this.$prompt('请输入服务端口（容器内部端口）', '配置访问路由', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        inputValue: '80',\n        inputValidator: (value) => {\n          const port = parseInt(value)\n          if (!port || port < 1 || port > 65535) {\n            return '请输入有效的端口号(1-65535)'\n          }\n          return true\n        }\n      }).then(({ value }) => {\n        const routeConfig = {\n          containerId: row.id,\n          servicePort: parseInt(value),\n          enableTraefik: true\n        }\n        \n        this.configContainerRoute(routeConfig).then((response) => {\n          if (response.code === 20000 && response.data && response.data.accessUrl) {\n            this.$message.success(`路由配置成功！访问地址：${response.data.accessUrl}`)\n            this.getList()\n          } else {\n            this.$message.error(response.message || '配置失败')\n          }\n        }).catch(() => {\n          // 错误处理已在拦截器中处理\n        })\n      }).catch(() => {\n        // 取消操作\n      })\n    },\n    \n    // 更新路由配置\n    handleUpdateRoute(row) {\n      this.$prompt('请输入新的服务端口', '更新路由', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        inputValidator: (value) => {\n          const port = parseInt(value)\n          if (!port || port < 1 || port > 65535) {\n            return '请输入有效的端口号(1-65535)'\n          }\n          return true\n        }\n      }).then(({ value }) => {\n        const routeConfig = {\n          containerId: row.id,\n          servicePort: parseInt(value)\n        }\n        \n        this.updateContainerRoute(routeConfig).then((response) => {\n          if (response.code === 20000 && response.data && response.data.accessUrl) {\n            this.$message.success(`路由更新成功！新地址：${response.data.accessUrl}`)\n            this.getList()\n          } else {\n            this.$message.error(response.message || '更新失败')\n          }\n        }).catch(() => {\n          // 错误处理已在拦截器中处理\n        })\n      }).catch(() => {\n        // 取消操作\n      })\n    },\n    \n    // 带Traefik的部署方法\n    async deployWithTraefik(deployData) {\n      return deployContainerWithTraefik(deployData)\n    },\n    \n    // 配置容器路由方法\n    async configContainerRoute(routeConfig) {\n      return configContainerRoute(routeConfig)\n    },\n    \n    // 更新容器路由方法\n    async updateContainerRoute(routeConfig) {\n      return updateContainerRoute(routeConfig)\n    },\n    \n    // 加载镜像列表\n    async loadImageList() {\n      this.imageListLoading = true\n      try {\n        const response = await getImageList()\n        if (response.code === 20000) {\n          // 处理分页数据结构\n          if (response.data && response.data.list) {\n            // 如果返回的是分页结构\n            this.imageList = response.data.list.map(image => {\n              return {\n                ...image,\n                // 确保 sizeMb 是数字类型\n                sizeMb: typeof image.sizeMb === 'string' ? parseInt(image.sizeMb) : image.sizeMb\n              }\n            })\n          } else if (Array.isArray(response.data)) {\n            // 如果返回的是直接数组\n            this.imageList = response.data.map(image => {\n              return {\n                ...image,\n                sizeMb: typeof image.sizeMb === 'string' ? parseInt(image.sizeMb) : image.sizeMb\n              }\n            })\n          } else {\n            this.imageList = []\n          }\n          \n          if (this.imageList.length === 0) {\n            this.$message.info('当前没有可用的镜像，请先构建或导入镜像')\n          }\n        } else {\n          this.$message.error('获取镜像列表失败：' + (response.message || '未知错误'))\n          this.imageList = []\n        }\n      } catch (error) {\n        console.error('加载镜像列表失败:', error)\n        this.$message.error('加载镜像列表失败')\n        this.imageList = []\n      } finally {\n        this.imageListLoading = false\n      }\n    },\n    \n    // 处理镜像选择变化\n    handleImageChange(imageId) {\n      if (imageId) {\n        this.selectedImage = this.imageList.find(img => img.id === imageId)\n        if (this.selectedImage) {\n          // 自动填充镜像名称和标签\n          this.deployForm.imageName = this.selectedImage.name\n          this.deployForm.imageTag = this.selectedImage.tag\n          \n          // 智能生成容器名称（如果当前为空）\n          if (!this.deployForm.containerName) {\n            this.suggestContainerName(this.selectedImage.name, this.selectedImage.tag)\n          }\n          \n          // 根据镜像类型智能推荐端口\n          this.suggestServicePort(this.selectedImage.name)\n        }\n      } else {\n        this.selectedImage = null\n        this.deployForm.imageName = ''\n        this.deployForm.imageTag = 'latest'\n      }\n    },\n    \n    // 智能生成容器名称\n    suggestContainerName(imageName, imageTag) {\n      // 移除镜像名称中的特殊字符，生成简洁的名称\n      let baseName = imageName.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase()\n      \n      // 如果标签不是latest，则加入标签\n      if (imageTag && imageTag !== 'latest') {\n        baseName += '-' + imageTag.replace(/[^a-zA-Z0-9]/g, '-')\n      }\n      \n      // 添加时间戳保证唯一性\n      const timestamp = Date.now().toString().slice(-6)\n      this.deployForm.containerName = `${baseName}-${timestamp}`\n    },\n    \n    // 根据镜像类型智能推荐端口\n    suggestServicePort(imageName) {\n      const portMap = {\n        'nginx': 80,\n        'apache': 80,\n        'httpd': 80,\n        'mysql': 3306,\n        'mariadb': 3306,\n        'postgres': 5432,\n        'postgresql': 5432,\n        'redis': 6379,\n        'mongodb': 27017,\n        'mongo': 27017,\n        'tomcat': 8080,\n        'node': 3000,\n        'spring': 8080,\n        'java': 8080\n      }\n      \n      // 查找匹配的镜像类型\n      for (const [key, port] of Object.entries(portMap)) {\n        if (imageName.toLowerCase().includes(key)) {\n          this.deployForm.servicePort = port\n          break\n        }\n      }\n    },\n    \n    // 格式化镜像大小\n    formatImageSize(sizeMb) {\n      if (!sizeMb || sizeMb === 0) return '-'\n      \n      // 确保是数字类型\n      const size = typeof sizeMb === 'string' ? parseInt(sizeMb) : sizeMb\n      if (isNaN(size)) return '-'\n      \n      // 如果已经是MB单位，直接显示\n      if (size < 1024) {\n        return `${size} MB`\n      }\n      \n      // 转换为GB\n      const sizeGb = size / 1024\n      return `${sizeGb.toFixed(1)} GB`\n    },\n    \n    // 获取分布策略显示名称\n    getStrategyDisplayName(strategy) {\n      const strategyMap = {\n        'SPREAD_ACROSS_NODES': '跨节点分散',\n        'WORKER_NODES_ONLY': '仅Worker节点',\n        'MANAGER_NODES_ONLY': '仅Manager节点',\n        'BALANCED': '平衡分布',\n        'SPREAD_ACROSS_ZONES': '跨可用区分散'\n      }\n      return strategyMap[strategy] || strategy\n    },\n    \n    // 获取推荐的分布策略\n    async getRecommendedDistributionStrategy(replicas) {\n      try {\n        const response = await getRecommendedStrategy(replicas)\n        if (response.code === 20000 && response.data) {\n          return response.data.recommendedStrategy\n        }\n      } catch (error) {\n        console.warn('获取推荐分布策略失败:', error)\n      }\n      return 'SPREAD_ACROSS_NODES' // 默认值\n    },\n    \n    // === HSM设备相关方法 ===\n    \n    // 处理HSM开关变化\n    handleHsmToggle(enabled) {\n      if (enabled) {\n        // 启用HSM时加载设备列表\n        this.loadHsmDeviceList()\n      } else {\n        // 禁用HSM时清空选择\n        this.deployForm.hsmDeviceId = null\n        this.selectedHsmDevice = null\n      }\n    },\n    \n    // 加载HSM设备列表\n    async loadHsmDeviceList() {\n      this.hsmDeviceListLoading = true\n      try {\n        const response = await getAvailableHsmDevices({\n          status: 'running'\n        })\n        \n        if (response.code === 20000) {\n          if (response.data && response.data.list) {\n            this.hsmDeviceList = response.data.list\n          } else if (Array.isArray(response.data)) {\n            this.hsmDeviceList = response.data\n          } else {\n            this.hsmDeviceList = []\n          }\n          \n          if (this.hsmDeviceList.length === 0) {\n            this.$message.info('当前没有可用的HSM设备')\n          }\n        } else {\n          this.$message.error('获取HSM设备列表失败：' + (response.message || '未知错误'))\n          this.hsmDeviceList = []\n        }\n      } catch (error) {\n        console.error('加载HSM设备列表失败:', error)\n        this.$message.error('加载HSM设备列表失败')\n        this.hsmDeviceList = []\n      } finally {\n        this.hsmDeviceListLoading = false\n      }\n    },\n    \n    // 处理HSM设备选择变化\n    handleHsmDeviceChange(deviceId) {\n      if (deviceId) {\n        this.selectedHsmDevice = this.hsmDeviceList.find(device => device.deviceId === deviceId)\n        if (this.selectedHsmDevice) {\n          // 可以在这里加载更详细的设备信息\n          this.loadHsmDeviceDetail(deviceId)\n        }\n      } else {\n        this.selectedHsmDevice = null\n      }\n    },\n    \n    // 加载HSM设备详细信息\n    async loadHsmDeviceDetail(deviceId) {\n      try {\n        const response = await getHsmDeviceDetail(deviceId)\n        if (response.code === 20000 && response.data) {\n          this.selectedHsmDevice = response.data\n        }\n      } catch (error) {\n        console.warn('获取HSM设备详细信息失败:', error)\n      }\n    },\n    \n    // 带HSM配置的部署方法\n    async deployWithHsm(deployData) {\n      return deployContainerWithHsm(deployData)\n    }\n  },\n  mounted() {\n    this.getList()\n    // 预加载镜像列表，提升用户体验\n    this.loadImageList()\n    // 预加载HSM设备列表\n    this.loadHsmDeviceList()\n  },\n  computed: {\n    ...mapGetters([\n      'user'\n    ])\n  },\n  watch: {\n    // 监听副本数变化，自动更新推荐的分布策略\n    'deployForm.replicas'(newReplicas, oldReplicas) {\n      if (newReplicas > 1 && newReplicas !== oldReplicas) {\n        this.getRecommendedDistributionStrategy(newReplicas).then(strategy => {\n          this.deployForm.distributionStrategy = strategy\n        })\n      } else if (newReplicas === 1) {\n        // 单副本时不需要分布策略\n        this.deployForm.distributionStrategy = 'BALANCED'\n      }\n    },\n    // 监听扩缩容副本数变化\n    'scaleForm.replicas'(newReplicas, oldReplicas) {\n      if (newReplicas > 1 && newReplicas !== oldReplicas) {\n        this.getRecommendedDistributionStrategy(newReplicas).then(strategy => {\n          this.scaleForm.distributionStrategy = strategy\n        })\n      } else if (newReplicas === 1) {\n        this.scaleForm.distributionStrategy = 'BALANCED'\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.filter-inner {\n  display: flex;\n  align-items: center;\n  flex-wrap: wrap;\n  \n  .el-form-item {\n    margin-bottom: 10px;\n    margin-right: 15px;\n  }\n}\n\n.pull-right {\n  float: right;\n}\n\n.clearfix::after {\n  content: \"\";\n  display: table;\n  clear: both;\n}\n\n.form-help {\n  font-size: 12px;\n  color: #909399;\n  margin-top: 4px;\n}\n\n.el-link {\n  font-size: 12px;\n  max-width: 150px;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  display: inline-block;\n}\n\n.image-info {\n  background-color: #f5f7fa;\n  padding: 12px;\n  border-radius: 4px;\n  border: 1px solid #e4e7ed;\n  \n  p {\n    margin: 4px 0;\n    font-size: 13px;\n    \n    strong {\n      color: #303133;\n      font-weight: 500;\n    }\n  }\n}\n\n.device-info {\n  background-color: #f0f9ff;\n  padding: 12px;\n  border-radius: 4px;\n  border: 1px solid #a7f3d0;\n  \n  p {\n    margin: 4px 0;\n    font-size: 13px;\n    \n    strong {\n      color: #303133;\n      font-weight: 500;\n    }\n  }\n}\n\n.strategy-info {\n  font-size: 11px;\n  color: #909399;\n  margin-top: 2px;\n}\n\n.hsm-info {\n  font-size: 11px;\n  color: #67c23a;\n  margin-top: 2px;\n}\n</style>"]}]}