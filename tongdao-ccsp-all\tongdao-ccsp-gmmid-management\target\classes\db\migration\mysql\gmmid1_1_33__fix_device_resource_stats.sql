-- 设备资源使用统计数据修复脚本
-- 版本: gmmid1_1_33
-- 描述: 修复设备资源使用统计数据，确保所有现有设备都有对应的统计记录

-- 1. 为现有VSM设备创建或更新统计记录
INSERT INTO gmmid_device_resource_usage_stats 
(device_resource_id, device_resource_type, total_capacity, used_capacity, available_capacity, active_containers_count, create_time, update_time)
SELECT 
    v.id as device_resource_id,
    'VSM' as device_resource_type,
    1 as total_capacity,
    COALESCE(rel_count.cnt, 0) as used_capacity,
    1 - COALESCE(rel_count.cnt, 0) as available_capacity,
    COALESCE(rel_count.cnt, 0) as active_containers_count,
    NOW() as create_time,
    NOW() as update_time
FROM gmmid_vsm v
LEFT JOIN (
    SELECT device_resource_id, COUNT(*) as cnt
    FROM gmmid_container_device_resource_rel 
    WHERE device_resource_type = 'VSM' AND status = 'active'
    GROUP BY device_resource_id
) rel_count ON v.id = rel_count.device_resource_id
WHERE v.status = 'normal'
ON DUPLICATE KEY UPDATE 
    used_capacity = COALESCE(rel_count.cnt, 0),
    available_capacity = 1 - COALESCE(rel_count.cnt, 0),
    active_containers_count = COALESCE(rel_count.cnt, 0),
    update_time = NOW();

-- 2. 为现有CHSM设备创建或更新统计记录
INSERT INTO gmmid_device_resource_usage_stats 
(device_resource_id, device_resource_type, total_capacity, used_capacity, available_capacity, active_containers_count, create_time, update_time)
SELECT 
    c.id as device_resource_id,
    'CHSM' as device_resource_type,
    COALESCE(c.vsm_capacity, 10) as total_capacity,
    COALESCE(rel_count.cnt, 0) as used_capacity,
    COALESCE(c.vsm_capacity, 10) - COALESCE(rel_count.cnt, 0) as available_capacity,
    COALESCE(rel_count.cnt, 0) as active_containers_count,
    NOW() as create_time,
    NOW() as update_time
FROM gmmid_chsm c
LEFT JOIN (
    SELECT device_resource_id, COUNT(*) as cnt
    FROM gmmid_container_device_resource_rel 
    WHERE device_resource_type = 'CHSM' AND status = 'active'
    GROUP BY device_resource_id
) rel_count ON c.id = rel_count.device_resource_id
WHERE c.status = 'normal'
ON DUPLICATE KEY UPDATE 
    total_capacity = COALESCE(c.vsm_capacity, 10),
    used_capacity = COALESCE(rel_count.cnt, 0),
    available_capacity = COALESCE(c.vsm_capacity, 10) - COALESCE(rel_count.cnt, 0),
    active_containers_count = COALESCE(rel_count.cnt, 0),
    update_time = NOW();

-- 3. 更新最后分配时间和释放时间
UPDATE gmmid_device_resource_usage_stats s
LEFT JOIN (
    SELECT 
        device_resource_id,
        device_resource_type,
        MAX(allocated_at) as last_allocated_at
    FROM gmmid_container_device_resource_rel 
    WHERE status = 'active' AND allocated_at IS NOT NULL
    GROUP BY device_resource_id, device_resource_type
) active_rel ON s.device_resource_id = active_rel.device_resource_id 
    AND s.device_resource_type = active_rel.device_resource_type
LEFT JOIN (
    SELECT 
        device_resource_id,
        device_resource_type,
        MAX(released_at) as last_released_at
    FROM gmmid_container_device_resource_rel 
    WHERE status = 'inactive' AND released_at IS NOT NULL
    GROUP BY device_resource_id, device_resource_type
) inactive_rel ON s.device_resource_id = inactive_rel.device_resource_id 
    AND s.device_resource_type = inactive_rel.device_resource_type
SET 
    s.last_allocated_at = active_rel.last_allocated_at,
    s.last_released_at = inactive_rel.last_released_at,
    s.update_time = NOW()
WHERE active_rel.last_allocated_at IS NOT NULL OR inactive_rel.last_released_at IS NOT NULL;

-- 4. 添加唯一索引确保数据一致性（如果不存在）
ALTER TABLE gmmid_device_resource_usage_stats 
ADD UNIQUE INDEX uk_device_resource_type (device_resource_id, device_resource_type);

-- 5. 验证数据一致性的查询（可选，用于验证修复结果）
-- 查询统计数据与实际关联数据不一致的记录
SELECT 
    s.device_resource_id,
    s.device_resource_type,
    s.active_containers_count as stats_count,
    COALESCE(r.actual_count, 0) as actual_count,
    s.used_capacity,
    s.available_capacity,
    s.total_capacity
FROM gmmid_device_resource_usage_stats s
LEFT JOIN (
    SELECT 
        device_resource_id,
        device_resource_type,
        COUNT(*) as actual_count
    FROM gmmid_container_device_resource_rel 
    WHERE status = 'active'
    GROUP BY device_resource_id, device_resource_type
) r ON s.device_resource_id = r.device_resource_id 
    AND s.device_resource_type = r.device_resource_type
WHERE s.active_containers_count != COALESCE(r.actual_count, 0)
   OR s.used_capacity != COALESCE(r.actual_count, 0)
   OR s.available_capacity != (s.total_capacity - COALESCE(r.actual_count, 0));

-- 6. 创建定期数据一致性检查的存储过程（可选）
DELIMITER //

CREATE PROCEDURE CheckDeviceResourceStatsConsistency()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE v_device_id BIGINT;
    DECLARE v_device_type VARCHAR(20);
    DECLARE v_actual_count INT;
    DECLARE v_total_capacity INT;
    
    DECLARE cur CURSOR FOR 
        SELECT DISTINCT device_resource_id, device_resource_type 
        FROM gmmid_device_resource_usage_stats;
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    OPEN cur;
    
    read_loop: LOOP
        FETCH cur INTO v_device_id, v_device_type;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        -- 获取实际的活跃容器数量
        SELECT COUNT(*) INTO v_actual_count
        FROM gmmid_container_device_resource_rel
        WHERE device_resource_id = v_device_id 
          AND device_resource_type = v_device_type 
          AND status = 'active';
        
        -- 获取总容量
        SELECT total_capacity INTO v_total_capacity
        FROM gmmid_device_resource_usage_stats
        WHERE device_resource_id = v_device_id 
          AND device_resource_type = v_device_type;
        
        -- 更新统计数据
        UPDATE gmmid_device_resource_usage_stats
        SET 
            used_capacity = v_actual_count,
            available_capacity = v_total_capacity - v_actual_count,
            active_containers_count = v_actual_count,
            update_time = NOW()
        WHERE device_resource_id = v_device_id 
          AND device_resource_type = v_device_type;
        
    END LOOP;
    
    CLOSE cur;
END //

DELIMITER ;

-- 7. 创建定时任务调用存储过程（需要管理员权限，可选）
-- CREATE EVENT IF NOT EXISTS evt_check_device_stats
-- ON SCHEDULE EVERY 1 HOUR
-- DO
--   CALL CheckDeviceResourceStatsConsistency();
