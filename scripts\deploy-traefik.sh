#!/bin/bash

# Traefik Docker Swarm 部署脚本
# 用途：在Docker Swarm集群中部署Traefik反向代理
# 作者：System
# 版本：1.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
TRAEFIK_DIR="/opt/traefik"
TRAEFIK_VERSION="v3.0"
NETWORK_NAME="ccsp-network"
STACK_NAME="traefik"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户或有sudo权限
check_permissions() {
    if [[ $EUID -eq 0 ]]; then
        log_info "以root用户运行"
    elif sudo -n true 2>/dev/null; then
        log_info "检测到sudo权限"
    else
        log_error "需要root权限或sudo权限来执行此脚本"
        exit 1
    fi
}

# 检查Docker Swarm状态
check_swarm_status() {
    log_info "检查Docker Swarm状态..."
    
    if ! docker info --format '{{.Swarm.LocalNodeState}}' | grep -q "active"; then
        log_error "Docker Swarm未初始化或节点未加入集群"
        log_info "请先初始化Swarm集群："
        echo "  docker swarm init --advertise-addr <MANAGER-IP>"
        exit 1
    fi
    
    local node_role=$(docker info --format '{{.Swarm.ControlAvailable}}')
    if [[ "$node_role" != "true" ]]; then
        log_error "当前节点不是Manager节点，Traefik需要部署在Manager节点上"
        exit 1
    fi
    
    log_success "Docker Swarm状态正常，当前节点为Manager节点"
}

# 创建或检查overlay网络
setup_network() {
    log_info "设置overlay网络: $NETWORK_NAME"
    
    if docker network ls --format '{{.Name}}' | grep -q "^${NETWORK_NAME}$"; then
        log_success "网络 $NETWORK_NAME 已存在"
    else
        log_info "创建overlay网络: $NETWORK_NAME"
        docker network create \
            --driver overlay \
            --attachable \
            --subnet=**********/16 \
            $NETWORK_NAME
        log_success "网络 $NETWORK_NAME 创建成功"
    fi
}

# 创建配置目录结构
setup_directories() {
    log_info "创建Traefik配置目录..."
    
    # 创建目录
    sudo mkdir -p $TRAEFIK_DIR/{config,data,logs}
    
    # 设置权限
    sudo chmod 755 $TRAEFIK_DIR
    sudo chown -R $(whoami):$(whoami) $TRAEFIK_DIR
    
    log_success "目录结构创建完成: $TRAEFIK_DIR"
}

# 生成Traefik静态配置
generate_static_config() {
    log_info "生成Traefik静态配置文件..."
    
    cat > $TRAEFIK_DIR/config/traefik.yml << 'EOF'
# Traefik静态配置
global:
  checkNewVersion: false
  sendAnonymousUsage: false

# API和Dashboard配置
api:
  dashboard: true
  debug: true
  insecure: true  # 生产环境建议设置为false并配置TLS

# 入口点配置
entryPoints:
  web:
    address: ":80"
  websecure:
    address: ":443"
  traefik:
    address: ":8080"  # Dashboard端口

# 提供者配置
providers:
  # Docker Swarm提供者
  swarm:
    endpoint: "unix:///var/run/docker.sock"
    network: "ccsp-network"
    exposedByDefault: false
    watch: true
    swarmMode: true
    swarmModeRefreshSeconds: 15
    
  # 文件提供者（用于静态路由）
  file:
    filename: /etc/traefik/dynamic.yml
    watch: true

# 日志配置
log:
  level: INFO
  filePath: "/var/log/traefik/traefik.log"
  format: json

# 访问日志
accessLog:
  filePath: "/var/log/traefik/access.log"
  format: json

# 指标配置
metrics:
  prometheus:
    addEntryPointsLabels: true
    addServicesLabels: true

# 证书解析器（HTTPS支持）
certificatesResolvers:
  letsencrypt:
    acme:
      email: <EMAIL>
      storage: /data/acme.json
      httpChallenge:
        entryPoint: web
EOF
    
    log_success "静态配置文件生成完成"
}

# 生成Traefik动态配置
generate_dynamic_config() {
    log_info "生成Traefik动态配置文件..."
    
    cat > $TRAEFIK_DIR/config/dynamic.yml << 'EOF'
# Traefik动态配置
http:
  # 中间件定义
  middlewares:
    # 默认头部中间件
    default-headers:
      headers:
        frameDeny: true
        sslRedirect: false  # 开发环境设置为false
        browserXssFilter: true
        contentTypeNosniff: true
        forceSTSHeader: false  # 开发环境设置为false
        stsIncludeSubdomains: true
        stsPreload: true
        stsSeconds: 31536000
        
    # CORS中间件
    cors-headers:
      headers:
        accessControlAllowMethods:
          - GET
          - OPTIONS
          - PUT
          - POST
          - DELETE
        accessControlAllowOriginList:
          - "*"
        accessControlMaxAge: 100
        addVaryHeader: true

  # 路由定义
  routers:
    # Traefik Dashboard路由
    traefik-dashboard:
      rule: "Host(`traefik.ccsp.local`)"
      service: api@internal
      entryPoints:
        - web

# TLS配置
tls:
  options:
    default:
      sslProtocols:
        - "TLSv1.2"
        - "TLSv1.3"
      minVersion: "VersionTLS12"
EOF
    
    log_success "动态配置文件生成完成"
}

# 生成Docker Compose文件
generate_compose_file() {
    log_info "生成Docker Compose文件..."
    
    cat > $TRAEFIK_DIR/docker-compose.yml << EOF
version: '3.8'

services:
  traefik:
    image: traefik:${TRAEFIK_VERSION}
    command:
      - --configfile=/etc/traefik/traefik.yml
    ports:
      - "80:80"      # HTTP
      - "443:443"    # HTTPS
      - "8080:8080"  # Dashboard
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./config/traefik.yml:/etc/traefik/traefik.yml:ro
      - ./config/dynamic.yml:/etc/traefik/dynamic.yml:ro
      - ./data:/data
      - ./logs:/var/log/traefik
    networks:
      - ${NETWORK_NAME}
    deploy:
      mode: replicated
      replicas: 1
      placement:
        constraints:
          - node.role == manager
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s
      update_config:
        parallelism: 1
        delay: 10s
        failure_action: rollback
        monitor: 60s
        max_failure_ratio: 0.3
      labels:
        - "traefik.enable=true"
        - "traefik.http.routers.traefik.rule=Host(\`traefik.ccsp.local\`)"
        - "traefik.http.routers.traefik.entrypoints=web"
        - "traefik.http.services.traefik.loadbalancer.server.port=8080"
        - "traefik.docker.network=${NETWORK_NAME}"
    environment:
      - TRAEFIK_LOG_LEVEL=INFO
    healthcheck:
      test: ["CMD", "traefik", "healthcheck", "--ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  ${NETWORK_NAME}:
    external: true
EOF
    
    log_success "Docker Compose文件生成完成"
}

# 部署Traefik服务
deploy_traefik() {
    log_info "部署Traefik服务..."
    
    cd $TRAEFIK_DIR
    
    # 部署stack
    docker stack deploy -c docker-compose.yml $STACK_NAME
    
    log_success "Traefik服务部署完成"
}

# 验证部署状态
verify_deployment() {
    log_info "验证Traefik部署状态..."
    
    # 等待服务启动
    sleep 10
    
    # 检查服务状态
    local service_status=$(docker service ls --filter name=${STACK_NAME}_traefik --format "{{.Replicas}}")
    if [[ "$service_status" == "1/1" ]]; then
        log_success "Traefik服务运行正常"
    else
        log_warning "Traefik服务状态: $service_status"
        log_info "查看服务日志："
        docker service logs ${STACK_NAME}_traefik --tail 20
    fi
    
    # 测试API访问
    if curl -s http://localhost:8080/ping > /dev/null; then
        log_success "Traefik API访问正常"
    else
        log_warning "Traefik API访问失败，请检查服务状态"
    fi
}

# 配置DNS解析
setup_dns() {
    log_info "配置本地DNS解析..."
    
    # 检查/etc/hosts中是否已存在配置
    if grep -q "traefik.ccsp.local" /etc/hosts; then
        log_info "DNS解析已配置"
    else
        echo "127.0.0.1 traefik.ccsp.local" | sudo tee -a /etc/hosts
        log_success "已添加DNS解析: traefik.ccsp.local -> 127.0.0.1"
    fi
}

# 显示部署信息
show_deployment_info() {
    log_success "Traefik部署完成！"
    echo
    echo "=== 部署信息 ==="
    echo "Traefik版本: $TRAEFIK_VERSION"
    echo "配置目录: $TRAEFIK_DIR"
    echo "网络名称: $NETWORK_NAME"
    echo "Stack名称: $STACK_NAME"
    echo
    echo "=== 访问地址 ==="
    echo "Dashboard: http://traefik.ccsp.local:8080"
    echo "API: http://localhost:8080/api/rawdata"
    echo "健康检查: http://localhost:8080/ping"
    echo
    echo "=== 常用命令 ==="
    echo "查看服务状态: docker service ls | grep traefik"
    echo "查看服务日志: docker service logs ${STACK_NAME}_traefik -f"
    echo "更新服务: docker stack deploy -c $TRAEFIK_DIR/docker-compose.yml $STACK_NAME"
    echo "删除服务: docker stack rm $STACK_NAME"
    echo
    echo "=== 测试命令 ==="
    echo "curl -H \"Host: traefik.ccsp.local\" http://localhost:8080/api/rawdata"
}

# 主函数
main() {
    log_info "开始部署Traefik到Docker Swarm集群..."
    
    check_permissions
    check_swarm_status
    setup_network
    setup_directories
    generate_static_config
    generate_dynamic_config
    generate_compose_file
    deploy_traefik
    verify_deployment
    setup_dns
    show_deployment_info
    
    log_success "Traefik部署脚本执行完成！"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
