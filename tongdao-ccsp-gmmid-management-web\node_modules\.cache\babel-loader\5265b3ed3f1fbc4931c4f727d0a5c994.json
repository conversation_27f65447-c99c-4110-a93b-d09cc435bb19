{"remainingRequest": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\src\\api\\docker\\container.js", "dependencies": [{"path": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\src\\api\\docker\\container.js", "mtime": 1756805064419}, {"path": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\babel.config.js", "mtime": 1699511114284}, {"path": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1729062151209}, {"path": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1729062151198}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "getContainerList", "params", "url", "method", "deployContainer", "data", "startContainer", "id", "stopContainer", "restartContainer", "remove<PERSON><PERSON><PERSON>", "syncContainerStatus", "getContainersByImage", "imageId", "getContainersByNode", "nodeId", "getRunningContainers", "scaleContainer", "replicas", "updateContainerImage", "newImageId", "getContainerLogs", "lines", "arguments", "length", "undefined", "getContainerDetail", "concat", "deployContainerWithTraefik", "console", "warn", "getContainerAccessUrl", "getAvailableDeviceResources", "getContainerDeviceResources", "getDeviceResourceUsageStats", "deviceResourceId", "deviceResourceType", "getRecommendedDeviceResources", "configContainerRoute", "updateContainerRoute", "removeContainerRoute", "deployContainerWithStrategy", "getRecommendedStrategy", "availableNodes"], "sources": ["D:/workspace/ccsp_v3/tongdao-ccsp-gmmid-management-web/src/api/docker/container.js"], "sourcesContent": ["import request from '@/utils/request'\n\n/**\n * Docker Swarm 容器实例管理 API\n * \n * 本模块对应后端 ContainerInstanceController\n * 基础路径: /api-management/swarm/container/v1\n * \n * 功能包括：\n * - 容器生命周期管理（部署、启动、停止、重启、删除）\n * - 容器扩缩容和镜像更新\n * - 容器状态同步和日志获取\n * - 按条件查询容器（镜像、节点）\n */\n\n// 分页查询容器实例列表\nexport function getContainerList(params) {\n  return request({\n    url: '/api-management/swarm/container/v1/list-page',\n    method: 'get',\n    params\n  })\n}\n\n// 部署容器\nexport function deployContainer(data) {\n  return request({\n    url: '/api-management/swarm/container/v1/deploy',\n    method: 'post',\n    data\n  })\n}\n\n// 启动容器\nexport function startContainer(id) {\n  return request({\n    url: '/api-management/swarm/container/v1/start',\n    method: 'post',\n    params: { id }\n  })\n}\n\n// 停止容器\nexport function stopContainer(id) {\n  return request({\n    url: '/api-management/swarm/container/v1/stop',\n    method: 'post',\n    params: { id }\n  })\n}\n\n// 重启容器\nexport function restartContainer(id) {\n  return request({\n    url: '/api-management/swarm/container/v1/restart',\n    method: 'post',\n    params: { id }\n  })\n}\n\n// 删除容器\nexport function removeContainer(id) {\n  return request({\n    url: '/api-management/swarm/container/v1/delete',\n    method: 'post',\n    params: { id }\n  })\n}\n\n// 同步容器状态\nexport function syncContainerStatus() {\n  return request({\n    url: '/api-management/swarm/container/v1/sync-status',\n    method: 'post'\n  })\n}\n\n// 根据镜像ID查询容器列表\nexport function getContainersByImage(imageId) {\n  return request({\n    url: '/api-management/swarm/container/v1/by-image',\n    method: 'get',\n    params: { imageId }\n  })\n}\n\n// 根据节点ID查询容器列表\nexport function getContainersByNode(nodeId) {\n  return request({\n    url: '/api-management/swarm/container/v1/by-node',\n    method: 'get',\n    params: { nodeId }\n  })\n}\n\n// 获取运行中的容器列表\nexport function getRunningContainers() {\n  return request({\n    url: '/api-management/swarm/container/v1/running',\n    method: 'get'\n  })\n}\n\n// 扩缩容操作\nexport function scaleContainer(id, replicas) {\n  return request({\n    url: '/api-management/swarm/container/v1/scale',\n    method: 'post',\n    params: { id, replicas }\n  })\n}\n\n// 更新容器镜像\nexport function updateContainerImage(id, newImageId) {\n  return request({\n    url: '/api-management/swarm/container/v1/update-image',\n    method: 'post',\n    params: { id, newImageId }\n  })\n}\n\n// 获取容器日志\nexport function getContainerLogs(id, lines = 100) {\n  return request({\n    url: '/api-management/swarm/container/v1/logs',\n    method: 'get',\n    params: { id, lines }\n  })\n}\n\n// 根据ID查询容器实例详情\nexport function getContainerDetail(id) {\n  return request({\n    url: `/api-management/swarm/container/v1/${id}`,\n    method: 'get'\n  })\n}\n\n// Traefik相关API\n\n// 带Traefik配置的容器部署\n// @deprecated 请使用统一的 deployContainer 方法\nexport function deployContainerWithTraefik(data) {\n  console.warn('deployContainerWithTraefik 已弃用，请使用 deployContainer')\n  return deployContainer(data)\n}\n\n// 获取容器访问地址\nexport function getContainerAccessUrl(id) {\n  return request({\n    url: `/api-management/swarm/container/v1/${id}/access-url`,\n    method: 'get'\n  })\n}\n\n// 获取可用的设备资源列表\nexport function getAvailableDeviceResources(params) {\n  return request({\n    url: '/api-management/swarm/container/v1/device-resources',\n    method: 'get',\n    params\n  })\n}\n\n// 获取容器关联的设备资源列表\nexport function getContainerDeviceResources(id) {\n  return request({\n    url: `/api-management/swarm/container/v1/${id}/device-resources`,\n    method: 'get'\n  })\n}\n\n// 获取设备资源使用统计\nexport function getDeviceResourceUsageStats(deviceResourceId, deviceResourceType) {\n  return request({\n    url: `/api-management/swarm/container/v1/device-resources/${deviceResourceId}/stats`,\n    method: 'get',\n    params: { deviceResourceType }\n  })\n}\n\n// 获取推荐的设备资源\nexport function getRecommendedDeviceResources(params) {\n  return request({\n    url: '/api-management/swarm/container/v1/device-resources/recommend',\n    method: 'get',\n    params\n  })\n}\n\n// 配置容器Traefik路由\nexport function configContainerRoute(data) {\n  return request({\n    url: '/api-management/swarm/container/v1/configure-traefik',\n    method: 'post',\n    data\n  })\n}\n\n// 更新容器Traefik路由\nexport function updateContainerRoute(data) {\n  return request({\n    url: '/api-management/swarm/container/v1/configure-traefik',\n    method: 'post',\n    data\n  })\n}\n\n// 移除容器Traefik配置\nexport function removeContainerRoute(id) {\n  return request({\n    url: '/api-management/swarm/container/v1/remove-traefik',\n    method: 'post',\n    params: { id }\n  })\n}\n\n// 分布策略相关API\n\n// 带分布策略的容器部署\n// @deprecated 请使用统一的 deployContainer 方法\nexport function deployContainerWithStrategy(data) {\n  console.warn('deployContainerWithStrategy 已弃用，请使用 deployContainer')\n  return deployContainer(data)\n}\n\n// 获取推荐的分布策略\nexport function getRecommendedStrategy(replicas, availableNodes) {\n  const params = { replicas }\n  if (availableNodes !== undefined) {\n    params.availableNodes = availableNodes\n  }\n  \n  return request({\n    url: '/api-management/swarm/container/v1/recommended-strategy',\n    method: 'get',\n    params\n  })\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,OAAO,SAASC,gBAAgBA,CAACC,MAAM,EAAE;EACvC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,8CAA8C;IACnDC,MAAM,EAAE,KAAK;IACbF,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASG,eAAeA,CAACC,IAAI,EAAE;EACpC,OAAON,OAAO,CAAC;IACbG,GAAG,EAAE,2CAA2C;IAChDC,MAAM,EAAE,MAAM;IACdE,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASC,cAAcA,CAACC,EAAE,EAAE;EACjC,OAAOR,OAAO,CAAC;IACbG,GAAG,EAAE,0CAA0C;IAC/CC,MAAM,EAAE,MAAM;IACdF,MAAM,EAAE;MAAEM,EAAE,EAAFA;IAAG;EACf,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASC,aAAaA,CAACD,EAAE,EAAE;EAChC,OAAOR,OAAO,CAAC;IACbG,GAAG,EAAE,yCAAyC;IAC9CC,MAAM,EAAE,MAAM;IACdF,MAAM,EAAE;MAAEM,EAAE,EAAFA;IAAG;EACf,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASE,gBAAgBA,CAACF,EAAE,EAAE;EACnC,OAAOR,OAAO,CAAC;IACbG,GAAG,EAAE,4CAA4C;IACjDC,MAAM,EAAE,MAAM;IACdF,MAAM,EAAE;MAAEM,EAAE,EAAFA;IAAG;EACf,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASG,eAAeA,CAACH,EAAE,EAAE;EAClC,OAAOR,OAAO,CAAC;IACbG,GAAG,EAAE,2CAA2C;IAChDC,MAAM,EAAE,MAAM;IACdF,MAAM,EAAE;MAAEM,EAAE,EAAFA;IAAG;EACf,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,mBAAmBA,CAAA,EAAG;EACpC,OAAOZ,OAAO,CAAC;IACbG,GAAG,EAAE,gDAAgD;IACrDC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASS,oBAAoBA,CAACC,OAAO,EAAE;EAC5C,OAAOd,OAAO,CAAC;IACbG,GAAG,EAAE,6CAA6C;IAClDC,MAAM,EAAE,KAAK;IACbF,MAAM,EAAE;MAAEY,OAAO,EAAPA;IAAQ;EACpB,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASC,mBAAmBA,CAACC,MAAM,EAAE;EAC1C,OAAOhB,OAAO,CAAC;IACbG,GAAG,EAAE,4CAA4C;IACjDC,MAAM,EAAE,KAAK;IACbF,MAAM,EAAE;MAAEc,MAAM,EAANA;IAAO;EACnB,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASC,oBAAoBA,CAAA,EAAG;EACrC,OAAOjB,OAAO,CAAC;IACbG,GAAG,EAAE,4CAA4C;IACjDC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASc,cAAcA,CAACV,EAAE,EAAEW,QAAQ,EAAE;EAC3C,OAAOnB,OAAO,CAAC;IACbG,GAAG,EAAE,0CAA0C;IAC/CC,MAAM,EAAE,MAAM;IACdF,MAAM,EAAE;MAAEM,EAAE,EAAFA,EAAE;MAAEW,QAAQ,EAARA;IAAS;EACzB,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASC,oBAAoBA,CAACZ,EAAE,EAAEa,UAAU,EAAE;EACnD,OAAOrB,OAAO,CAAC;IACbG,GAAG,EAAE,iDAAiD;IACtDC,MAAM,EAAE,MAAM;IACdF,MAAM,EAAE;MAAEM,EAAE,EAAFA,EAAE;MAAEa,UAAU,EAAVA;IAAW;EAC3B,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASC,gBAAgBA,CAACd,EAAE,EAAe;EAAA,IAAbe,KAAK,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,GAAG;EAC9C,OAAOxB,OAAO,CAAC;IACbG,GAAG,EAAE,yCAAyC;IAC9CC,MAAM,EAAE,KAAK;IACbF,MAAM,EAAE;MAAEM,EAAE,EAAFA,EAAE;MAAEe,KAAK,EAALA;IAAM;EACtB,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,kBAAkBA,CAACnB,EAAE,EAAE;EACrC,OAAOR,OAAO,CAAC;IACbG,GAAG,wCAAAyB,MAAA,CAAwCpB,EAAE,CAAE;IAC/CJ,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;;AAEA;AACA;AACA,OAAO,SAASyB,0BAA0BA,CAACvB,IAAI,EAAE;EAC/CwB,OAAO,CAACC,IAAI,CAAC,oDAAoD,CAAC;EAClE,OAAO1B,eAAe,CAACC,IAAI,CAAC;AAC9B;;AAEA;AACA,OAAO,SAAS0B,qBAAqBA,CAACxB,EAAE,EAAE;EACxC,OAAOR,OAAO,CAAC;IACbG,GAAG,wCAAAyB,MAAA,CAAwCpB,EAAE,gBAAa;IAC1DJ,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAAS6B,2BAA2BA,CAAC/B,MAAM,EAAE;EAClD,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,qDAAqD;IAC1DC,MAAM,EAAE,KAAK;IACbF,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASgC,2BAA2BA,CAAC1B,EAAE,EAAE;EAC9C,OAAOR,OAAO,CAAC;IACbG,GAAG,wCAAAyB,MAAA,CAAwCpB,EAAE,sBAAmB;IAChEJ,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAAS+B,2BAA2BA,CAACC,gBAAgB,EAAEC,kBAAkB,EAAE;EAChF,OAAOrC,OAAO,CAAC;IACbG,GAAG,yDAAAyB,MAAA,CAAyDQ,gBAAgB,WAAQ;IACpFhC,MAAM,EAAE,KAAK;IACbF,MAAM,EAAE;MAAEmC,kBAAkB,EAAlBA;IAAmB;EAC/B,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASC,6BAA6BA,CAACpC,MAAM,EAAE;EACpD,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,+DAA+D;IACpEC,MAAM,EAAE,KAAK;IACbF,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASqC,oBAAoBA,CAACjC,IAAI,EAAE;EACzC,OAAON,OAAO,CAAC;IACbG,GAAG,EAAE,sDAAsD;IAC3DC,MAAM,EAAE,MAAM;IACdE,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASkC,oBAAoBA,CAAClC,IAAI,EAAE;EACzC,OAAON,OAAO,CAAC;IACbG,GAAG,EAAE,sDAAsD;IAC3DC,MAAM,EAAE,MAAM;IACdE,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASmC,oBAAoBA,CAACjC,EAAE,EAAE;EACvC,OAAOR,OAAO,CAAC;IACbG,GAAG,EAAE,mDAAmD;IACxDC,MAAM,EAAE,MAAM;IACdF,MAAM,EAAE;MAAEM,EAAE,EAAFA;IAAG;EACf,CAAC,CAAC;AACJ;;AAEA;;AAEA;AACA;AACA,OAAO,SAASkC,2BAA2BA,CAACpC,IAAI,EAAE;EAChDwB,OAAO,CAACC,IAAI,CAAC,qDAAqD,CAAC;EACnE,OAAO1B,eAAe,CAACC,IAAI,CAAC;AAC9B;;AAEA;AACA,OAAO,SAASqC,sBAAsBA,CAACxB,QAAQ,EAAEyB,cAAc,EAAE;EAC/D,IAAM1C,MAAM,GAAG;IAAEiB,QAAQ,EAARA;EAAS,CAAC;EAC3B,IAAIyB,cAAc,KAAKlB,SAAS,EAAE;IAChCxB,MAAM,CAAC0C,cAAc,GAAGA,cAAc;EACxC;EAEA,OAAO5C,OAAO,CAAC;IACbG,GAAG,EAAE,yDAAyD;IAC9DC,MAAM,EAAE,KAAK;IACbF,MAAM,EAANA;EACF,CAAC,CAAC;AACJ", "ignoreList": []}]}