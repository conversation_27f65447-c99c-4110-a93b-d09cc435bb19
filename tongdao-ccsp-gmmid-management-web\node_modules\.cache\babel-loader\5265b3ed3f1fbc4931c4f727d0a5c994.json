{"remainingRequest": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\src\\api\\docker\\container.js", "dependencies": [{"path": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\src\\api\\docker\\container.js", "mtime": 1756802202442}, {"path": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\babel.config.js", "mtime": 1699511114284}, {"path": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1729062151209}, {"path": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1729062151198}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "getContainerList", "params", "url", "method", "deployContainer", "data", "startContainer", "id", "stopContainer", "restartContainer", "remove<PERSON><PERSON><PERSON>", "syncContainerStatus", "getContainersByImage", "imageId", "getContainersByNode", "nodeId", "getRunningContainers", "scaleContainer", "replicas", "updateContainerImage", "newImageId", "getContainerLogs", "lines", "arguments", "length", "undefined", "getContainerDetail", "concat", "deployContainerWithTraefik", "getContainerAccessUrl", "getAvailableDeviceResources", "getContainerDeviceResources", "getDeviceResourceUsageStats", "deviceResourceId", "deviceResourceType", "getRecommendedDeviceResources", "configContainerRoute", "updateContainerRoute", "removeContainerRoute", "deployContainerWithStrategy", "getRecommendedStrategy", "availableNodes"], "sources": ["D:/workspace/ccsp_v3/tongdao-ccsp-gmmid-management-web/src/api/docker/container.js"], "sourcesContent": ["import request from '@/utils/request'\n\n/**\n * Docker Swarm 容器实例管理 API\n * \n * 本模块对应后端 ContainerInstanceController\n * 基础路径: /api-management/swarm/container/v1\n * \n * 功能包括：\n * - 容器生命周期管理（部署、启动、停止、重启、删除）\n * - 容器扩缩容和镜像更新\n * - 容器状态同步和日志获取\n * - 按条件查询容器（镜像、节点）\n */\n\n// 分页查询容器实例列表\nexport function getContainerList(params) {\n  return request({\n    url: '/api-management/swarm/container/v1/list-page',\n    method: 'get',\n    params\n  })\n}\n\n// 部署容器\nexport function deployContainer(data) {\n  return request({\n    url: '/api-management/swarm/container/v1/deploy',\n    method: 'post',\n    data\n  })\n}\n\n// 启动容器\nexport function startContainer(id) {\n  return request({\n    url: '/api-management/swarm/container/v1/start',\n    method: 'post',\n    params: { id }\n  })\n}\n\n// 停止容器\nexport function stopContainer(id) {\n  return request({\n    url: '/api-management/swarm/container/v1/stop',\n    method: 'post',\n    params: { id }\n  })\n}\n\n// 重启容器\nexport function restartContainer(id) {\n  return request({\n    url: '/api-management/swarm/container/v1/restart',\n    method: 'post',\n    params: { id }\n  })\n}\n\n// 删除容器\nexport function removeContainer(id) {\n  return request({\n    url: '/api-management/swarm/container/v1/delete',\n    method: 'post',\n    params: { id }\n  })\n}\n\n// 同步容器状态\nexport function syncContainerStatus() {\n  return request({\n    url: '/api-management/swarm/container/v1/sync-status',\n    method: 'post'\n  })\n}\n\n// 根据镜像ID查询容器列表\nexport function getContainersByImage(imageId) {\n  return request({\n    url: '/api-management/swarm/container/v1/by-image',\n    method: 'get',\n    params: { imageId }\n  })\n}\n\n// 根据节点ID查询容器列表\nexport function getContainersByNode(nodeId) {\n  return request({\n    url: '/api-management/swarm/container/v1/by-node',\n    method: 'get',\n    params: { nodeId }\n  })\n}\n\n// 获取运行中的容器列表\nexport function getRunningContainers() {\n  return request({\n    url: '/api-management/swarm/container/v1/running',\n    method: 'get'\n  })\n}\n\n// 扩缩容操作\nexport function scaleContainer(id, replicas) {\n  return request({\n    url: '/api-management/swarm/container/v1/scale',\n    method: 'post',\n    params: { id, replicas }\n  })\n}\n\n// 更新容器镜像\nexport function updateContainerImage(id, newImageId) {\n  return request({\n    url: '/api-management/swarm/container/v1/update-image',\n    method: 'post',\n    params: { id, newImageId }\n  })\n}\n\n// 获取容器日志\nexport function getContainerLogs(id, lines = 100) {\n  return request({\n    url: '/api-management/swarm/container/v1/logs',\n    method: 'get',\n    params: { id, lines }\n  })\n}\n\n// 根据ID查询容器实例详情\nexport function getContainerDetail(id) {\n  return request({\n    url: `/api-management/swarm/container/v1/${id}`,\n    method: 'get'\n  })\n}\n\n// Traefik相关API\n\n// 带Traefik配置的容器部署\nexport function deployContainerWithTraefik(data) {\n  return request({\n    url: '/api-management/swarm/container/v1/deploy-with-traefik',\n    method: 'post',\n    data\n  })\n}\n\n// 获取容器访问地址\nexport function getContainerAccessUrl(id) {\n  return request({\n    url: `/api-management/swarm/container/v1/${id}/access-url`,\n    method: 'get'\n  })\n}\n\n// 获取可用的设备资源列表\nexport function getAvailableDeviceResources(params) {\n  return request({\n    url: '/api-management/swarm/container/v1/device-resources',\n    method: 'get',\n    params\n  })\n}\n\n// 获取容器关联的设备资源列表\nexport function getContainerDeviceResources(id) {\n  return request({\n    url: `/api-management/swarm/container/v1/${id}/device-resources`,\n    method: 'get'\n  })\n}\n\n// 获取设备资源使用统计\nexport function getDeviceResourceUsageStats(deviceResourceId, deviceResourceType) {\n  return request({\n    url: `/api-management/swarm/container/v1/device-resources/${deviceResourceId}/stats`,\n    method: 'get',\n    params: { deviceResourceType }\n  })\n}\n\n// 获取推荐的设备资源\nexport function getRecommendedDeviceResources(params) {\n  return request({\n    url: '/api-management/swarm/container/v1/device-resources/recommend',\n    method: 'get',\n    params\n  })\n}\n\n// 配置容器Traefik路由\nexport function configContainerRoute(data) {\n  return request({\n    url: '/api-management/swarm/container/v1/configure-traefik',\n    method: 'post',\n    data\n  })\n}\n\n// 更新容器Traefik路由\nexport function updateContainerRoute(data) {\n  return request({\n    url: '/api-management/swarm/container/v1/configure-traefik',\n    method: 'post',\n    data\n  })\n}\n\n// 移除容器Traefik配置\nexport function removeContainerRoute(id) {\n  return request({\n    url: '/api-management/swarm/container/v1/remove-traefik',\n    method: 'post',\n    params: { id }\n  })\n}\n\n// 分布策略相关API\n\n// 带分布策略的容器部署\nexport function deployContainerWithStrategy(data) {\n  return request({\n    url: '/api-management/swarm/container/v1/deploy-with-strategy',\n    method: 'post',\n    data\n  })\n}\n\n// 获取推荐的分布策略\nexport function getRecommendedStrategy(replicas, availableNodes) {\n  const params = { replicas }\n  if (availableNodes !== undefined) {\n    params.availableNodes = availableNodes\n  }\n  \n  return request({\n    url: '/api-management/swarm/container/v1/recommended-strategy',\n    method: 'get',\n    params\n  })\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,OAAO,SAASC,gBAAgBA,CAACC,MAAM,EAAE;EACvC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,8CAA8C;IACnDC,MAAM,EAAE,KAAK;IACbF,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASG,eAAeA,CAACC,IAAI,EAAE;EACpC,OAAON,OAAO,CAAC;IACbG,GAAG,EAAE,2CAA2C;IAChDC,MAAM,EAAE,MAAM;IACdE,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASC,cAAcA,CAACC,EAAE,EAAE;EACjC,OAAOR,OAAO,CAAC;IACbG,GAAG,EAAE,0CAA0C;IAC/CC,MAAM,EAAE,MAAM;IACdF,MAAM,EAAE;MAAEM,EAAE,EAAFA;IAAG;EACf,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASC,aAAaA,CAACD,EAAE,EAAE;EAChC,OAAOR,OAAO,CAAC;IACbG,GAAG,EAAE,yCAAyC;IAC9CC,MAAM,EAAE,MAAM;IACdF,MAAM,EAAE;MAAEM,EAAE,EAAFA;IAAG;EACf,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASE,gBAAgBA,CAACF,EAAE,EAAE;EACnC,OAAOR,OAAO,CAAC;IACbG,GAAG,EAAE,4CAA4C;IACjDC,MAAM,EAAE,MAAM;IACdF,MAAM,EAAE;MAAEM,EAAE,EAAFA;IAAG;EACf,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASG,eAAeA,CAACH,EAAE,EAAE;EAClC,OAAOR,OAAO,CAAC;IACbG,GAAG,EAAE,2CAA2C;IAChDC,MAAM,EAAE,MAAM;IACdF,MAAM,EAAE;MAAEM,EAAE,EAAFA;IAAG;EACf,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,mBAAmBA,CAAA,EAAG;EACpC,OAAOZ,OAAO,CAAC;IACbG,GAAG,EAAE,gDAAgD;IACrDC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASS,oBAAoBA,CAACC,OAAO,EAAE;EAC5C,OAAOd,OAAO,CAAC;IACbG,GAAG,EAAE,6CAA6C;IAClDC,MAAM,EAAE,KAAK;IACbF,MAAM,EAAE;MAAEY,OAAO,EAAPA;IAAQ;EACpB,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASC,mBAAmBA,CAACC,MAAM,EAAE;EAC1C,OAAOhB,OAAO,CAAC;IACbG,GAAG,EAAE,4CAA4C;IACjDC,MAAM,EAAE,KAAK;IACbF,MAAM,EAAE;MAAEc,MAAM,EAANA;IAAO;EACnB,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASC,oBAAoBA,CAAA,EAAG;EACrC,OAAOjB,OAAO,CAAC;IACbG,GAAG,EAAE,4CAA4C;IACjDC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASc,cAAcA,CAACV,EAAE,EAAEW,QAAQ,EAAE;EAC3C,OAAOnB,OAAO,CAAC;IACbG,GAAG,EAAE,0CAA0C;IAC/CC,MAAM,EAAE,MAAM;IACdF,MAAM,EAAE;MAAEM,EAAE,EAAFA,EAAE;MAAEW,QAAQ,EAARA;IAAS;EACzB,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASC,oBAAoBA,CAACZ,EAAE,EAAEa,UAAU,EAAE;EACnD,OAAOrB,OAAO,CAAC;IACbG,GAAG,EAAE,iDAAiD;IACtDC,MAAM,EAAE,MAAM;IACdF,MAAM,EAAE;MAAEM,EAAE,EAAFA,EAAE;MAAEa,UAAU,EAAVA;IAAW;EAC3B,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASC,gBAAgBA,CAACd,EAAE,EAAe;EAAA,IAAbe,KAAK,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,GAAG;EAC9C,OAAOxB,OAAO,CAAC;IACbG,GAAG,EAAE,yCAAyC;IAC9CC,MAAM,EAAE,KAAK;IACbF,MAAM,EAAE;MAAEM,EAAE,EAAFA,EAAE;MAAEe,KAAK,EAALA;IAAM;EACtB,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,kBAAkBA,CAACnB,EAAE,EAAE;EACrC,OAAOR,OAAO,CAAC;IACbG,GAAG,wCAAAyB,MAAA,CAAwCpB,EAAE,CAAE;IAC/CJ,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;;AAEA;AACA,OAAO,SAASyB,0BAA0BA,CAACvB,IAAI,EAAE;EAC/C,OAAON,OAAO,CAAC;IACbG,GAAG,EAAE,wDAAwD;IAC7DC,MAAM,EAAE,MAAM;IACdE,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASwB,qBAAqBA,CAACtB,EAAE,EAAE;EACxC,OAAOR,OAAO,CAAC;IACbG,GAAG,wCAAAyB,MAAA,CAAwCpB,EAAE,gBAAa;IAC1DJ,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAAS2B,2BAA2BA,CAAC7B,MAAM,EAAE;EAClD,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,qDAAqD;IAC1DC,MAAM,EAAE,KAAK;IACbF,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAAS8B,2BAA2BA,CAACxB,EAAE,EAAE;EAC9C,OAAOR,OAAO,CAAC;IACbG,GAAG,wCAAAyB,MAAA,CAAwCpB,EAAE,sBAAmB;IAChEJ,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAAS6B,2BAA2BA,CAACC,gBAAgB,EAAEC,kBAAkB,EAAE;EAChF,OAAOnC,OAAO,CAAC;IACbG,GAAG,yDAAAyB,MAAA,CAAyDM,gBAAgB,WAAQ;IACpF9B,MAAM,EAAE,KAAK;IACbF,MAAM,EAAE;MAAEiC,kBAAkB,EAAlBA;IAAmB;EAC/B,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASC,6BAA6BA,CAAClC,MAAM,EAAE;EACpD,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,+DAA+D;IACpEC,MAAM,EAAE,KAAK;IACbF,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASmC,oBAAoBA,CAAC/B,IAAI,EAAE;EACzC,OAAON,OAAO,CAAC;IACbG,GAAG,EAAE,sDAAsD;IAC3DC,MAAM,EAAE,MAAM;IACdE,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASgC,oBAAoBA,CAAChC,IAAI,EAAE;EACzC,OAAON,OAAO,CAAC;IACbG,GAAG,EAAE,sDAAsD;IAC3DC,MAAM,EAAE,MAAM;IACdE,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASiC,oBAAoBA,CAAC/B,EAAE,EAAE;EACvC,OAAOR,OAAO,CAAC;IACbG,GAAG,EAAE,mDAAmD;IACxDC,MAAM,EAAE,MAAM;IACdF,MAAM,EAAE;MAAEM,EAAE,EAAFA;IAAG;EACf,CAAC,CAAC;AACJ;;AAEA;;AAEA;AACA,OAAO,SAASgC,2BAA2BA,CAAClC,IAAI,EAAE;EAChD,OAAON,OAAO,CAAC;IACbG,GAAG,EAAE,yDAAyD;IAC9DC,MAAM,EAAE,MAAM;IACdE,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASmC,sBAAsBA,CAACtB,QAAQ,EAAEuB,cAAc,EAAE;EAC/D,IAAMxC,MAAM,GAAG;IAAEiB,QAAQ,EAARA;EAAS,CAAC;EAC3B,IAAIuB,cAAc,KAAKhB,SAAS,EAAE;IAChCxB,MAAM,CAACwC,cAAc,GAAGA,cAAc;EACxC;EAEA,OAAO1C,OAAO,CAAC;IACbG,GAAG,EAAE,yDAAyD;IAC9DC,MAAM,EAAE,KAAK;IACbF,MAAM,EAANA;EACF,CAAC,CAAC;AACJ", "ignoreList": []}]}