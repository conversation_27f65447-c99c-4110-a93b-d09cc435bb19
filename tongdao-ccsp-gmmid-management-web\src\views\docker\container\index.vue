<template>
  <div class="app-container">
    <el-card shadow="never">
      <div class="filter-container clearfix">
        <el-form ref="searchForm" :model="listQuery" inline @submit.native.prevent="getList">
          <div class="filter-inner">
            <el-form-item label="容器名称">
              <el-input v-model.trim="listQuery.containerName" clearable placeholder="容器名称" />
            </el-form-item>
            <el-form-item label="状态">
              <el-select v-model="listQuery.status" clearable placeholder="请选择状态">
                <el-option label="运行中" value="running" />
                <el-option label="已停止" value="stopped" />
                <el-option label="已暂停" value="paused" />
              </el-select>
            </el-form-item>
          </div>
          <div class="pull-right">
            <el-button-group>
              <el-form-item>
                <el-button type="primary" @click="handleSearch"><i class="el-icon-search" /> 查询</el-button>
                <el-button type="info" @click="handleResetSearch"><i class="el-icon-refresh" /> 重置</el-button>
              </el-form-item>
            </el-button-group>
          </div>
        </el-form>
      </div>
    </el-card>
    
    <el-card shadow="never">
      <div class="filter-container clearfix">
        <div class="filter-inner">
          <el-button type="primary" @click="handleDeployContainer">部署容器</el-button>
          <el-button type="success" @click="handleSyncStatus">同步状态</el-button>
        </div>
      </div>
      
      <el-table
        ref="dataTable"
        v-loading="loading"
        :data="tableList"
        style="width: 100%;"
      >
        <el-table-column align="center" label="序号" type="index" width="50" />
        <el-table-column align="center" label="容器ID" min-width="120" prop="id" />
        <el-table-column align="center" label="容器名称" min-width="150" prop="containerName" />
        <el-table-column align="center" label="所属应用" min-width="140">
          <template v-slot="{row}">
            <span>{{ row.applicationName || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="镜像" min-width="150">
          <template v-slot="{row}">
            <span>{{ row.imageName }}:{{ row.imageTag || 'latest' }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="状态" min-width="100" prop="status">
          <template v-slot="{row}">
            <el-tag v-if="row.status === 'running'" type="success">运行中</el-tag>
            <el-tag v-else-if="row.status === 'stopped'" type="danger">已停止</el-tag>
            <el-tag v-else-if="row.status === 'paused'" type="warning">已暂停</el-tag>
            <el-tag v-else>{{ row.status }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column align="center" label="端口" min-width="120">
          <template v-slot="{row}">
            <span>{{ formatPorts(row.portMappings) }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="副本信息" min-width="120">
          <template v-slot="{row}">
            <div v-if="row.replicas">
              <div>副本数：{{ row.replicas || '-' }}</div>
              <div v-if="row.distributionStrategy" class="strategy-info">
                分布：{{ getStrategyDisplayName(row.distributionStrategy) }}
              </div>
            </div>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="访问地址" min-width="180">
          <template v-slot="{row}">
            <div v-if="row.accessUrl">
              <el-link :href="row.accessUrl" target="_blank" type="primary">
                {{ row.accessUrl }}
              </el-link>
              <el-button 
                type="text" 
                size="mini" 
                @click="copyToClipboard(row.accessUrl)"
                style="margin-left: 5px;"
              >
                <i class="el-icon-copy-document"></i>
              </el-button>
            </div>
            <el-button 
              v-else-if="row.status === 'running'" 
              type="text" 
              size="mini" 
              @click="handleConfigRoute(row)"
            >
              配置访问
            </el-button>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="HSM设备" min-width="120">
          <template v-slot="{row}">
            <div v-if="row.hsmDeviceId || row.hsmConfigured">
              <el-tag type="success" size="mini">已配置</el-tag>
              <div v-if="row.hsmDeviceName" class="hsm-info">
                {{ row.hsmDeviceName }}
              </div>
            </div>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="节点" min-width="100" prop="nodeName" />
        <el-table-column align="center" label="创建时间" min-width="150">
          <template v-slot="{row}">
            <span>{{ formatDate(row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" fixed="right" label="操作" width="250">
          <template v-slot="{row}">
            <el-button v-if="row.status === 'running'" type="text" @click="handleStopContainer(row)">停止</el-button>
            <el-button v-else type="text" @click="handleStartContainer(row)">启动</el-button>
            <el-button type="text" @click="handleRestartContainer(row)">重启</el-button>
            <el-button type="text" @click="handleScaleContainer(row)">扩缩容</el-button>
            <el-button type="text" @click="handleViewLogs(row)">日志</el-button>
            <el-button v-if="row.accessUrl" type="text" @click="handleUpdateRoute(row)">更新路由</el-button>
            <el-button type="text" @click="handleRemoveContainer(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <pagination
        v-show="total > listQuery.pageSize"
        :limit.sync="listQuery.pageSize"
        :page.sync="listQuery.page"
        :total="total"
        layout="prev, pager, next"
        @pagination="getList"
      />
    </el-card>
    
    <!-- 部署容器对话框 -->
    <el-dialog :visible.sync="deployDialogVisible" title="部署容器" width="600px">
      <el-form ref="deployForm" :model="deployForm" :rules="deployRules" label-width="120px">
        <el-form-item label="关联应用" prop="applicationId">
          <el-select v-model="deployForm.applicationId" filterable placeholder="请选择应用" style="width: 100%;" :loading="appOptionsLoading">
            <el-option v-for="opt in appOptions" :key="opt.value" :label="opt.label" :value="String(opt.value)" />
          </el-select>
        </el-form-item>
        <el-form-item label="容器名称" prop="containerName">
          <el-input v-model.trim="deployForm.containerName" placeholder="例如: my-nginx" />
        </el-form-item>
        <el-form-item label="镜像选择" prop="imageId">
          <el-select 
            v-model="deployForm.imageId" 
            filterable 
            placeholder="请选择镜像"
            @change="handleImageChange"
            style="width: 100%;"
            :loading="imageListLoading"
            :disabled="imageList.length === 0"
          >
            <el-option
              v-for="image in imageList"
              :key="image.id"
              :label="`${image.name}:${image.tag}`"
              :value="image.id"
            >
              <span style="float: left">{{ image.name }}:{{ image.tag }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ formatImageSize(image.sizeMb) }}</span>
            </el-option>
            <div v-if="imageList.length === 0" slot="empty">
              <span v-if="imageListLoading">加载中...</span>
              <span v-else>暂无可用镜像，请先构建或导入镜像</span>
            </div>
          </el-select>
          <div class="form-help">
            选择已有的Docker镜像进行部署
            <el-button 
              type="text" 
              size="mini" 
              @click="loadImageList"
              style="margin-left: 8px;"
            >
              <i class="el-icon-refresh"></i> 刷新
            </el-button>
          </div>
        </el-form-item>
        <el-form-item label="镜像信息" v-if="selectedImage">
          <div class="image-info">
            <p><strong>名称：</strong>{{ selectedImage.name }}</p>
            <p><strong>标签：</strong>{{ selectedImage.tag }}</p>
            <p><strong>大小：</strong>{{ formatImageSize(selectedImage.sizeMb) }}</p>
            <p v-if="selectedImage.description"><strong>描述：</strong>{{ selectedImage.description }}</p>
          </div>
        </el-form-item>
        <el-form-item label="服务端口" prop="servicePort">
          <el-input-number v-model="deployForm.servicePort" placeholder="容器内部端口，如: 80" :min="1" :max="65535" />
          <div class="form-help">容器内应用监听的端口号</div>
        </el-form-item>
        <el-form-item label="副本数" prop="replicas">
          <el-input-number v-model="deployForm.replicas" :min="1" />
        </el-form-item>
        <el-form-item label="分布策略" v-if="deployForm.replicas > 1">
          <el-select v-model="deployForm.distributionStrategy" placeholder="请选择分布策略" style="width: 100%;">
            <el-option label="跨节点分散（推荐）" value="SPREAD_ACROSS_NODES" />
            <el-option label="仅Worker节点" value="WORKER_NODES_ONLY" />
            <el-option label="仅Manager节点" value="MANAGER_NODES_ONLY" />
            <el-option label="平衡分布" value="BALANCED" />
            <el-option label="跨可用区分散" value="SPREAD_ACROSS_ZONES" />
          </el-select>
          <div class="form-help">多副本时的部署分布策略，推荐选择跨节点分散</div>
        </el-form-item>
        <el-form-item label="启用路由">
          <el-switch v-model="deployForm.enableTraefik" active-text="启用" inactive-text="禁用" />
          <div class="form-help">启用后可通过内网地址访问服务</div>
        </el-form-item>
        <el-form-item label="HSM设备资源">
          <el-switch v-model="deployForm.enableHsm" active-text="启用" inactive-text="禁用" @change="handleHsmToggle" />
          <div class="form-help">启用后可配置HSM加密设备资源</div>
        </el-form-item>
        <el-form-item v-if="deployForm.enableHsm" label="选择设备" prop="hsmDeviceId">
          <el-select 
            v-model="deployForm.hsmDeviceId" 
            filterable 
            placeholder="请选择HSM设备"
            @change="handleHsmDeviceChange"
            style="width: 100%;"
            :loading="hsmDeviceListLoading"
            :disabled="hsmDeviceList.length === 0"
          >
            <el-option
              v-for="device in hsmDeviceList"
              :key="device.deviceId"
              :label="device.deviceName"
              :value="device.deviceId"
            >
              <span style="float: left">{{ device.deviceName }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ device.ipAddress }}:{{ device.servicePort }}</span>
            </el-option>
            <div v-if="hsmDeviceList.length === 0" slot="empty">
              <span v-if="hsmDeviceListLoading">加载中...</span>
              <span v-else>暂无可用设备</span>
            </div>
          </el-select>
          <div class="form-help">
            选择可用的HSM加密设备
            <el-button 
              type="text" 
              size="mini" 
              @click="loadHsmDeviceList"
              style="margin-left: 8px;"
            >
              <i class="el-icon-refresh"></i> 刷新
            </el-button>
          </div>
        </el-form-item>
        <el-form-item v-if="deployForm.enableHsm && selectedHsmDevice" label="设备信息">
          <div class="device-info">
            <p><strong>设备名称：</strong>{{ selectedHsmDevice.deviceName }}</p>
            <p><strong>IP地址：</strong>{{ selectedHsmDevice.ipAddress }}</p>
            <p><strong>服务端口：</strong>{{ selectedHsmDevice.servicePort }}</p>
            <p><strong>管理端口：</strong>{{ selectedHsmDevice.managementPort }}</p>
            <p><strong>状态：</strong>
              <el-tag v-if="selectedHsmDevice.status === 'available'" type="success" size="mini">可用</el-tag>
              <el-tag v-else-if="selectedHsmDevice.status === 'running'" type="success" size="mini">运行中</el-tag>
              <el-tag v-else-if="selectedHsmDevice.status === 'active'" type="success" size="mini">活跃</el-tag>
              <el-tag v-else type="warning" size="mini">{{ selectedHsmDevice.status }}</el-tag>
            </p>
            <p v-if="selectedHsmDevice.description"><strong>描述：</strong>{{ selectedHsmDevice.description }}</p>
            <p v-if="selectedHsmDevice.deviceGroupName"><strong>设备组：</strong>{{ selectedHsmDevice.deviceGroupName }}</p>
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="deployDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmDeployContainer">部署</el-button>
      </div>
    </el-dialog>
    
    <!-- 容器日志对话框 -->
    <el-dialog :visible.sync="logsDialogVisible" title="容器日志" width="800px">
      <el-input
        v-model="containerLogs"
        :rows="20"
        readonly
        type="textarea"
      />
      <div slot="footer" class="dialog-footer">
        <el-button @click="logsDialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>
    
    <!-- 扩缩容对话框 -->
    <el-dialog :visible.sync="scaleDialogVisible" title="容器扩缩容" width="500px">
      <el-form ref="scaleForm" :model="scaleForm" :rules="scaleRules" label-width="100px">
        <el-form-item label="副本数" prop="replicas">
          <el-input-number v-model="scaleForm.replicas" :min="1" />
          <div class="form-help">当前运行的容器实例数量</div>
        </el-form-item>
        <el-form-item label="分布策略" v-if="scaleForm.replicas > 1">
          <el-select v-model="scaleForm.distributionStrategy" placeholder="请选择分布策略" style="width: 100%;">
            <el-option label="跨节点分散（推荐）" value="SPREAD_ACROSS_NODES" />
            <el-option label="仅Worker节点" value="WORKER_NODES_ONLY" />
            <el-option label="仅Manager节点" value="MANAGER_NODES_ONLY" />
            <el-option label="平衡分布" value="BALANCED" />
          </el-select>
          <div class="form-help">多副本时的部署分布策略</div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="scaleDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmScaleContainer">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Pagination from '@/components/Pagination'
import {
  getContainerList,
  deployContainer,
  getRecommendedStrategy,
  startContainer,
  stopContainer,
  restartContainer,
  removeContainer,
  getContainerLogs,
  syncContainerStatus,
  scaleContainer,
  updateContainerImage,
  configContainerRoute,
  updateContainerRoute
} from '@/api/docker/container'
import { getImageList } from '@/api/docker/image'
import { fetchApplicationOptions, fetchUserApps } from '@/api/application'
import { getAvailableHsmDevices, getHsmDeviceDetail } from '@/api/docker/hsm'

export default {
  name: 'DockerContainerIndex',
  components: {
    Pagination
  },
  data() {
    return {
      tableList: [],
      loading: false,
      total: 0,
      listQuery: {
        page: 1,
        pageSize: 20,
        containerName: undefined,
        status: undefined
      },
      logsDialogVisible: false,
      containerLogs: '',
      currentContainer: null,
      deployDialogVisible: false,
      deployForm: {
        containerName: '',
        imageId: null,
        imageName: '',
        imageTag: 'latest',
        servicePort: 80,
        replicas: 1,
        distributionStrategy: 'SPREAD_ACROSS_NODES',
        enableTraefik: true,
        enableHsm: false,
        hsmDeviceId: null,
        deployedBy: null,
        applicationId: null,
        applicationName: ''
      },
      deployRules: {
        applicationId: [{ required: true, message: '请选择关联应用', trigger: 'change' }],
        containerName: [{ required: true, message: '请输入容器名称', trigger: 'blur' }],
        imageId: [{ required: true, message: '请选择镜像', trigger: 'change' }],
        servicePort: [{ required: true, message: '请输入服务端口', trigger: 'blur' }]
      },
      scaleDialogVisible: false,
      scaleForm: {
        id: '',
        replicas: 1,
        distributionStrategy: 'SPREAD_ACROSS_NODES'
      },
      scaleRules: {
        replicas: [{ required: true, message: '请输入副本数量', trigger: 'blur' }]
      },
      // 镜像相关数据
      imageList: [],
      imageListLoading: false,
      selectedImage: null,
      // HSM设备相关数据
      hsmDeviceList: [],
      hsmDeviceListLoading: false,
      selectedHsmDevice: null,
      // 应用下拉
      appOptions: [],
      appOptionsLoading: false
    }
  },
  methods: {
    handleSearch() {
      this.getList()
    },
    handleResetSearch() {
      this.listQuery = {
        page: 1,
        pageSize: 20,
        containerName: undefined,
        status: undefined
      }
      this.getList()
    },
    handleDeployContainer() {
      // 首先加载镜像列表
      Promise.all([this.loadImageList(), this.loadAppOptions()]).then(() => {
        this.deployForm = {
          containerName: '',
          imageId: null,
          imageName: '',
          imageTag: 'latest',
          servicePort: 80,
          replicas: 1,
          distributionStrategy: 'SPREAD_ACROSS_NODES',
          enableTraefik: true,
          enableHsm: false,
          hsmDeviceId: null,
          deployedBy: this.user ? this.user.id : null,
          applicationId: null,
          applicationName: ''
        }
        this.selectedImage = null
        this.selectedHsmDevice = null
        this.deployDialogVisible = true
        this.$nextTick(() => {
          this.$refs['deployForm'].clearValidate()
        })
      })
    },
    confirmDeployContainer() {
      this.$refs['deployForm'].validate((valid) => {
        if (valid) {
          // 检查HSM设备配置
          if (this.deployForm.enableHsm && !this.deployForm.hsmDeviceId) {
            this.$message.error('请选择HSM设备')
            return
          }
          
          const deployData = { ...this.deployForm }
          
          // 确保镜像信息完整
          if (this.selectedImage) {
            deployData.imageName = this.selectedImage.imageName
            deployData.imageTag = this.selectedImage.imageTag
          }
          
          // 构建HSM设备配置
          if (this.deployForm.enableHsm && this.selectedHsmDevice) {
            deployData.hsmDeviceConfig = {
              encryptorGroupId: 1, // 默认组ID
              encryptorId: this.selectedHsmDevice.deviceId,
              encryptorName: this.selectedHsmDevice.deviceName,
              serverIpAddr: this.selectedHsmDevice.ipAddress,
              serverPort: this.selectedHsmDevice.servicePort,
              tcpConnNum: this.selectedHsmDevice.tcpConnNum || 5,
              msgHeadLen: this.selectedHsmDevice.msgHeadLen || 4,
              msgTailLen: this.selectedHsmDevice.msgTailLen || 0,
              asciiOrEbcdic: this.selectedHsmDevice.encoding || 0,
              dynamicLibPath: './libdeviceapi.so'
            }
          }
          
          // 构建Traefik配置
          if (this.deployForm.enableTraefik) {
            deployData.traefikConfig = {
              enabled: true,
              domain: `${deployData.containerName.toLowerCase().replace(/[^a-z0-9-]/g, '-')}.ccsp.local`,
              servicePort: deployData.servicePort || 80,
              pathPrefix: '/',
              httpsEnabled: false
            }
          }

          // 设置分布策略
          if (deployData.replicas > 1 && deployData.distributionStrategy) {
            deployData.distributionStrategy = deployData.distributionStrategy
          }

          // 设备资源配置
          if (this.deployForm.enableHsm && this.selectedHsmDevice && deployData.hsmDeviceConfig) {
            deployData.deviceResourceConfig = {
              deviceResourceType: 'VSM',
              deviceResourceIds: [this.selectedHsmDevice.deviceId],
              allocationType: 'exclusive',
              priority: 1,
              configData: JSON.stringify(deployData.hsmDeviceConfig)
            }
          }

          // 使用统一的部署接口
          console.log('使用统一的部署接口，配置：', {
            hsm: this.deployForm.enableHsm,
            traefik: this.deployForm.enableTraefik,
            strategy: deployData.distributionStrategy,
            replicas: deployData.replicas
          })

          deployContainer(deployData).then((response) => {
            this.deployDialogVisible = false
            if (response.code === 20000) {
              if (response.data) {
                // 处理不同的响应结构
                let message = '容器部署成功！'
                
                if (response.data.accessUrl) {
                  message += `访问地址：${response.data.accessUrl}`
                } else if (response.data.container && response.data.container.accessUrl) {
                  message += `访问地址：${response.data.container.accessUrl}`
                }
                
                if (response.data.distributionStrategy) {
                  message += `，分布策略：${this.getStrategyDisplayName(response.data.distributionStrategy)}`
                }
                
                if (response.data.hsmConfigured) {
                  message += `，HSM设备：${this.selectedHsmDevice.deviceName}`
                }
                
                this.$message.success(message)
              } else {
                this.$message.success('容器部署任务已提交')
              }
              this.getList()
            } else {
              this.$message.error(response.message || '部署失败')
            }
          }).catch(() => {
            // 错误处理已在拦截器中处理
          })
        }
      })
    },

    // 加载应用选项
    async loadAppOptions() {
      this.appOptionsLoading = true
      try {
        let resp
        // 租户角色使用 user/list-options，其它使用 fetch-options
        if (this.user && Array.isArray(this.user.roles) && this.user.roles.includes('ROLE_TENANT')) {
          resp = await fetchUserApps()
        } else {
          resp = await fetchApplicationOptions()
        }
        if (resp && resp.code === 20000) {
          const data = Array.isArray(resp.data) ? resp.data : (resp.data && resp.data.list) ? resp.data.list : []
          this.appOptions = data.map(item => {
            if (item.label && (item.value !== undefined)) return item
            return { label: item.name || item.applicationName || item.label, value: String(item.id || item.value) }
          })
        } else {
          this.appOptions = []
        }
      } catch (e) {
        this.appOptions = []
      } finally {
        this.appOptionsLoading = false
      }
    },
    handleStartContainer(row) {
      startContainer(row.id).then(() => {
        this.$message.success('容器启动成功')
        this.getList()
      })
    },
    handleStopContainer(row) {
      stopContainer(row.id).then(() => {
        this.$message.success('容器停止成功')
        this.getList()
      })
    },
    handleRestartContainer(row) {
      restartContainer(row.id).then(() => {
        this.$message.success('容器重启成功')
        this.getList()
      })
    },
    handleRemoveContainer(row) {
      this.$msgbox.confirm(`确认要删除容器 ${row.containerName} 吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        removeContainer(row.id).then(() => {
          this.$message.success('删除成功')
          this.getList()
        })
      }).catch(() => {
        // 取消删除
      })
    },
    handleScaleContainer(row) {
      this.scaleForm = {
        id: row.id,
        replicas: row.replicas || 1, // 使用当前副本数或默认值
        distributionStrategy: row.distributionStrategy || 'SPREAD_ACROSS_NODES'
      }
      this.scaleDialogVisible = true
      this.$nextTick(() => {
        this.$refs['scaleForm'].clearValidate()
      })
    },
    confirmScaleContainer() {
      this.$refs['scaleForm'].validate((valid) => {
        if (valid) {
          scaleContainer(this.scaleForm.id, this.scaleForm.replicas).then(() => {
            this.scaleDialogVisible = false
            this.$message.success('容器扩缩容任务已提交')
            this.getList()
          })
        }
      })
    },
    handleViewLogs(row) {
      this.currentContainer = row
      getContainerLogs(row.id).then(response => {
        if (response.code === 20000) {
          this.containerLogs = response.data || ''
          this.logsDialogVisible = true
        }
      })
    },
    handleSyncStatus() {
      syncContainerStatus().then(() => {
        this.$message.success('容器状态同步任务已提交')
        this.getList()
      })
    },
    getList() {
      this.loading = true
      getContainerList(this.listQuery).then(response => {
        if (response.code === 20000) {
          this.tableList = response.data.list
          this.total = response.data.totalCount
        }
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    // 格式化端口映射显示
    formatPorts(portMappings) {
      if (!portMappings) return '-'
      try {
        const ports = JSON.parse(portMappings)
        if (Array.isArray(ports) && ports.length > 0) {
          return ports.map(p => `${p.hostPort || ''}:${p.containerPort}/${p.protocol || 'tcp'}`).join(', ')
        }
        return '-'
      } catch (e) {
        return portMappings
      }
    },
    // 格式化日期显示
    formatDate(dateTime) {
      if (!dateTime) return '-'
      return new Date(dateTime).toLocaleString('zh-CN')
    },
    
    // 复制到剪贴板
    copyToClipboard(text) {
      if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(() => {
          this.$message.success('地址已复制到剪贴板')
        })
      } else {
        // 降级方案
        const textArea = document.createElement('textarea')
        textArea.value = text
        document.body.appendChild(textArea)
        textArea.select()
        document.execCommand('copy')
        document.body.removeChild(textArea)
        this.$message.success('地址已复制到剪贴板')
      }
    },
    
    // 配置访问路由
    handleConfigRoute(row) {
      this.$prompt('请输入服务端口（容器内部端口）', '配置访问路由', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: '80',
        inputValidator: (value) => {
          const port = parseInt(value)
          if (!port || port < 1 || port > 65535) {
            return '请输入有效的端口号(1-65535)'
          }
          return true
        }
      }).then(({ value }) => {
        const routeConfig = {
          containerId: row.id,
          servicePort: parseInt(value),
          enableTraefik: true
        }
        
        this.configContainerRoute(routeConfig).then((response) => {
          if (response.code === 20000 && response.data && response.data.accessUrl) {
            this.$message.success(`路由配置成功！访问地址：${response.data.accessUrl}`)
            this.getList()
          } else {
            this.$message.error(response.message || '配置失败')
          }
        }).catch(() => {
          // 错误处理已在拦截器中处理
        })
      }).catch(() => {
        // 取消操作
      })
    },
    
    // 更新路由配置
    handleUpdateRoute(row) {
      this.$prompt('请输入新的服务端口', '更新路由', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValidator: (value) => {
          const port = parseInt(value)
          if (!port || port < 1 || port > 65535) {
            return '请输入有效的端口号(1-65535)'
          }
          return true
        }
      }).then(({ value }) => {
        const routeConfig = {
          containerId: row.id,
          servicePort: parseInt(value)
        }
        
        this.updateContainerRoute(routeConfig).then((response) => {
          if (response.code === 20000 && response.data && response.data.accessUrl) {
            this.$message.success(`路由更新成功！新地址：${response.data.accessUrl}`)
            this.getList()
          } else {
            this.$message.error(response.message || '更新失败')
          }
        }).catch(() => {
          // 错误处理已在拦截器中处理
        })
      }).catch(() => {
        // 取消操作
      })
    },
    

    
    // 配置容器路由方法
    async configContainerRoute(routeConfig) {
      return configContainerRoute(routeConfig)
    },
    
    // 更新容器路由方法
    async updateContainerRoute(routeConfig) {
      return updateContainerRoute(routeConfig)
    },
    
    // 加载镜像列表
    async loadImageList() {
      this.imageListLoading = true
      try {
        const response = await getImageList()
        if (response.code === 20000) {
          // 处理分页数据结构
          if (response.data && response.data.list) {
            // 如果返回的是分页结构
            this.imageList = response.data.list.map(image => {
              return {
                ...image,
                // 确保 sizeMb 是数字类型
                sizeMb: typeof image.sizeMb === 'string' ? parseInt(image.sizeMb) : image.sizeMb
              }
            })
          } else if (Array.isArray(response.data)) {
            // 如果返回的是直接数组
            this.imageList = response.data.map(image => {
              return {
                ...image,
                sizeMb: typeof image.sizeMb === 'string' ? parseInt(image.sizeMb) : image.sizeMb
              }
            })
          } else {
            this.imageList = []
          }
          
          if (this.imageList.length === 0) {
            this.$message.info('当前没有可用的镜像，请先构建或导入镜像')
          }
        } else {
          this.$message.error('获取镜像列表失败：' + (response.message || '未知错误'))
          this.imageList = []
        }
      } catch (error) {
        console.error('加载镜像列表失败:', error)
        this.$message.error('加载镜像列表失败')
        this.imageList = []
      } finally {
        this.imageListLoading = false
      }
    },
    
    // 处理镜像选择变化
    handleImageChange(imageId) {
      if (imageId) {
        this.selectedImage = this.imageList.find(img => img.id === imageId)
        if (this.selectedImage) {
          // 自动填充镜像名称和标签
          this.deployForm.imageName = this.selectedImage.name
          this.deployForm.imageTag = this.selectedImage.tag
          
          // 智能生成容器名称（如果当前为空）
          if (!this.deployForm.containerName) {
            this.suggestContainerName(this.selectedImage.name, this.selectedImage.tag)
          }
          
          // 根据镜像类型智能推荐端口
          this.suggestServicePort(this.selectedImage.name)
        }
      } else {
        this.selectedImage = null
        this.deployForm.imageName = ''
        this.deployForm.imageTag = 'latest'
      }
    },
    
    // 智能生成容器名称
    suggestContainerName(imageName, imageTag) {
      // 移除镜像名称中的特殊字符，生成简洁的名称
      let baseName = imageName.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase()
      
      // 如果标签不是latest，则加入标签
      if (imageTag && imageTag !== 'latest') {
        baseName += '-' + imageTag.replace(/[^a-zA-Z0-9]/g, '-')
      }
      
      // 添加时间戳保证唯一性
      const timestamp = Date.now().toString().slice(-6)
      this.deployForm.containerName = `${baseName}-${timestamp}`
    },
    
    // 根据镜像类型智能推荐端口
    suggestServicePort(imageName) {
      const portMap = {
        'nginx': 80,
        'apache': 80,
        'httpd': 80,
        'mysql': 3306,
        'mariadb': 3306,
        'postgres': 5432,
        'postgresql': 5432,
        'redis': 6379,
        'mongodb': 27017,
        'mongo': 27017,
        'tomcat': 8080,
        'node': 3000,
        'spring': 8080,
        'java': 8080
      }
      
      // 查找匹配的镜像类型
      for (const [key, port] of Object.entries(portMap)) {
        if (imageName.toLowerCase().includes(key)) {
          this.deployForm.servicePort = port
          break
        }
      }
    },
    
    // 格式化镜像大小
    formatImageSize(sizeMb) {
      if (!sizeMb || sizeMb === 0) return '-'
      
      // 确保是数字类型
      const size = typeof sizeMb === 'string' ? parseInt(sizeMb) : sizeMb
      if (isNaN(size)) return '-'
      
      // 如果已经是MB单位，直接显示
      if (size < 1024) {
        return `${size} MB`
      }
      
      // 转换为GB
      const sizeGb = size / 1024
      return `${sizeGb.toFixed(1)} GB`
    },
    
    // 获取分布策略显示名称
    getStrategyDisplayName(strategy) {
      const strategyMap = {
        'SPREAD_ACROSS_NODES': '跨节点分散',
        'WORKER_NODES_ONLY': '仅Worker节点',
        'MANAGER_NODES_ONLY': '仅Manager节点',
        'BALANCED': '平衡分布',
        'SPREAD_ACROSS_ZONES': '跨可用区分散'
      }
      return strategyMap[strategy] || strategy
    },
    
    // 获取推荐的分布策略
    async getRecommendedDistributionStrategy(replicas) {
      try {
        const response = await getRecommendedStrategy(replicas)
        if (response.code === 20000 && response.data) {
          return response.data.recommendedStrategy
        }
      } catch (error) {
        console.warn('获取推荐分布策略失败:', error)
      }
      return 'SPREAD_ACROSS_NODES' // 默认值
    },
    
    // === HSM设备相关方法 ===
    
    // 处理HSM开关变化
    handleHsmToggle(enabled) {
      if (enabled) {
        // 启用HSM时加载设备列表
        this.loadHsmDeviceList()
      } else {
        // 禁用HSM时清空选择
        this.deployForm.hsmDeviceId = null
        this.selectedHsmDevice = null
      }
    },
    
    // 加载HSM设备列表
    async loadHsmDeviceList() {
      this.hsmDeviceListLoading = true
      try {
        const response = await getAvailableHsmDevices({
          status: 'running'
        })
        
        if (response.code === 20000) {
          if (response.data && response.data.list) {
            this.hsmDeviceList = response.data.list
          } else if (Array.isArray(response.data)) {
            this.hsmDeviceList = response.data
          } else {
            this.hsmDeviceList = []
          }
          
          if (this.hsmDeviceList.length === 0) {
            this.$message.info('当前没有可用的HSM设备')
          }
        } else {
          this.$message.error('获取HSM设备列表失败：' + (response.message || '未知错误'))
          this.hsmDeviceList = []
        }
      } catch (error) {
        console.error('加载HSM设备列表失败:', error)
        this.$message.error('加载HSM设备列表失败')
        this.hsmDeviceList = []
      } finally {
        this.hsmDeviceListLoading = false
      }
    },
    
    // 处理HSM设备选择变化
    handleHsmDeviceChange(deviceId) {
      if (deviceId) {
        this.selectedHsmDevice = this.hsmDeviceList.find(device => device.deviceId === deviceId)
        if (this.selectedHsmDevice) {
          // 可以在这里加载更详细的设备信息
          this.loadHsmDeviceDetail(deviceId)
        }
      } else {
        this.selectedHsmDevice = null
      }
    },
    
    // 加载HSM设备详细信息
    async loadHsmDeviceDetail(deviceId) {
      try {
        const response = await getHsmDeviceDetail(deviceId)
        if (response.code === 20000 && response.data) {
          this.selectedHsmDevice = response.data
        }
      } catch (error) {
        console.warn('获取HSM设备详细信息失败:', error)
      }
    },
    

  },
  mounted() {
    this.getList()
    // 预加载镜像列表，提升用户体验
    this.loadImageList()
    // 预加载HSM设备列表
    this.loadHsmDeviceList()
  },
  computed: {
    ...mapGetters([
      'user'
    ])
  },
  watch: {
    // 监听副本数变化，自动更新推荐的分布策略
    'deployForm.replicas'(newReplicas, oldReplicas) {
      if (newReplicas > 1 && newReplicas !== oldReplicas) {
        this.getRecommendedDistributionStrategy(newReplicas).then(strategy => {
          this.deployForm.distributionStrategy = strategy
        })
      } else if (newReplicas === 1) {
        // 单副本时不需要分布策略
        this.deployForm.distributionStrategy = 'BALANCED'
      }
    },
    // 监听扩缩容副本数变化
    'scaleForm.replicas'(newReplicas, oldReplicas) {
      if (newReplicas > 1 && newReplicas !== oldReplicas) {
        this.getRecommendedDistributionStrategy(newReplicas).then(strategy => {
          this.scaleForm.distributionStrategy = strategy
        })
      } else if (newReplicas === 1) {
        this.scaleForm.distributionStrategy = 'BALANCED'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.filter-inner {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  
  .el-form-item {
    margin-bottom: 10px;
    margin-right: 15px;
  }
}

.pull-right {
  float: right;
}

.clearfix::after {
  content: "";
  display: table;
  clear: both;
}

.form-help {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.el-link {
  font-size: 12px;
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
}

.image-info {
  background-color: #f5f7fa;
  padding: 12px;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
  
  p {
    margin: 4px 0;
    font-size: 13px;
    
    strong {
      color: #303133;
      font-weight: 500;
    }
  }
}

.device-info {
  background-color: #f0f9ff;
  padding: 12px;
  border-radius: 4px;
  border: 1px solid #a7f3d0;
  
  p {
    margin: 4px 0;
    font-size: 13px;
    
    strong {
      color: #303133;
      font-weight: 500;
    }
  }
}

.strategy-info {
  font-size: 11px;
  color: #909399;
  margin-top: 2px;
}

.hsm-info {
  font-size: 11px;
  color: #67c23a;
  margin-top: 2px;
}
</style>