{"groups": [{"name": "docker.swarm", "type": "tech.tongdao.ccsp.management.config.DockerConfig$DockerSwarmProperties", "sourceType": "tech.tongdao.ccsp.management.config.DockerConfig$DockerSwarmProperties"}, {"name": "ignore", "type": "tech.tongdao.ccsp.management.config.IgnoreConfig", "sourceType": "tech.tongdao.ccsp.management.config.IgnoreConfig"}], "properties": [{"name": "docker.swarm.cert-path", "type": "java.lang.String", "sourceType": "tech.tongdao.ccsp.management.config.DockerConfig$DockerSwarmProperties"}, {"name": "docker.swarm.connection-timeout", "type": "java.lang.Integer", "sourceType": "tech.tongdao.ccsp.management.config.DockerConfig$DockerSwarmProperties"}, {"name": "docker.swarm.host", "type": "java.lang.String", "sourceType": "tech.tongdao.ccsp.management.config.DockerConfig$DockerSwarmProperties"}, {"name": "docker.swarm.max-connections", "type": "java.lang.Integer", "sourceType": "tech.tongdao.ccsp.management.config.DockerConfig$DockerSwarmProperties"}, {"name": "docker.swarm.response-timeout", "type": "java.lang.Integer", "sourceType": "tech.tongdao.ccsp.management.config.DockerConfig$DockerSwarmProperties"}, {"name": "docker.swarm.tls-verify", "type": "java.lang.Bo<PERSON>an", "sourceType": "tech.tongdao.ccsp.management.config.DockerConfig$DockerSwarmProperties"}, {"name": "ignore.app-codes", "type": "java.util.List<java.lang.String>", "sourceType": "tech.tongdao.ccsp.management.config.IgnoreConfig"}, {"name": "ignore.roles", "type": "java.util.List<java.lang.String>", "sourceType": "tech.tongdao.ccsp.management.config.IgnoreConfig"}, {"name": "ignore.tenant-codes", "type": "java.util.List<java.lang.String>", "sourceType": "tech.tongdao.ccsp.management.config.IgnoreConfig"}], "hints": []}