{"remainingRequest": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\src\\views\\docker\\container\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\src\\views\\docker\\container\\index.vue", "mtime": 1756804572859}, {"path": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1729062151209}, {"path": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1729062151198}, {"path": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1729062151209}, {"path": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1729062152627}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IG1hcEdldHRlcnMgfSBmcm9tICd2dWV4JwppbXBvcnQgUGFnaW5hdGlvbiBmcm9tICdAL2NvbXBvbmVudHMvUGFnaW5hdGlvbicKaW1wb3J0IHsgCiAgZ2V0Q29udGFpbmVyTGlzdCwgCiAgZGVwbG95Q29udGFpbmVyLCAKICBkZXBsb3lDb250YWluZXJXaXRoVHJhZWZpaywKICBkZXBsb3lDb250YWluZXJXaXRoU3RyYXRlZ3ksCiAgZ2V0UmVjb21tZW5kZWRTdHJhdGVneSwKICBzdGFydENvbnRhaW5lciwgCiAgc3RvcENvbnRhaW5lciwgCiAgcmVzdGFydENvbnRhaW5lciwgCiAgcmVtb3ZlQ29udGFpbmVyLCAKICBnZXRDb250YWluZXJMb2dzLCAKICBzeW5jQ29udGFpbmVyU3RhdHVzLCAKICBzY2FsZUNvbnRhaW5lciwgCiAgdXBkYXRlQ29udGFpbmVySW1hZ2UsCiAgY29uZmlnQ29udGFpbmVyUm91dGUsCiAgdXBkYXRlQ29udGFpbmVyUm91dGUKfSBmcm9tICdAL2FwaS9kb2NrZXIvY29udGFpbmVyJwppbXBvcnQgeyBnZXRJbWFnZUxpc3QgfSBmcm9tICdAL2FwaS9kb2NrZXIvaW1hZ2UnCmltcG9ydCB7IGZldGNoQXBwbGljYXRpb25PcHRpb25zLCBmZXRjaFVzZXJBcHBzIH0gZnJvbSAnQC9hcGkvYXBwbGljYXRpb24nCmltcG9ydCB7IGdldEF2YWlsYWJsZUhzbURldmljZXMsIGdldEhzbURldmljZURldGFpbCwgZGVwbG95Q29udGFpbmVyV2l0aEhzbSB9IGZyb20gJ0AvYXBpL2RvY2tlci9oc20nCgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ0RvY2tlckNvbnRhaW5lckluZGV4JywKICBjb21wb25lbnRzOiB7CiAgICBQYWdpbmF0aW9uCiAgfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgdGFibGVMaXN0OiBbXSwKICAgICAgbG9hZGluZzogZmFsc2UsCiAgICAgIHRvdGFsOiAwLAogICAgICBsaXN0UXVlcnk6IHsKICAgICAgICBwYWdlOiAxLAogICAgICAgIHBhZ2VTaXplOiAyMCwKICAgICAgICBjb250YWluZXJOYW1lOiB1bmRlZmluZWQsCiAgICAgICAgc3RhdHVzOiB1bmRlZmluZWQKICAgICAgfSwKICAgICAgbG9nc0RpYWxvZ1Zpc2libGU6IGZhbHNlLAogICAgICBjb250YWluZXJMb2dzOiAnJywKICAgICAgY3VycmVudENvbnRhaW5lcjogbnVsbCwKICAgICAgZGVwbG95RGlhbG9nVmlzaWJsZTogZmFsc2UsCiAgICAgIGRlcGxveUZvcm06IHsKICAgICAgICBjb250YWluZXJOYW1lOiAnJywKICAgICAgICBpbWFnZUlkOiBudWxsLAogICAgICAgIGltYWdlTmFtZTogJycsCiAgICAgICAgaW1hZ2VUYWc6ICdsYXRlc3QnLAogICAgICAgIHNlcnZpY2VQb3J0OiA4MCwKICAgICAgICByZXBsaWNhczogMSwKICAgICAgICBkaXN0cmlidXRpb25TdHJhdGVneTogJ1NQUkVBRF9BQ1JPU1NfTk9ERVMnLAogICAgICAgIGVuYWJsZVRyYWVmaWs6IHRydWUsCiAgICAgICAgZW5hYmxlSHNtOiBmYWxzZSwKICAgICAgICBoc21EZXZpY2VJZDogbnVsbCwKICAgICAgICBkZXBsb3llZEJ5OiBudWxsLAogICAgICAgIGFwcGxpY2F0aW9uSWQ6IG51bGwsCiAgICAgICAgYXBwbGljYXRpb25OYW1lOiAnJwogICAgICB9LAogICAgICBkZXBsb3lSdWxlczogewogICAgICAgIGFwcGxpY2F0aW9uSWQ6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36YCJ5oup5YWz6IGU5bqU55SoJywgdHJpZ2dlcjogJ2NoYW5nZScgfV0sCiAgICAgICAgY29udGFpbmVyTmFtZTogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaXlrrnlmajlkI3np7AnLCB0cmlnZ2VyOiAnYmx1cicgfV0sCiAgICAgICAgaW1hZ2VJZDogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fpgInmi6nplZzlg48nLCB0cmlnZ2VyOiAnY2hhbmdlJyB9XSwKICAgICAgICBzZXJ2aWNlUG9ydDogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaXmnI3liqHnq6/lj6MnLCB0cmlnZ2VyOiAnYmx1cicgfV0KICAgICAgfSwKICAgICAgc2NhbGVEaWFsb2dWaXNpYmxlOiBmYWxzZSwKICAgICAgc2NhbGVGb3JtOiB7CiAgICAgICAgaWQ6ICcnLAogICAgICAgIHJlcGxpY2FzOiAxLAogICAgICAgIGRpc3RyaWJ1dGlvblN0cmF0ZWd5OiAnU1BSRUFEX0FDUk9TU19OT0RFUycKICAgICAgfSwKICAgICAgc2NhbGVSdWxlczogewogICAgICAgIHJlcGxpY2FzOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpeWJr+acrOaVsOmHjycsIHRyaWdnZXI6ICdibHVyJyB9XQogICAgICB9LAogICAgICAvLyDplZzlg4/nm7jlhbPmlbDmja4KICAgICAgaW1hZ2VMaXN0OiBbXSwKICAgICAgaW1hZ2VMaXN0TG9hZGluZzogZmFsc2UsCiAgICAgIHNlbGVjdGVkSW1hZ2U6IG51bGwsCiAgICAgIC8vIEhTTeiuvuWkh+ebuOWFs+aVsOaNrgogICAgICBoc21EZXZpY2VMaXN0OiBbXSwKICAgICAgaHNtRGV2aWNlTGlzdExvYWRpbmc6IGZhbHNlLAogICAgICBzZWxlY3RlZEhzbURldmljZTogbnVsbCwKICAgICAgLy8g5bqU55So5LiL5ouJCiAgICAgIGFwcE9wdGlvbnM6IFtdLAogICAgICBhcHBPcHRpb25zTG9hZGluZzogZmFsc2UKICAgIH0KICB9LAogIG1ldGhvZHM6IHsKICAgIGhhbmRsZVNlYXJjaCgpIHsKICAgICAgdGhpcy5nZXRMaXN0KCkKICAgIH0sCiAgICBoYW5kbGVSZXNldFNlYXJjaCgpIHsKICAgICAgdGhpcy5saXN0UXVlcnkgPSB7CiAgICAgICAgcGFnZTogMSwKICAgICAgICBwYWdlU2l6ZTogMjAsCiAgICAgICAgY29udGFpbmVyTmFtZTogdW5kZWZpbmVkLAogICAgICAgIHN0YXR1czogdW5kZWZpbmVkCiAgICAgIH0KICAgICAgdGhpcy5nZXRMaXN0KCkKICAgIH0sCiAgICBoYW5kbGVEZXBsb3lDb250YWluZXIoKSB7CiAgICAgIC8vIOmmluWFiOWKoOi9vemVnOWDj+WIl+ihqAogICAgICBQcm9taXNlLmFsbChbdGhpcy5sb2FkSW1hZ2VMaXN0KCksIHRoaXMubG9hZEFwcE9wdGlvbnMoKV0pLnRoZW4oKCkgPT4gewogICAgICAgIHRoaXMuZGVwbG95Rm9ybSA9IHsKICAgICAgICAgIGNvbnRhaW5lck5hbWU6ICcnLAogICAgICAgICAgaW1hZ2VJZDogbnVsbCwKICAgICAgICAgIGltYWdlTmFtZTogJycsCiAgICAgICAgICBpbWFnZVRhZzogJ2xhdGVzdCcsCiAgICAgICAgICBzZXJ2aWNlUG9ydDogODAsCiAgICAgICAgICByZXBsaWNhczogMSwKICAgICAgICAgIGRpc3RyaWJ1dGlvblN0cmF0ZWd5OiAnU1BSRUFEX0FDUk9TU19OT0RFUycsCiAgICAgICAgICBlbmFibGVUcmFlZmlrOiB0cnVlLAogICAgICAgICAgZW5hYmxlSHNtOiBmYWxzZSwKICAgICAgICAgIGhzbURldmljZUlkOiBudWxsLAogICAgICAgICAgZGVwbG95ZWRCeTogdGhpcy51c2VyID8gdGhpcy51c2VyLmlkIDogbnVsbCwKICAgICAgICAgIGFwcGxpY2F0aW9uSWQ6IG51bGwsCiAgICAgICAgICBhcHBsaWNhdGlvbk5hbWU6ICcnCiAgICAgICAgfQogICAgICAgIHRoaXMuc2VsZWN0ZWRJbWFnZSA9IG51bGwKICAgICAgICB0aGlzLnNlbGVjdGVkSHNtRGV2aWNlID0gbnVsbAogICAgICAgIHRoaXMuZGVwbG95RGlhbG9nVmlzaWJsZSA9IHRydWUKICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgICB0aGlzLiRyZWZzWydkZXBsb3lGb3JtJ10uY2xlYXJWYWxpZGF0ZSgpCiAgICAgICAgfSkKICAgICAgfSkKICAgIH0sCiAgICBjb25maXJtRGVwbG95Q29udGFpbmVyKCkgewogICAgICB0aGlzLiRyZWZzWydkZXBsb3lGb3JtJ10udmFsaWRhdGUoKHZhbGlkKSA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICAvLyDmo4Dmn6VIU03orr7lpIfphY3nva4KICAgICAgICAgIGlmICh0aGlzLmRlcGxveUZvcm0uZW5hYmxlSHNtICYmICF0aGlzLmRlcGxveUZvcm0uaHNtRGV2aWNlSWQpIHsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign6K+36YCJ5oupSFNN6K6+5aSHJykKICAgICAgICAgICAgcmV0dXJuCiAgICAgICAgICB9CiAgICAgICAgICAKICAgICAgICAgIGNvbnN0IGRlcGxveURhdGEgPSB7IC4uLnRoaXMuZGVwbG95Rm9ybSB9CiAgICAgICAgICAKICAgICAgICAgIC8vIOehruS/nemVnOWDj+S/oeaBr+WujOaVtAogICAgICAgICAgaWYgKHRoaXMuc2VsZWN0ZWRJbWFnZSkgewogICAgICAgICAgICBkZXBsb3lEYXRhLmltYWdlTmFtZSA9IHRoaXMuc2VsZWN0ZWRJbWFnZS5pbWFnZU5hbWUKICAgICAgICAgICAgZGVwbG95RGF0YS5pbWFnZVRhZyA9IHRoaXMuc2VsZWN0ZWRJbWFnZS5pbWFnZVRhZwogICAgICAgICAgfQogICAgICAgICAgCiAgICAgICAgICAvLyDmnoTlu7pIU03orr7lpIfphY3nva4KICAgICAgICAgIGlmICh0aGlzLmRlcGxveUZvcm0uZW5hYmxlSHNtICYmIHRoaXMuc2VsZWN0ZWRIc21EZXZpY2UpIHsKICAgICAgICAgICAgZGVwbG95RGF0YS5oc21EZXZpY2VDb25maWcgPSB7CiAgICAgICAgICAgICAgZW5jcnlwdG9yR3JvdXBJZDogMSwgLy8g6buY6K6k57uESUQKICAgICAgICAgICAgICBlbmNyeXB0b3JJZDogdGhpcy5zZWxlY3RlZEhzbURldmljZS5kZXZpY2VJZCwKICAgICAgICAgICAgICBlbmNyeXB0b3JOYW1lOiB0aGlzLnNlbGVjdGVkSHNtRGV2aWNlLmRldmljZU5hbWUsCiAgICAgICAgICAgICAgc2VydmVySXBBZGRyOiB0aGlzLnNlbGVjdGVkSHNtRGV2aWNlLmlwQWRkcmVzcywKICAgICAgICAgICAgICBzZXJ2ZXJQb3J0OiB0aGlzLnNlbGVjdGVkSHNtRGV2aWNlLnNlcnZpY2VQb3J0LAogICAgICAgICAgICAgIHRjcENvbm5OdW06IHRoaXMuc2VsZWN0ZWRIc21EZXZpY2UudGNwQ29ubk51bSB8fCA1LAogICAgICAgICAgICAgIG1zZ0hlYWRMZW46IHRoaXMuc2VsZWN0ZWRIc21EZXZpY2UubXNnSGVhZExlbiB8fCA0LAogICAgICAgICAgICAgIG1zZ1RhaWxMZW46IHRoaXMuc2VsZWN0ZWRIc21EZXZpY2UubXNnVGFpbExlbiB8fCAwLAogICAgICAgICAgICAgIGFzY2lpT3JFYmNkaWM6IHRoaXMuc2VsZWN0ZWRIc21EZXZpY2UuZW5jb2RpbmcgfHwgMCwKICAgICAgICAgICAgICBkeW5hbWljTGliUGF0aDogJy4vbGliZGV2aWNlYXBpLnNvJwogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgICAKICAgICAgICAgIC8vIOagueaNrumFjee9rumAieaLqemDqOe9suaOpeWPowogICAgICAgICAgbGV0IGRlcGxveUZuCiAgICAgICAgICBpZiAodGhpcy5kZXBsb3lGb3JtLmVuYWJsZUhzbSkgewogICAgICAgICAgICAvLyDkvb/nlKjluKZIU03phY3nva7nmoTpg6jnvbLmjqXlj6MKICAgICAgICAgICAgY29uc29sZS5sb2coJ+S9v+eUqOW4pkhTTemFjee9rueahOmDqOe9suaOpeWPoycpCiAgICAgICAgICAgIGRlcGxveUZuID0gdGhpcy5kZXBsb3lXaXRoSHNtCiAgICAgICAgICB9IGVsc2UgaWYgKGRlcGxveURhdGEucmVwbGljYXMgPiAxICYmIGRlcGxveURhdGEuZGlzdHJpYnV0aW9uU3RyYXRlZ3kpIHsKICAgICAgICAgICAgLy8g5L2/55So5bim5YiG5biD562W55Wl55qE6YOo572y5o6l5Y+jCiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfkvb/nlKjluKbliIbluIPnrZbnlaXnmoTpg6jnvbLmjqXlj6MnKQogICAgICAgICAgICBkZXBsb3lGbiA9IGRlcGxveUNvbnRhaW5lcldpdGhTdHJhdGVneQogICAgICAgICAgfSBlbHNlIGlmIChkZXBsb3lEYXRhLmVuYWJsZVRyYWVmaWspIHsKICAgICAgICAgICAgLy8g5L2/55So5bimVHJhZWZpa+eahOmDqOe9suaOpeWPowogICAgICAgICAgICBjb25zb2xlLmxvZygn5L2/55So5bimVHJhZWZpa+eahOmDqOe9suaOpeWPoycpCiAgICAgICAgICAgIGRlcGxveUZuID0gdGhpcy5kZXBsb3lXaXRoVHJhZWZpawogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgLy8g5L2/55So5Z+65pys6YOo572y5o6l5Y+jCiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfkvb/nlKjln7rmnKzpg6jnvbLmjqXlj6MnKQogICAgICAgICAgICBkZXBsb3lGbiA9IGRlcGxveUNvbnRhaW5lcgogICAgICAgICAgfQogICAgICAgICAgCiAgICAgICAgICBkZXBsb3lGbihkZXBsb3lEYXRhKS50aGVuKChyZXNwb25zZSkgPT4gewogICAgICAgICAgICB0aGlzLmRlcGxveURpYWxvZ1Zpc2libGUgPSBmYWxzZQogICAgICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwMDApIHsKICAgICAgICAgICAgICBpZiAocmVzcG9uc2UuZGF0YSkgewogICAgICAgICAgICAgICAgLy8g5aSE55CG5LiN5ZCM55qE5ZON5bqU57uT5p6ECiAgICAgICAgICAgICAgICBsZXQgbWVzc2FnZSA9ICflrrnlmajpg6jnvbLmiJDlip/vvIEnCiAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgIGlmIChyZXNwb25zZS5kYXRhLmFjY2Vzc1VybCkgewogICAgICAgICAgICAgICAgICBtZXNzYWdlICs9IGDorr/pl67lnLDlnYDvvJoke3Jlc3BvbnNlLmRhdGEuYWNjZXNzVXJsfWAKICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAocmVzcG9uc2UuZGF0YS5jb250YWluZXIgJiYgcmVzcG9uc2UuZGF0YS5jb250YWluZXIuYWNjZXNzVXJsKSB7CiAgICAgICAgICAgICAgICAgIG1lc3NhZ2UgKz0gYOiuv+mXruWcsOWdgO+8miR7cmVzcG9uc2UuZGF0YS5jb250YWluZXIuYWNjZXNzVXJsfWAKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEuZGlzdHJpYnV0aW9uU3RyYXRlZ3kpIHsKICAgICAgICAgICAgICAgICAgbWVzc2FnZSArPSBg77yM5YiG5biD562W55Wl77yaJHt0aGlzLmdldFN0cmF0ZWd5RGlzcGxheU5hbWUocmVzcG9uc2UuZGF0YS5kaXN0cmlidXRpb25TdHJhdGVneSl9YAogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICBpZiAocmVzcG9uc2UuZGF0YS5oc21Db25maWd1cmVkKSB7CiAgICAgICAgICAgICAgICAgIG1lc3NhZ2UgKz0gYO+8jEhTTeiuvuWkh++8miR7dGhpcy5zZWxlY3RlZEhzbURldmljZS5kZXZpY2VOYW1lfWAKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKG1lc3NhZ2UpCiAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5a655Zmo6YOo572y5Lu75Yqh5bey5o+Q5LqkJykKICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCkKICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlc3BvbnNlLm1lc3NhZ2UgfHwgJ+mDqOe9suWksei0pScpCiAgICAgICAgICAgIH0KICAgICAgICAgIH0pLmNhdGNoKCgpID0+IHsKICAgICAgICAgICAgLy8g6ZSZ6K+v5aSE55CG5bey5Zyo5oum5oiq5Zmo5Lit5aSE55CGCiAgICAgICAgICB9KQogICAgICAgIH0KICAgICAgfSkKICAgIH0sCgogICAgLy8g5Yqg6L295bqU55So6YCJ6aG5CiAgICBhc3luYyBsb2FkQXBwT3B0aW9ucygpIHsKICAgICAgdGhpcy5hcHBPcHRpb25zTG9hZGluZyA9IHRydWUKICAgICAgdHJ5IHsKICAgICAgICBsZXQgcmVzcAogICAgICAgIC8vIOenn+aIt+inkuiJsuS9v+eUqCB1c2VyL2xpc3Qtb3B0aW9uc++8jOWFtuWug+S9v+eUqCBmZXRjaC1vcHRpb25zCiAgICAgICAgaWYgKHRoaXMudXNlciAmJiBBcnJheS5pc0FycmF5KHRoaXMudXNlci5yb2xlcykgJiYgdGhpcy51c2VyLnJvbGVzLmluY2x1ZGVzKCdST0xFX1RFTkFOVCcpKSB7CiAgICAgICAgICByZXNwID0gYXdhaXQgZmV0Y2hVc2VyQXBwcygpCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHJlc3AgPSBhd2FpdCBmZXRjaEFwcGxpY2F0aW9uT3B0aW9ucygpCiAgICAgICAgfQogICAgICAgIGlmIChyZXNwICYmIHJlc3AuY29kZSA9PT0gMjAwMDApIHsKICAgICAgICAgIGNvbnN0IGRhdGEgPSBBcnJheS5pc0FycmF5KHJlc3AuZGF0YSkgPyByZXNwLmRhdGEgOiAocmVzcC5kYXRhICYmIHJlc3AuZGF0YS5saXN0KSA/IHJlc3AuZGF0YS5saXN0IDogW10KICAgICAgICAgIHRoaXMuYXBwT3B0aW9ucyA9IGRhdGEubWFwKGl0ZW0gPT4gewogICAgICAgICAgICBpZiAoaXRlbS5sYWJlbCAmJiAoaXRlbS52YWx1ZSAhPT0gdW5kZWZpbmVkKSkgcmV0dXJuIGl0ZW0KICAgICAgICAgICAgcmV0dXJuIHsgbGFiZWw6IGl0ZW0ubmFtZSB8fCBpdGVtLmFwcGxpY2F0aW9uTmFtZSB8fCBpdGVtLmxhYmVsLCB2YWx1ZTogU3RyaW5nKGl0ZW0uaWQgfHwgaXRlbS52YWx1ZSkgfQogICAgICAgICAgfSkKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy5hcHBPcHRpb25zID0gW10KICAgICAgICB9CiAgICAgIH0gY2F0Y2ggKGUpIHsKICAgICAgICB0aGlzLmFwcE9wdGlvbnMgPSBbXQogICAgICB9IGZpbmFsbHkgewogICAgICAgIHRoaXMuYXBwT3B0aW9uc0xvYWRpbmcgPSBmYWxzZQogICAgICB9CiAgICB9LAogICAgaGFuZGxlU3RhcnRDb250YWluZXIocm93KSB7CiAgICAgIHN0YXJ0Q29udGFpbmVyKHJvdy5pZCkudGhlbigoKSA9PiB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCflrrnlmajlkK/liqjmiJDlip8nKQogICAgICAgIHRoaXMuZ2V0TGlzdCgpCiAgICAgIH0pCiAgICB9LAogICAgaGFuZGxlU3RvcENvbnRhaW5lcihyb3cpIHsKICAgICAgc3RvcENvbnRhaW5lcihyb3cuaWQpLnRoZW4oKCkgPT4gewogICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5a655Zmo5YGc5q2i5oiQ5YqfJykKICAgICAgICB0aGlzLmdldExpc3QoKQogICAgICB9KQogICAgfSwKICAgIGhhbmRsZVJlc3RhcnRDb250YWluZXIocm93KSB7CiAgICAgIHJlc3RhcnRDb250YWluZXIocm93LmlkKS50aGVuKCgpID0+IHsKICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+WuueWZqOmHjeWQr+aIkOWKnycpCiAgICAgICAgdGhpcy5nZXRMaXN0KCkKICAgICAgfSkKICAgIH0sCiAgICBoYW5kbGVSZW1vdmVDb250YWluZXIocm93KSB7CiAgICAgIHRoaXMuJG1zZ2JveC5jb25maXJtKGDnoa7orqTopoHliKDpmaTlrrnlmaggJHtyb3cuY29udGFpbmVyTmFtZX0g5ZCX77yfYCwgJ+aPkOekuicsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsCiAgICAgICAgdHlwZTogJ3dhcm5pbmcnCiAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgIHJlbW92ZUNvbnRhaW5lcihyb3cuaWQpLnRoZW4oKCkgPT4gewogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfliKDpmaTmiJDlip8nKQogICAgICAgICAgdGhpcy5nZXRMaXN0KCkKICAgICAgICB9KQogICAgICB9KS5jYXRjaCgoKSA9PiB7CiAgICAgICAgLy8g5Y+W5raI5Yig6ZmkCiAgICAgIH0pCiAgICB9LAogICAgaGFuZGxlU2NhbGVDb250YWluZXIocm93KSB7CiAgICAgIHRoaXMuc2NhbGVGb3JtID0gewogICAgICAgIGlkOiByb3cuaWQsCiAgICAgICAgcmVwbGljYXM6IHJvdy5yZXBsaWNhcyB8fCAxLCAvLyDkvb/nlKjlvZPliY3lia/mnKzmlbDmiJbpu5jorqTlgLwKICAgICAgICBkaXN0cmlidXRpb25TdHJhdGVneTogcm93LmRpc3RyaWJ1dGlvblN0cmF0ZWd5IHx8ICdTUFJFQURfQUNST1NTX05PREVTJwogICAgICB9CiAgICAgIHRoaXMuc2NhbGVEaWFsb2dWaXNpYmxlID0gdHJ1ZQogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgdGhpcy4kcmVmc1snc2NhbGVGb3JtJ10uY2xlYXJWYWxpZGF0ZSgpCiAgICAgIH0pCiAgICB9LAogICAgY29uZmlybVNjYWxlQ29udGFpbmVyKCkgewogICAgICB0aGlzLiRyZWZzWydzY2FsZUZvcm0nXS52YWxpZGF0ZSgodmFsaWQpID0+IHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIHNjYWxlQ29udGFpbmVyKHRoaXMuc2NhbGVGb3JtLmlkLCB0aGlzLnNjYWxlRm9ybS5yZXBsaWNhcykudGhlbigoKSA9PiB7CiAgICAgICAgICAgIHRoaXMuc2NhbGVEaWFsb2dWaXNpYmxlID0gZmFsc2UKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCflrrnlmajmiannvKnlrrnku7vliqHlt7Lmj5DkuqQnKQogICAgICAgICAgICB0aGlzLmdldExpc3QoKQogICAgICAgICAgfSkKICAgICAgICB9CiAgICAgIH0pCiAgICB9LAogICAgaGFuZGxlVmlld0xvZ3Mocm93KSB7CiAgICAgIHRoaXMuY3VycmVudENvbnRhaW5lciA9IHJvdwogICAgICBnZXRDb250YWluZXJMb2dzKHJvdy5pZCkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMDAwKSB7CiAgICAgICAgICB0aGlzLmNvbnRhaW5lckxvZ3MgPSByZXNwb25zZS5kYXRhIHx8ICcnCiAgICAgICAgICB0aGlzLmxvZ3NEaWFsb2dWaXNpYmxlID0gdHJ1ZQogICAgICAgIH0KICAgICAgfSkKICAgIH0sCiAgICBoYW5kbGVTeW5jU3RhdHVzKCkgewogICAgICBzeW5jQ29udGFpbmVyU3RhdHVzKCkudGhlbigoKSA9PiB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCflrrnlmajnirbmgIHlkIzmraXku7vliqHlt7Lmj5DkuqQnKQogICAgICAgIHRoaXMuZ2V0TGlzdCgpCiAgICAgIH0pCiAgICB9LAogICAgZ2V0TGlzdCgpIHsKICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZQogICAgICBnZXRDb250YWluZXJMaXN0KHRoaXMubGlzdFF1ZXJ5KS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwMDApIHsKICAgICAgICAgIHRoaXMudGFibGVMaXN0ID0gcmVzcG9uc2UuZGF0YS5saXN0CiAgICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UuZGF0YS50b3RhbENvdW50CiAgICAgICAgfQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlCiAgICAgIH0pLmNhdGNoKCgpID0+IHsKICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZQogICAgICB9KQogICAgfSwKICAgIC8vIOagvOW8j+WMluerr+WPo+aYoOWwhOaYvuekugogICAgZm9ybWF0UG9ydHMocG9ydE1hcHBpbmdzKSB7CiAgICAgIGlmICghcG9ydE1hcHBpbmdzKSByZXR1cm4gJy0nCiAgICAgIHRyeSB7CiAgICAgICAgY29uc3QgcG9ydHMgPSBKU09OLnBhcnNlKHBvcnRNYXBwaW5ncykKICAgICAgICBpZiAoQXJyYXkuaXNBcnJheShwb3J0cykgJiYgcG9ydHMubGVuZ3RoID4gMCkgewogICAgICAgICAgcmV0dXJuIHBvcnRzLm1hcChwID0+IGAke3AuaG9zdFBvcnQgfHwgJyd9OiR7cC5jb250YWluZXJQb3J0fS8ke3AucHJvdG9jb2wgfHwgJ3RjcCd9YCkuam9pbignLCAnKQogICAgICAgIH0KICAgICAgICByZXR1cm4gJy0nCiAgICAgIH0gY2F0Y2ggKGUpIHsKICAgICAgICByZXR1cm4gcG9ydE1hcHBpbmdzCiAgICAgIH0KICAgIH0sCiAgICAvLyDmoLzlvI/ljJbml6XmnJ/mmL7npLoKICAgIGZvcm1hdERhdGUoZGF0ZVRpbWUpIHsKICAgICAgaWYgKCFkYXRlVGltZSkgcmV0dXJuICctJwogICAgICByZXR1cm4gbmV3IERhdGUoZGF0ZVRpbWUpLnRvTG9jYWxlU3RyaW5nKCd6aC1DTicpCiAgICB9LAogICAgCiAgICAvLyDlpI3liLbliLDliarotLTmnb8KICAgIGNvcHlUb0NsaXBib2FyZCh0ZXh0KSB7CiAgICAgIGlmIChuYXZpZ2F0b3IuY2xpcGJvYXJkKSB7CiAgICAgICAgbmF2aWdhdG9yLmNsaXBib2FyZC53cml0ZVRleHQodGV4dCkudGhlbigoKSA9PiB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+WcsOWdgOW3suWkjeWItuWIsOWJqui0tOadvycpCiAgICAgICAgfSkKICAgICAgfSBlbHNlIHsKICAgICAgICAvLyDpmY3nuqfmlrnmoYgKICAgICAgICBjb25zdCB0ZXh0QXJlYSA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ3RleHRhcmVhJykKICAgICAgICB0ZXh0QXJlYS52YWx1ZSA9IHRleHQKICAgICAgICBkb2N1bWVudC5ib2R5LmFwcGVuZENoaWxkKHRleHRBcmVhKQogICAgICAgIHRleHRBcmVhLnNlbGVjdCgpCiAgICAgICAgZG9jdW1lbnQuZXhlY0NvbW1hbmQoJ2NvcHknKQogICAgICAgIGRvY3VtZW50LmJvZHkucmVtb3ZlQ2hpbGQodGV4dEFyZWEpCiAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCflnLDlnYDlt7LlpI3liLbliLDliarotLTmnb8nKQogICAgICB9CiAgICB9LAogICAgCiAgICAvLyDphY3nva7orr/pl67ot6/nlLEKICAgIGhhbmRsZUNvbmZpZ1JvdXRlKHJvdykgewogICAgICB0aGlzLiRwcm9tcHQoJ+ivt+i+k+WFpeacjeWKoeerr+WPo++8iOWuueWZqOWGhemDqOerr+WPo++8iScsICfphY3nva7orr/pl67ot6/nlLEnLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLAogICAgICAgIGlucHV0VmFsdWU6ICc4MCcsCiAgICAgICAgaW5wdXRWYWxpZGF0b3I6ICh2YWx1ZSkgPT4gewogICAgICAgICAgY29uc3QgcG9ydCA9IHBhcnNlSW50KHZhbHVlKQogICAgICAgICAgaWYgKCFwb3J0IHx8IHBvcnQgPCAxIHx8IHBvcnQgPiA2NTUzNSkgewogICAgICAgICAgICByZXR1cm4gJ+ivt+i+k+WFpeacieaViOeahOerr+WPo+WPtygxLTY1NTM1KScKICAgICAgICAgIH0KICAgICAgICAgIHJldHVybiB0cnVlCiAgICAgICAgfQogICAgICB9KS50aGVuKCh7IHZhbHVlIH0pID0+IHsKICAgICAgICBjb25zdCByb3V0ZUNvbmZpZyA9IHsKICAgICAgICAgIGNvbnRhaW5lcklkOiByb3cuaWQsCiAgICAgICAgICBzZXJ2aWNlUG9ydDogcGFyc2VJbnQodmFsdWUpLAogICAgICAgICAgZW5hYmxlVHJhZWZpazogdHJ1ZQogICAgICAgIH0KICAgICAgICAKICAgICAgICB0aGlzLmNvbmZpZ0NvbnRhaW5lclJvdXRlKHJvdXRlQ29uZmlnKS50aGVuKChyZXNwb25zZSkgPT4gewogICAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMDAwICYmIHJlc3BvbnNlLmRhdGEgJiYgcmVzcG9uc2UuZGF0YS5hY2Nlc3NVcmwpIHsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKGDot6/nlLHphY3nva7miJDlip/vvIHorr/pl67lnLDlnYDvvJoke3Jlc3BvbnNlLmRhdGEuYWNjZXNzVXJsfWApCiAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpCiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlc3BvbnNlLm1lc3NhZ2UgfHwgJ+mFjee9ruWksei0pScpCiAgICAgICAgICB9CiAgICAgICAgfSkuY2F0Y2goKCkgPT4gewogICAgICAgICAgLy8g6ZSZ6K+v5aSE55CG5bey5Zyo5oum5oiq5Zmo5Lit5aSE55CGCiAgICAgICAgfSkKICAgICAgfSkuY2F0Y2goKCkgPT4gewogICAgICAgIC8vIOWPlua2iOaTjeS9nAogICAgICB9KQogICAgfSwKICAgIAogICAgLy8g5pu05paw6Lev55Sx6YWN572uCiAgICBoYW5kbGVVcGRhdGVSb3V0ZShyb3cpIHsKICAgICAgdGhpcy4kcHJvbXB0KCfor7fovpPlhaXmlrDnmoTmnI3liqHnq6/lj6MnLCAn5pu05paw6Lev55SxJywgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywKICAgICAgICBpbnB1dFZhbGlkYXRvcjogKHZhbHVlKSA9PiB7CiAgICAgICAgICBjb25zdCBwb3J0ID0gcGFyc2VJbnQodmFsdWUpCiAgICAgICAgICBpZiAoIXBvcnQgfHwgcG9ydCA8IDEgfHwgcG9ydCA+IDY1NTM1KSB7CiAgICAgICAgICAgIHJldHVybiAn6K+36L6T5YWl5pyJ5pWI55qE56uv5Y+j5Y+3KDEtNjU1MzUpJwogICAgICAgICAgfQogICAgICAgICAgcmV0dXJuIHRydWUKICAgICAgICB9CiAgICAgIH0pLnRoZW4oKHsgdmFsdWUgfSkgPT4gewogICAgICAgIGNvbnN0IHJvdXRlQ29uZmlnID0gewogICAgICAgICAgY29udGFpbmVySWQ6IHJvdy5pZCwKICAgICAgICAgIHNlcnZpY2VQb3J0OiBwYXJzZUludCh2YWx1ZSkKICAgICAgICB9CiAgICAgICAgCiAgICAgICAgdGhpcy51cGRhdGVDb250YWluZXJSb3V0ZShyb3V0ZUNvbmZpZykudGhlbigocmVzcG9uc2UpID0+IHsKICAgICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDAwMCAmJiByZXNwb25zZS5kYXRhICYmIHJlc3BvbnNlLmRhdGEuYWNjZXNzVXJsKSB7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcyhg6Lev55Sx5pu05paw5oiQ5Yqf77yB5paw5Zyw5Z2A77yaJHtyZXNwb25zZS5kYXRhLmFjY2Vzc1VybH1gKQogICAgICAgICAgICB0aGlzLmdldExpc3QoKQogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXNwb25zZS5tZXNzYWdlIHx8ICfmm7TmlrDlpLHotKUnKQogICAgICAgICAgfQogICAgICAgIH0pLmNhdGNoKCgpID0+IHsKICAgICAgICAgIC8vIOmUmeivr+WkhOeQhuW3suWcqOaLpuaIquWZqOS4reWkhOeQhgogICAgICAgIH0pCiAgICAgIH0pLmNhdGNoKCgpID0+IHsKICAgICAgICAvLyDlj5bmtojmk43kvZwKICAgICAgfSkKICAgIH0sCiAgICAKICAgIC8vIOW4plRyYWVmaWvnmoTpg6jnvbLmlrnms5UKICAgIGFzeW5jIGRlcGxveVdpdGhUcmFlZmlrKGRlcGxveURhdGEpIHsKICAgICAgcmV0dXJuIGRlcGxveUNvbnRhaW5lcldpdGhUcmFlZmlrKGRlcGxveURhdGEpCiAgICB9LAogICAgCiAgICAvLyDphY3nva7lrrnlmajot6/nlLHmlrnms5UKICAgIGFzeW5jIGNvbmZpZ0NvbnRhaW5lclJvdXRlKHJvdXRlQ29uZmlnKSB7CiAgICAgIHJldHVybiBjb25maWdDb250YWluZXJSb3V0ZShyb3V0ZUNvbmZpZykKICAgIH0sCiAgICAKICAgIC8vIOabtOaWsOWuueWZqOi3r+eUseaWueazlQogICAgYXN5bmMgdXBkYXRlQ29udGFpbmVyUm91dGUocm91dGVDb25maWcpIHsKICAgICAgcmV0dXJuIHVwZGF0ZUNvbnRhaW5lclJvdXRlKHJvdXRlQ29uZmlnKQogICAgfSwKICAgIAogICAgLy8g5Yqg6L296ZWc5YOP5YiX6KGoCiAgICBhc3luYyBsb2FkSW1hZ2VMaXN0KCkgewogICAgICB0aGlzLmltYWdlTGlzdExvYWRpbmcgPSB0cnVlCiAgICAgIHRyeSB7CiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBnZXRJbWFnZUxpc3QoKQogICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDAwMCkgewogICAgICAgICAgLy8g5aSE55CG5YiG6aG15pWw5o2u57uT5p6ECiAgICAgICAgICBpZiAocmVzcG9uc2UuZGF0YSAmJiByZXNwb25zZS5kYXRhLmxpc3QpIHsKICAgICAgICAgICAgLy8g5aaC5p6c6L+U5Zue55qE5piv5YiG6aG157uT5p6ECiAgICAgICAgICAgIHRoaXMuaW1hZ2VMaXN0ID0gcmVzcG9uc2UuZGF0YS5saXN0Lm1hcChpbWFnZSA9PiB7CiAgICAgICAgICAgICAgcmV0dXJuIHsKICAgICAgICAgICAgICAgIC4uLmltYWdlLAogICAgICAgICAgICAgICAgLy8g56Gu5L+dIHNpemVNYiDmmK/mlbDlrZfnsbvlnosKICAgICAgICAgICAgICAgIHNpemVNYjogdHlwZW9mIGltYWdlLnNpemVNYiA9PT0gJ3N0cmluZycgPyBwYXJzZUludChpbWFnZS5zaXplTWIpIDogaW1hZ2Uuc2l6ZU1iCiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9KQogICAgICAgICAgfSBlbHNlIGlmIChBcnJheS5pc0FycmF5KHJlc3BvbnNlLmRhdGEpKSB7CiAgICAgICAgICAgIC8vIOWmguaenOi/lOWbnueahOaYr+ebtOaOpeaVsOe7hAogICAgICAgICAgICB0aGlzLmltYWdlTGlzdCA9IHJlc3BvbnNlLmRhdGEubWFwKGltYWdlID0+IHsKICAgICAgICAgICAgICByZXR1cm4gewogICAgICAgICAgICAgICAgLi4uaW1hZ2UsCiAgICAgICAgICAgICAgICBzaXplTWI6IHR5cGVvZiBpbWFnZS5zaXplTWIgPT09ICdzdHJpbmcnID8gcGFyc2VJbnQoaW1hZ2Uuc2l6ZU1iKSA6IGltYWdlLnNpemVNYgogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSkKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIHRoaXMuaW1hZ2VMaXN0ID0gW10KICAgICAgICAgIH0KICAgICAgICAgIAogICAgICAgICAgaWYgKHRoaXMuaW1hZ2VMaXN0Lmxlbmd0aCA9PT0gMCkgewogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmluZm8oJ+W9k+WJjeayoeacieWPr+eUqOeahOmVnOWDj++8jOivt+WFiOaehOW7uuaIluWvvOWFpemVnOWDjycpCiAgICAgICAgICB9CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+iOt+WPlumVnOWDj+WIl+ihqOWksei0pe+8micgKyAocmVzcG9uc2UubWVzc2FnZSB8fCAn5pyq55+l6ZSZ6K+vJykpCiAgICAgICAgICB0aGlzLmltYWdlTGlzdCA9IFtdCiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+WKoOi9vemVnOWDj+WIl+ihqOWksei0pTonLCBlcnJvcikKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfliqDovb3plZzlg4/liJfooajlpLHotKUnKQogICAgICAgIHRoaXMuaW1hZ2VMaXN0ID0gW10KICAgICAgfSBmaW5hbGx5IHsKICAgICAgICB0aGlzLmltYWdlTGlzdExvYWRpbmcgPSBmYWxzZQogICAgICB9CiAgICB9LAogICAgCiAgICAvLyDlpITnkIbplZzlg4/pgInmi6nlj5jljJYKICAgIGhhbmRsZUltYWdlQ2hhbmdlKGltYWdlSWQpIHsKICAgICAgaWYgKGltYWdlSWQpIHsKICAgICAgICB0aGlzLnNlbGVjdGVkSW1hZ2UgPSB0aGlzLmltYWdlTGlzdC5maW5kKGltZyA9PiBpbWcuaWQgPT09IGltYWdlSWQpCiAgICAgICAgaWYgKHRoaXMuc2VsZWN0ZWRJbWFnZSkgewogICAgICAgICAgLy8g6Ieq5Yqo5aGr5YWF6ZWc5YOP5ZCN56ew5ZKM5qCH562+CiAgICAgICAgICB0aGlzLmRlcGxveUZvcm0uaW1hZ2VOYW1lID0gdGhpcy5zZWxlY3RlZEltYWdlLm5hbWUKICAgICAgICAgIHRoaXMuZGVwbG95Rm9ybS5pbWFnZVRhZyA9IHRoaXMuc2VsZWN0ZWRJbWFnZS50YWcKICAgICAgICAgIAogICAgICAgICAgLy8g5pm66IO955Sf5oiQ5a655Zmo5ZCN56ew77yI5aaC5p6c5b2T5YmN5Li656m677yJCiAgICAgICAgICBpZiAoIXRoaXMuZGVwbG95Rm9ybS5jb250YWluZXJOYW1lKSB7CiAgICAgICAgICAgIHRoaXMuc3VnZ2VzdENvbnRhaW5lck5hbWUodGhpcy5zZWxlY3RlZEltYWdlLm5hbWUsIHRoaXMuc2VsZWN0ZWRJbWFnZS50YWcpCiAgICAgICAgICB9CiAgICAgICAgICAKICAgICAgICAgIC8vIOagueaNrumVnOWDj+exu+Wei+aZuuiDveaOqOiNkOerr+WPowogICAgICAgICAgdGhpcy5zdWdnZXN0U2VydmljZVBvcnQodGhpcy5zZWxlY3RlZEltYWdlLm5hbWUpCiAgICAgICAgfQogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuc2VsZWN0ZWRJbWFnZSA9IG51bGwKICAgICAgICB0aGlzLmRlcGxveUZvcm0uaW1hZ2VOYW1lID0gJycKICAgICAgICB0aGlzLmRlcGxveUZvcm0uaW1hZ2VUYWcgPSAnbGF0ZXN0JwogICAgICB9CiAgICB9LAogICAgCiAgICAvLyDmmbrog73nlJ/miJDlrrnlmajlkI3np7AKICAgIHN1Z2dlc3RDb250YWluZXJOYW1lKGltYWdlTmFtZSwgaW1hZ2VUYWcpIHsKICAgICAgLy8g56e76Zmk6ZWc5YOP5ZCN56ew5Lit55qE54m55q6K5a2X56ym77yM55Sf5oiQ566A5rSB55qE5ZCN56ewCiAgICAgIGxldCBiYXNlTmFtZSA9IGltYWdlTmFtZS5yZXBsYWNlKC9bXmEtekEtWjAtOV0vZywgJy0nKS50b0xvd2VyQ2FzZSgpCiAgICAgIAogICAgICAvLyDlpoLmnpzmoIfnrb7kuI3mmK9sYXRlc3TvvIzliJnliqDlhaXmoIfnrb4KICAgICAgaWYgKGltYWdlVGFnICYmIGltYWdlVGFnICE9PSAnbGF0ZXN0JykgewogICAgICAgIGJhc2VOYW1lICs9ICctJyArIGltYWdlVGFnLnJlcGxhY2UoL1teYS16QS1aMC05XS9nLCAnLScpCiAgICAgIH0KICAgICAgCiAgICAgIC8vIOa3u+WKoOaXtumXtOaIs+S/neivgeWUr+S4gOaApwogICAgICBjb25zdCB0aW1lc3RhbXAgPSBEYXRlLm5vdygpLnRvU3RyaW5nKCkuc2xpY2UoLTYpCiAgICAgIHRoaXMuZGVwbG95Rm9ybS5jb250YWluZXJOYW1lID0gYCR7YmFzZU5hbWV9LSR7dGltZXN0YW1wfWAKICAgIH0sCiAgICAKICAgIC8vIOagueaNrumVnOWDj+exu+Wei+aZuuiDveaOqOiNkOerr+WPowogICAgc3VnZ2VzdFNlcnZpY2VQb3J0KGltYWdlTmFtZSkgewogICAgICBjb25zdCBwb3J0TWFwID0gewogICAgICAgICduZ2lueCc6IDgwLAogICAgICAgICdhcGFjaGUnOiA4MCwKICAgICAgICAnaHR0cGQnOiA4MCwKICAgICAgICAnbXlzcWwnOiAzMzA2LAogICAgICAgICdtYXJpYWRiJzogMzMwNiwKICAgICAgICAncG9zdGdyZXMnOiA1NDMyLAogICAgICAgICdwb3N0Z3Jlc3FsJzogNTQzMiwKICAgICAgICAncmVkaXMnOiA2Mzc5LAogICAgICAgICdtb25nb2RiJzogMjcwMTcsCiAgICAgICAgJ21vbmdvJzogMjcwMTcsCiAgICAgICAgJ3RvbWNhdCc6IDgwODAsCiAgICAgICAgJ25vZGUnOiAzMDAwLAogICAgICAgICdzcHJpbmcnOiA4MDgwLAogICAgICAgICdqYXZhJzogODA4MAogICAgICB9CiAgICAgIAogICAgICAvLyDmn6Xmib7ljLnphY3nmoTplZzlg4/nsbvlnosKICAgICAgZm9yIChjb25zdCBba2V5LCBwb3J0XSBvZiBPYmplY3QuZW50cmllcyhwb3J0TWFwKSkgewogICAgICAgIGlmIChpbWFnZU5hbWUudG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhrZXkpKSB7CiAgICAgICAgICB0aGlzLmRlcGxveUZvcm0uc2VydmljZVBvcnQgPSBwb3J0CiAgICAgICAgICBicmVhawogICAgICAgIH0KICAgICAgfQogICAgfSwKICAgIAogICAgLy8g5qC85byP5YyW6ZWc5YOP5aSn5bCPCiAgICBmb3JtYXRJbWFnZVNpemUoc2l6ZU1iKSB7CiAgICAgIGlmICghc2l6ZU1iIHx8IHNpemVNYiA9PT0gMCkgcmV0dXJuICctJwogICAgICAKICAgICAgLy8g56Gu5L+d5piv5pWw5a2X57G75Z6LCiAgICAgIGNvbnN0IHNpemUgPSB0eXBlb2Ygc2l6ZU1iID09PSAnc3RyaW5nJyA/IHBhcnNlSW50KHNpemVNYikgOiBzaXplTWIKICAgICAgaWYgKGlzTmFOKHNpemUpKSByZXR1cm4gJy0nCiAgICAgIAogICAgICAvLyDlpoLmnpzlt7Lnu4/mmK9NQuWNleS9je+8jOebtOaOpeaYvuekugogICAgICBpZiAoc2l6ZSA8IDEwMjQpIHsKICAgICAgICByZXR1cm4gYCR7c2l6ZX0gTUJgCiAgICAgIH0KICAgICAgCiAgICAgIC8vIOi9rOaNouS4ukdCCiAgICAgIGNvbnN0IHNpemVHYiA9IHNpemUgLyAxMDI0CiAgICAgIHJldHVybiBgJHtzaXplR2IudG9GaXhlZCgxKX0gR0JgCiAgICB9LAogICAgCiAgICAvLyDojrflj5bliIbluIPnrZbnlaXmmL7npLrlkI3np7AKICAgIGdldFN0cmF0ZWd5RGlzcGxheU5hbWUoc3RyYXRlZ3kpIHsKICAgICAgY29uc3Qgc3RyYXRlZ3lNYXAgPSB7CiAgICAgICAgJ1NQUkVBRF9BQ1JPU1NfTk9ERVMnOiAn6Leo6IqC54K55YiG5pWjJywKICAgICAgICAnV09SS0VSX05PREVTX09OTFknOiAn5LuFV29ya2Vy6IqC54K5JywKICAgICAgICAnTUFOQUdFUl9OT0RFU19PTkxZJzogJ+S7hU1hbmFnZXLoioLngrknLAogICAgICAgICdCQUxBTkNFRCc6ICflubPooaHliIbluIMnLAogICAgICAgICdTUFJFQURfQUNST1NTX1pPTkVTJzogJ+i3qOWPr+eUqOWMuuWIhuaVoycKICAgICAgfQogICAgICByZXR1cm4gc3RyYXRlZ3lNYXBbc3RyYXRlZ3ldIHx8IHN0cmF0ZWd5CiAgICB9LAogICAgCiAgICAvLyDojrflj5bmjqjojZDnmoTliIbluIPnrZbnlaUKICAgIGFzeW5jIGdldFJlY29tbWVuZGVkRGlzdHJpYnV0aW9uU3RyYXRlZ3kocmVwbGljYXMpIHsKICAgICAgdHJ5IHsKICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGdldFJlY29tbWVuZGVkU3RyYXRlZ3kocmVwbGljYXMpCiAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMDAwICYmIHJlc3BvbnNlLmRhdGEpIHsKICAgICAgICAgIHJldHVybiByZXNwb25zZS5kYXRhLnJlY29tbWVuZGVkU3RyYXRlZ3kKICAgICAgICB9CiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgY29uc29sZS53YXJuKCfojrflj5bmjqjojZDliIbluIPnrZbnlaXlpLHotKU6JywgZXJyb3IpCiAgICAgIH0KICAgICAgcmV0dXJuICdTUFJFQURfQUNST1NTX05PREVTJyAvLyDpu5jorqTlgLwKICAgIH0sCiAgICAKICAgIC8vID09PSBIU03orr7lpIfnm7jlhbPmlrnms5UgPT09CiAgICAKICAgIC8vIOWkhOeQhkhTTeW8gOWFs+WPmOWMlgogICAgaGFuZGxlSHNtVG9nZ2xlKGVuYWJsZWQpIHsKICAgICAgaWYgKGVuYWJsZWQpIHsKICAgICAgICAvLyDlkK/nlKhIU03ml7bliqDovb3orr7lpIfliJfooagKICAgICAgICB0aGlzLmxvYWRIc21EZXZpY2VMaXN0KCkKICAgICAgfSBlbHNlIHsKICAgICAgICAvLyDnpoHnlKhIU03ml7bmuIXnqbrpgInmi6kKICAgICAgICB0aGlzLmRlcGxveUZvcm0uaHNtRGV2aWNlSWQgPSBudWxsCiAgICAgICAgdGhpcy5zZWxlY3RlZEhzbURldmljZSA9IG51bGwKICAgICAgfQogICAgfSwKICAgIAogICAgLy8g5Yqg6L29SFNN6K6+5aSH5YiX6KGoCiAgICBhc3luYyBsb2FkSHNtRGV2aWNlTGlzdCgpIHsKICAgICAgdGhpcy5oc21EZXZpY2VMaXN0TG9hZGluZyA9IHRydWUKICAgICAgdHJ5IHsKICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGdldEF2YWlsYWJsZUhzbURldmljZXMoewogICAgICAgICAgc3RhdHVzOiAncnVubmluZycKICAgICAgICB9KQogICAgICAgIAogICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDAwMCkgewogICAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEgJiYgcmVzcG9uc2UuZGF0YS5saXN0KSB7CiAgICAgICAgICAgIHRoaXMuaHNtRGV2aWNlTGlzdCA9IHJlc3BvbnNlLmRhdGEubGlzdAogICAgICAgICAgfSBlbHNlIGlmIChBcnJheS5pc0FycmF5KHJlc3BvbnNlLmRhdGEpKSB7CiAgICAgICAgICAgIHRoaXMuaHNtRGV2aWNlTGlzdCA9IHJlc3BvbnNlLmRhdGEKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIHRoaXMuaHNtRGV2aWNlTGlzdCA9IFtdCiAgICAgICAgICB9CiAgICAgICAgICAKICAgICAgICAgIGlmICh0aGlzLmhzbURldmljZUxpc3QubGVuZ3RoID09PSAwKSB7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuaW5mbygn5b2T5YmN5rKh5pyJ5Y+v55So55qESFNN6K6+5aSHJykKICAgICAgICAgIH0KICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign6I635Y+WSFNN6K6+5aSH5YiX6KGo5aSx6LSl77yaJyArIChyZXNwb25zZS5tZXNzYWdlIHx8ICfmnKrnn6XplJnor68nKSkKICAgICAgICAgIHRoaXMuaHNtRGV2aWNlTGlzdCA9IFtdCiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+WKoOi9vUhTTeiuvuWkh+WIl+ihqOWksei0pTonLCBlcnJvcikKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfliqDovb1IU03orr7lpIfliJfooajlpLHotKUnKQogICAgICAgIHRoaXMuaHNtRGV2aWNlTGlzdCA9IFtdCiAgICAgIH0gZmluYWxseSB7CiAgICAgICAgdGhpcy5oc21EZXZpY2VMaXN0TG9hZGluZyA9IGZhbHNlCiAgICAgIH0KICAgIH0sCiAgICAKICAgIC8vIOWkhOeQhkhTTeiuvuWkh+mAieaLqeWPmOWMlgogICAgaGFuZGxlSHNtRGV2aWNlQ2hhbmdlKGRldmljZUlkKSB7CiAgICAgIGlmIChkZXZpY2VJZCkgewogICAgICAgIHRoaXMuc2VsZWN0ZWRIc21EZXZpY2UgPSB0aGlzLmhzbURldmljZUxpc3QuZmluZChkZXZpY2UgPT4gZGV2aWNlLmRldmljZUlkID09PSBkZXZpY2VJZCkKICAgICAgICBpZiAodGhpcy5zZWxlY3RlZEhzbURldmljZSkgewogICAgICAgICAgLy8g5Y+v5Lul5Zyo6L+Z6YeM5Yqg6L295pu06K+m57uG55qE6K6+5aSH5L+h5oGvCiAgICAgICAgICB0aGlzLmxvYWRIc21EZXZpY2VEZXRhaWwoZGV2aWNlSWQpCiAgICAgICAgfQogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuc2VsZWN0ZWRIc21EZXZpY2UgPSBudWxsCiAgICAgIH0KICAgIH0sCiAgICAKICAgIC8vIOWKoOi9vUhTTeiuvuWkh+ivpue7huS/oeaBrwogICAgYXN5bmMgbG9hZEhzbURldmljZURldGFpbChkZXZpY2VJZCkgewogICAgICB0cnkgewogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZ2V0SHNtRGV2aWNlRGV0YWlsKGRldmljZUlkKQogICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDAwMCAmJiByZXNwb25zZS5kYXRhKSB7CiAgICAgICAgICB0aGlzLnNlbGVjdGVkSHNtRGV2aWNlID0gcmVzcG9uc2UuZGF0YQogICAgICAgIH0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICBjb25zb2xlLndhcm4oJ+iOt+WPlkhTTeiuvuWkh+ivpue7huS/oeaBr+Wksei0pTonLCBlcnJvcikKICAgICAgfQogICAgfSwKICAgIAogICAgLy8g5bimSFNN6YWN572u55qE6YOo572y5pa55rOVCiAgICBhc3luYyBkZXBsb3lXaXRoSHNtKGRlcGxveURhdGEpIHsKICAgICAgcmV0dXJuIGRlcGxveUNvbnRhaW5lcldpdGhIc20oZGVwbG95RGF0YSkKICAgIH0KICB9LAogIG1vdW50ZWQoKSB7CiAgICB0aGlzLmdldExpc3QoKQogICAgLy8g6aKE5Yqg6L296ZWc5YOP5YiX6KGo77yM5o+Q5Y2H55So5oi35L2T6aqMCiAgICB0aGlzLmxvYWRJbWFnZUxpc3QoKQogICAgLy8g6aKE5Yqg6L29SFNN6K6+5aSH5YiX6KGoCiAgICB0aGlzLmxvYWRIc21EZXZpY2VMaXN0KCkKICB9LAogIGNvbXB1dGVkOiB7CiAgICAuLi5tYXBHZXR0ZXJzKFsKICAgICAgJ3VzZXInCiAgICBdKQogIH0sCiAgd2F0Y2g6IHsKICAgIC8vIOebkeWQrOWJr+acrOaVsOWPmOWMlu+8jOiHquWKqOabtOaWsOaOqOiNkOeahOWIhuW4g+etlueVpQogICAgJ2RlcGxveUZvcm0ucmVwbGljYXMnKG5ld1JlcGxpY2FzLCBvbGRSZXBsaWNhcykgewogICAgICBpZiAobmV3UmVwbGljYXMgPiAxICYmIG5ld1JlcGxpY2FzICE9PSBvbGRSZXBsaWNhcykgewogICAgICAgIHRoaXMuZ2V0UmVjb21tZW5kZWREaXN0cmlidXRpb25TdHJhdGVneShuZXdSZXBsaWNhcykudGhlbihzdHJhdGVneSA9PiB7CiAgICAgICAgICB0aGlzLmRlcGxveUZvcm0uZGlzdHJpYnV0aW9uU3RyYXRlZ3kgPSBzdHJhdGVneQogICAgICAgIH0pCiAgICAgIH0gZWxzZSBpZiAobmV3UmVwbGljYXMgPT09IDEpIHsKICAgICAgICAvLyDljZXlia/mnKzml7bkuI3pnIDopoHliIbluIPnrZbnlaUKICAgICAgICB0aGlzLmRlcGxveUZvcm0uZGlzdHJpYnV0aW9uU3RyYXRlZ3kgPSAnQkFMQU5DRUQnCiAgICAgIH0KICAgIH0sCiAgICAvLyDnm5HlkKzmiannvKnlrrnlia/mnKzmlbDlj5jljJYKICAgICdzY2FsZUZvcm0ucmVwbGljYXMnKG5ld1JlcGxpY2FzLCBvbGRSZXBsaWNhcykgewogICAgICBpZiAobmV3UmVwbGljYXMgPiAxICYmIG5ld1JlcGxpY2FzICE9PSBvbGRSZXBsaWNhcykgewogICAgICAgIHRoaXMuZ2V0UmVjb21tZW5kZWREaXN0cmlidXRpb25TdHJhdGVneShuZXdSZXBsaWNhcykudGhlbihzdHJhdGVneSA9PiB7CiAgICAgICAgICB0aGlzLnNjYWxlRm9ybS5kaXN0cmlidXRpb25TdHJhdGVneSA9IHN0cmF0ZWd5CiAgICAgICAgfSkKICAgICAgfSBlbHNlIGlmIChuZXdSZXBsaWNhcyA9PT0gMSkgewogICAgICAgIHRoaXMuc2NhbGVGb3JtLmRpc3RyaWJ1dGlvblN0cmF0ZWd5ID0gJ0JBTEFOQ0VEJwogICAgICB9CiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAo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file": "index.vue", "sourceRoot": "src/views/docker/container", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-card shadow=\"never\">\n      <div class=\"filter-container clearfix\">\n        <el-form ref=\"searchForm\" :model=\"listQuery\" inline @submit.native.prevent=\"getList\">\n          <div class=\"filter-inner\">\n            <el-form-item label=\"容器名称\">\n              <el-input v-model.trim=\"listQuery.containerName\" clearable placeholder=\"容器名称\" />\n            </el-form-item>\n            <el-form-item label=\"状态\">\n              <el-select v-model=\"listQuery.status\" clearable placeholder=\"请选择状态\">\n                <el-option label=\"运行中\" value=\"running\" />\n                <el-option label=\"已停止\" value=\"stopped\" />\n                <el-option label=\"已暂停\" value=\"paused\" />\n              </el-select>\n            </el-form-item>\n          </div>\n          <div class=\"pull-right\">\n            <el-button-group>\n              <el-form-item>\n                <el-button type=\"primary\" @click=\"handleSearch\"><i class=\"el-icon-search\" /> 查询</el-button>\n                <el-button type=\"info\" @click=\"handleResetSearch\"><i class=\"el-icon-refresh\" /> 重置</el-button>\n              </el-form-item>\n            </el-button-group>\n          </div>\n        </el-form>\n      </div>\n    </el-card>\n    \n    <el-card shadow=\"never\">\n      <div class=\"filter-container clearfix\">\n        <div class=\"filter-inner\">\n          <el-button type=\"primary\" @click=\"handleDeployContainer\">部署容器</el-button>\n          <el-button type=\"success\" @click=\"handleSyncStatus\">同步状态</el-button>\n        </div>\n      </div>\n      \n      <el-table\n        ref=\"dataTable\"\n        v-loading=\"loading\"\n        :data=\"tableList\"\n        style=\"width: 100%;\"\n      >\n        <el-table-column align=\"center\" label=\"序号\" type=\"index\" width=\"50\" />\n        <el-table-column align=\"center\" label=\"容器ID\" min-width=\"120\" prop=\"id\" />\n        <el-table-column align=\"center\" label=\"容器名称\" min-width=\"150\" prop=\"containerName\" />\n        <el-table-column align=\"center\" label=\"所属应用\" min-width=\"140\">\n          <template v-slot=\"{row}\">\n            <span>{{ row.applicationName || '-' }}</span>\n          </template>\n        </el-table-column>\n        <el-table-column align=\"center\" label=\"镜像\" min-width=\"150\">\n          <template v-slot=\"{row}\">\n            <span>{{ row.imageName }}:{{ row.imageTag || 'latest' }}</span>\n          </template>\n        </el-table-column>\n        <el-table-column align=\"center\" label=\"状态\" min-width=\"100\" prop=\"status\">\n          <template v-slot=\"{row}\">\n            <el-tag v-if=\"row.status === 'running'\" type=\"success\">运行中</el-tag>\n            <el-tag v-else-if=\"row.status === 'stopped'\" type=\"danger\">已停止</el-tag>\n            <el-tag v-else-if=\"row.status === 'paused'\" type=\"warning\">已暂停</el-tag>\n            <el-tag v-else>{{ row.status }}</el-tag>\n          </template>\n        </el-table-column>\n        <el-table-column align=\"center\" label=\"端口\" min-width=\"120\">\n          <template v-slot=\"{row}\">\n            <span>{{ formatPorts(row.portMappings) }}</span>\n          </template>\n        </el-table-column>\n        <el-table-column align=\"center\" label=\"副本信息\" min-width=\"120\">\n          <template v-slot=\"{row}\">\n            <div v-if=\"row.replicas\">\n              <div>副本数：{{ row.replicas || '-' }}</div>\n              <div v-if=\"row.distributionStrategy\" class=\"strategy-info\">\n                分布：{{ getStrategyDisplayName(row.distributionStrategy) }}\n              </div>\n            </div>\n            <span v-else>-</span>\n          </template>\n        </el-table-column>\n        <el-table-column align=\"center\" label=\"访问地址\" min-width=\"180\">\n          <template v-slot=\"{row}\">\n            <div v-if=\"row.accessUrl\">\n              <el-link :href=\"row.accessUrl\" target=\"_blank\" type=\"primary\">\n                {{ row.accessUrl }}\n              </el-link>\n              <el-button \n                type=\"text\" \n                size=\"mini\" \n                @click=\"copyToClipboard(row.accessUrl)\"\n                style=\"margin-left: 5px;\"\n              >\n                <i class=\"el-icon-copy-document\"></i>\n              </el-button>\n            </div>\n            <el-button \n              v-else-if=\"row.status === 'running'\" \n              type=\"text\" \n              size=\"mini\" \n              @click=\"handleConfigRoute(row)\"\n            >\n              配置访问\n            </el-button>\n            <span v-else>-</span>\n          </template>\n        </el-table-column>\n        <el-table-column align=\"center\" label=\"HSM设备\" min-width=\"120\">\n          <template v-slot=\"{row}\">\n            <div v-if=\"row.hsmDeviceId || row.hsmConfigured\">\n              <el-tag type=\"success\" size=\"mini\">已配置</el-tag>\n              <div v-if=\"row.hsmDeviceName\" class=\"hsm-info\">\n                {{ row.hsmDeviceName }}\n              </div>\n            </div>\n            <span v-else>-</span>\n          </template>\n        </el-table-column>\n        <el-table-column align=\"center\" label=\"节点\" min-width=\"100\" prop=\"nodeName\" />\n        <el-table-column align=\"center\" label=\"创建时间\" min-width=\"150\">\n          <template v-slot=\"{row}\">\n            <span>{{ formatDate(row.createTime) }}</span>\n          </template>\n        </el-table-column>\n        <el-table-column align=\"center\" fixed=\"right\" label=\"操作\" width=\"250\">\n          <template v-slot=\"{row}\">\n            <el-button v-if=\"row.status === 'running'\" type=\"text\" @click=\"handleStopContainer(row)\">停止</el-button>\n            <el-button v-else type=\"text\" @click=\"handleStartContainer(row)\">启动</el-button>\n            <el-button type=\"text\" @click=\"handleRestartContainer(row)\">重启</el-button>\n            <el-button type=\"text\" @click=\"handleScaleContainer(row)\">扩缩容</el-button>\n            <el-button type=\"text\" @click=\"handleViewLogs(row)\">日志</el-button>\n            <el-button v-if=\"row.accessUrl\" type=\"text\" @click=\"handleUpdateRoute(row)\">更新路由</el-button>\n            <el-button type=\"text\" @click=\"handleRemoveContainer(row)\">删除</el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n      \n      <pagination\n        v-show=\"total > listQuery.pageSize\"\n        :limit.sync=\"listQuery.pageSize\"\n        :page.sync=\"listQuery.page\"\n        :total=\"total\"\n        layout=\"prev, pager, next\"\n        @pagination=\"getList\"\n      />\n    </el-card>\n    \n    <!-- 部署容器对话框 -->\n    <el-dialog :visible.sync=\"deployDialogVisible\" title=\"部署容器\" width=\"600px\">\n      <el-form ref=\"deployForm\" :model=\"deployForm\" :rules=\"deployRules\" label-width=\"120px\">\n        <el-form-item label=\"关联应用\" prop=\"applicationId\">\n          <el-select v-model=\"deployForm.applicationId\" filterable placeholder=\"请选择应用\" style=\"width: 100%;\" :loading=\"appOptionsLoading\">\n            <el-option v-for=\"opt in appOptions\" :key=\"opt.value\" :label=\"opt.label\" :value=\"String(opt.value)\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"容器名称\" prop=\"containerName\">\n          <el-input v-model.trim=\"deployForm.containerName\" placeholder=\"例如: my-nginx\" />\n        </el-form-item>\n        <el-form-item label=\"镜像选择\" prop=\"imageId\">\n          <el-select \n            v-model=\"deployForm.imageId\" \n            filterable \n            placeholder=\"请选择镜像\"\n            @change=\"handleImageChange\"\n            style=\"width: 100%;\"\n            :loading=\"imageListLoading\"\n            :disabled=\"imageList.length === 0\"\n          >\n            <el-option\n              v-for=\"image in imageList\"\n              :key=\"image.id\"\n              :label=\"`${image.name}:${image.tag}`\"\n              :value=\"image.id\"\n            >\n              <span style=\"float: left\">{{ image.name }}:{{ image.tag }}</span>\n              <span style=\"float: right; color: #8492a6; font-size: 13px\">{{ formatImageSize(image.sizeMb) }}</span>\n            </el-option>\n            <div v-if=\"imageList.length === 0\" slot=\"empty\">\n              <span v-if=\"imageListLoading\">加载中...</span>\n              <span v-else>暂无可用镜像，请先构建或导入镜像</span>\n            </div>\n          </el-select>\n          <div class=\"form-help\">\n            选择已有的Docker镜像进行部署\n            <el-button \n              type=\"text\" \n              size=\"mini\" \n              @click=\"loadImageList\"\n              style=\"margin-left: 8px;\"\n            >\n              <i class=\"el-icon-refresh\"></i> 刷新\n            </el-button>\n          </div>\n        </el-form-item>\n        <el-form-item label=\"镜像信息\" v-if=\"selectedImage\">\n          <div class=\"image-info\">\n            <p><strong>名称：</strong>{{ selectedImage.name }}</p>\n            <p><strong>标签：</strong>{{ selectedImage.tag }}</p>\n            <p><strong>大小：</strong>{{ formatImageSize(selectedImage.sizeMb) }}</p>\n            <p v-if=\"selectedImage.description\"><strong>描述：</strong>{{ selectedImage.description }}</p>\n          </div>\n        </el-form-item>\n        <el-form-item label=\"服务端口\" prop=\"servicePort\">\n          <el-input-number v-model=\"deployForm.servicePort\" placeholder=\"容器内部端口，如: 80\" :min=\"1\" :max=\"65535\" />\n          <div class=\"form-help\">容器内应用监听的端口号</div>\n        </el-form-item>\n        <el-form-item label=\"副本数\" prop=\"replicas\">\n          <el-input-number v-model=\"deployForm.replicas\" :min=\"1\" />\n        </el-form-item>\n        <el-form-item label=\"分布策略\" v-if=\"deployForm.replicas > 1\">\n          <el-select v-model=\"deployForm.distributionStrategy\" placeholder=\"请选择分布策略\" style=\"width: 100%;\">\n            <el-option label=\"跨节点分散（推荐）\" value=\"SPREAD_ACROSS_NODES\" />\n            <el-option label=\"仅Worker节点\" value=\"WORKER_NODES_ONLY\" />\n            <el-option label=\"仅Manager节点\" value=\"MANAGER_NODES_ONLY\" />\n            <el-option label=\"平衡分布\" value=\"BALANCED\" />\n            <el-option label=\"跨可用区分散\" value=\"SPREAD_ACROSS_ZONES\" />\n          </el-select>\n          <div class=\"form-help\">多副本时的部署分布策略，推荐选择跨节点分散</div>\n        </el-form-item>\n        <el-form-item label=\"启用路由\">\n          <el-switch v-model=\"deployForm.enableTraefik\" active-text=\"启用\" inactive-text=\"禁用\" />\n          <div class=\"form-help\">启用后可通过内网地址访问服务</div>\n        </el-form-item>\n        <el-form-item label=\"HSM设备资源\">\n          <el-switch v-model=\"deployForm.enableHsm\" active-text=\"启用\" inactive-text=\"禁用\" @change=\"handleHsmToggle\" />\n          <div class=\"form-help\">启用后可配置HSM加密设备资源</div>\n        </el-form-item>\n        <el-form-item v-if=\"deployForm.enableHsm\" label=\"选择设备\" prop=\"hsmDeviceId\">\n          <el-select \n            v-model=\"deployForm.hsmDeviceId\" \n            filterable \n            placeholder=\"请选择HSM设备\"\n            @change=\"handleHsmDeviceChange\"\n            style=\"width: 100%;\"\n            :loading=\"hsmDeviceListLoading\"\n            :disabled=\"hsmDeviceList.length === 0\"\n          >\n            <el-option\n              v-for=\"device in hsmDeviceList\"\n              :key=\"device.deviceId\"\n              :label=\"device.deviceName\"\n              :value=\"device.deviceId\"\n            >\n              <span style=\"float: left\">{{ device.deviceName }}</span>\n              <span style=\"float: right; color: #8492a6; font-size: 13px\">{{ device.ipAddress }}:{{ device.servicePort }}</span>\n            </el-option>\n            <div v-if=\"hsmDeviceList.length === 0\" slot=\"empty\">\n              <span v-if=\"hsmDeviceListLoading\">加载中...</span>\n              <span v-else>暂无可用设备</span>\n            </div>\n          </el-select>\n          <div class=\"form-help\">\n            选择可用的HSM加密设备\n            <el-button \n              type=\"text\" \n              size=\"mini\" \n              @click=\"loadHsmDeviceList\"\n              style=\"margin-left: 8px;\"\n            >\n              <i class=\"el-icon-refresh\"></i> 刷新\n            </el-button>\n          </div>\n        </el-form-item>\n        <el-form-item v-if=\"deployForm.enableHsm && selectedHsmDevice\" label=\"设备信息\">\n          <div class=\"device-info\">\n            <p><strong>设备名称：</strong>{{ selectedHsmDevice.deviceName }}</p>\n            <p><strong>IP地址：</strong>{{ selectedHsmDevice.ipAddress }}</p>\n            <p><strong>服务端口：</strong>{{ selectedHsmDevice.servicePort }}</p>\n            <p><strong>管理端口：</strong>{{ selectedHsmDevice.managementPort }}</p>\n            <p><strong>状态：</strong>\n              <el-tag v-if=\"selectedHsmDevice.status === 'available'\" type=\"success\" size=\"mini\">可用</el-tag>\n              <el-tag v-else-if=\"selectedHsmDevice.status === 'running'\" type=\"success\" size=\"mini\">运行中</el-tag>\n              <el-tag v-else-if=\"selectedHsmDevice.status === 'active'\" type=\"success\" size=\"mini\">活跃</el-tag>\n              <el-tag v-else type=\"warning\" size=\"mini\">{{ selectedHsmDevice.status }}</el-tag>\n            </p>\n            <p v-if=\"selectedHsmDevice.description\"><strong>描述：</strong>{{ selectedHsmDevice.description }}</p>\n            <p v-if=\"selectedHsmDevice.deviceGroupName\"><strong>设备组：</strong>{{ selectedHsmDevice.deviceGroupName }}</p>\n          </div>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"deployDialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"confirmDeployContainer\">部署</el-button>\n      </div>\n    </el-dialog>\n    \n    <!-- 容器日志对话框 -->\n    <el-dialog :visible.sync=\"logsDialogVisible\" title=\"容器日志\" width=\"800px\">\n      <el-input\n        v-model=\"containerLogs\"\n        :rows=\"20\"\n        readonly\n        type=\"textarea\"\n      />\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"logsDialogVisible = false\">关闭</el-button>\n      </div>\n    </el-dialog>\n    \n    <!-- 扩缩容对话框 -->\n    <el-dialog :visible.sync=\"scaleDialogVisible\" title=\"容器扩缩容\" width=\"500px\">\n      <el-form ref=\"scaleForm\" :model=\"scaleForm\" :rules=\"scaleRules\" label-width=\"100px\">\n        <el-form-item label=\"副本数\" prop=\"replicas\">\n          <el-input-number v-model=\"scaleForm.replicas\" :min=\"1\" />\n          <div class=\"form-help\">当前运行的容器实例数量</div>\n        </el-form-item>\n        <el-form-item label=\"分布策略\" v-if=\"scaleForm.replicas > 1\">\n          <el-select v-model=\"scaleForm.distributionStrategy\" placeholder=\"请选择分布策略\" style=\"width: 100%;\">\n            <el-option label=\"跨节点分散（推荐）\" value=\"SPREAD_ACROSS_NODES\" />\n            <el-option label=\"仅Worker节点\" value=\"WORKER_NODES_ONLY\" />\n            <el-option label=\"仅Manager节点\" value=\"MANAGER_NODES_ONLY\" />\n            <el-option label=\"平衡分布\" value=\"BALANCED\" />\n          </el-select>\n          <div class=\"form-help\">多副本时的部署分布策略</div>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"scaleDialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"confirmScaleContainer\">确认</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { mapGetters } from 'vuex'\nimport Pagination from '@/components/Pagination'\nimport { \n  getContainerList, \n  deployContainer, \n  deployContainerWithTraefik,\n  deployContainerWithStrategy,\n  getRecommendedStrategy,\n  startContainer, \n  stopContainer, \n  restartContainer, \n  removeContainer, \n  getContainerLogs, \n  syncContainerStatus, \n  scaleContainer, \n  updateContainerImage,\n  configContainerRoute,\n  updateContainerRoute\n} from '@/api/docker/container'\nimport { getImageList } from '@/api/docker/image'\nimport { fetchApplicationOptions, fetchUserApps } from '@/api/application'\nimport { getAvailableHsmDevices, getHsmDeviceDetail, deployContainerWithHsm } from '@/api/docker/hsm'\n\nexport default {\n  name: 'DockerContainerIndex',\n  components: {\n    Pagination\n  },\n  data() {\n    return {\n      tableList: [],\n      loading: false,\n      total: 0,\n      listQuery: {\n        page: 1,\n        pageSize: 20,\n        containerName: undefined,\n        status: undefined\n      },\n      logsDialogVisible: false,\n      containerLogs: '',\n      currentContainer: null,\n      deployDialogVisible: false,\n      deployForm: {\n        containerName: '',\n        imageId: null,\n        imageName: '',\n        imageTag: 'latest',\n        servicePort: 80,\n        replicas: 1,\n        distributionStrategy: 'SPREAD_ACROSS_NODES',\n        enableTraefik: true,\n        enableHsm: false,\n        hsmDeviceId: null,\n        deployedBy: null,\n        applicationId: null,\n        applicationName: ''\n      },\n      deployRules: {\n        applicationId: [{ required: true, message: '请选择关联应用', trigger: 'change' }],\n        containerName: [{ required: true, message: '请输入容器名称', trigger: 'blur' }],\n        imageId: [{ required: true, message: '请选择镜像', trigger: 'change' }],\n        servicePort: [{ required: true, message: '请输入服务端口', trigger: 'blur' }]\n      },\n      scaleDialogVisible: false,\n      scaleForm: {\n        id: '',\n        replicas: 1,\n        distributionStrategy: 'SPREAD_ACROSS_NODES'\n      },\n      scaleRules: {\n        replicas: [{ required: true, message: '请输入副本数量', trigger: 'blur' }]\n      },\n      // 镜像相关数据\n      imageList: [],\n      imageListLoading: false,\n      selectedImage: null,\n      // HSM设备相关数据\n      hsmDeviceList: [],\n      hsmDeviceListLoading: false,\n      selectedHsmDevice: null,\n      // 应用下拉\n      appOptions: [],\n      appOptionsLoading: false\n    }\n  },\n  methods: {\n    handleSearch() {\n      this.getList()\n    },\n    handleResetSearch() {\n      this.listQuery = {\n        page: 1,\n        pageSize: 20,\n        containerName: undefined,\n        status: undefined\n      }\n      this.getList()\n    },\n    handleDeployContainer() {\n      // 首先加载镜像列表\n      Promise.all([this.loadImageList(), this.loadAppOptions()]).then(() => {\n        this.deployForm = {\n          containerName: '',\n          imageId: null,\n          imageName: '',\n          imageTag: 'latest',\n          servicePort: 80,\n          replicas: 1,\n          distributionStrategy: 'SPREAD_ACROSS_NODES',\n          enableTraefik: true,\n          enableHsm: false,\n          hsmDeviceId: null,\n          deployedBy: this.user ? this.user.id : null,\n          applicationId: null,\n          applicationName: ''\n        }\n        this.selectedImage = null\n        this.selectedHsmDevice = null\n        this.deployDialogVisible = true\n        this.$nextTick(() => {\n          this.$refs['deployForm'].clearValidate()\n        })\n      })\n    },\n    confirmDeployContainer() {\n      this.$refs['deployForm'].validate((valid) => {\n        if (valid) {\n          // 检查HSM设备配置\n          if (this.deployForm.enableHsm && !this.deployForm.hsmDeviceId) {\n            this.$message.error('请选择HSM设备')\n            return\n          }\n          \n          const deployData = { ...this.deployForm }\n          \n          // 确保镜像信息完整\n          if (this.selectedImage) {\n            deployData.imageName = this.selectedImage.imageName\n            deployData.imageTag = this.selectedImage.imageTag\n          }\n          \n          // 构建HSM设备配置\n          if (this.deployForm.enableHsm && this.selectedHsmDevice) {\n            deployData.hsmDeviceConfig = {\n              encryptorGroupId: 1, // 默认组ID\n              encryptorId: this.selectedHsmDevice.deviceId,\n              encryptorName: this.selectedHsmDevice.deviceName,\n              serverIpAddr: this.selectedHsmDevice.ipAddress,\n              serverPort: this.selectedHsmDevice.servicePort,\n              tcpConnNum: this.selectedHsmDevice.tcpConnNum || 5,\n              msgHeadLen: this.selectedHsmDevice.msgHeadLen || 4,\n              msgTailLen: this.selectedHsmDevice.msgTailLen || 0,\n              asciiOrEbcdic: this.selectedHsmDevice.encoding || 0,\n              dynamicLibPath: './libdeviceapi.so'\n            }\n          }\n          \n          // 根据配置选择部署接口\n          let deployFn\n          if (this.deployForm.enableHsm) {\n            // 使用带HSM配置的部署接口\n            console.log('使用带HSM配置的部署接口')\n            deployFn = this.deployWithHsm\n          } else if (deployData.replicas > 1 && deployData.distributionStrategy) {\n            // 使用带分布策略的部署接口\n            console.log('使用带分布策略的部署接口')\n            deployFn = deployContainerWithStrategy\n          } else if (deployData.enableTraefik) {\n            // 使用带Traefik的部署接口\n            console.log('使用带Traefik的部署接口')\n            deployFn = this.deployWithTraefik\n          } else {\n            // 使用基本部署接口\n            console.log('使用基本部署接口')\n            deployFn = deployContainer\n          }\n          \n          deployFn(deployData).then((response) => {\n            this.deployDialogVisible = false\n            if (response.code === 20000) {\n              if (response.data) {\n                // 处理不同的响应结构\n                let message = '容器部署成功！'\n                \n                if (response.data.accessUrl) {\n                  message += `访问地址：${response.data.accessUrl}`\n                } else if (response.data.container && response.data.container.accessUrl) {\n                  message += `访问地址：${response.data.container.accessUrl}`\n                }\n                \n                if (response.data.distributionStrategy) {\n                  message += `，分布策略：${this.getStrategyDisplayName(response.data.distributionStrategy)}`\n                }\n                \n                if (response.data.hsmConfigured) {\n                  message += `，HSM设备：${this.selectedHsmDevice.deviceName}`\n                }\n                \n                this.$message.success(message)\n              } else {\n                this.$message.success('容器部署任务已提交')\n              }\n              this.getList()\n            } else {\n              this.$message.error(response.message || '部署失败')\n            }\n          }).catch(() => {\n            // 错误处理已在拦截器中处理\n          })\n        }\n      })\n    },\n\n    // 加载应用选项\n    async loadAppOptions() {\n      this.appOptionsLoading = true\n      try {\n        let resp\n        // 租户角色使用 user/list-options，其它使用 fetch-options\n        if (this.user && Array.isArray(this.user.roles) && this.user.roles.includes('ROLE_TENANT')) {\n          resp = await fetchUserApps()\n        } else {\n          resp = await fetchApplicationOptions()\n        }\n        if (resp && resp.code === 20000) {\n          const data = Array.isArray(resp.data) ? resp.data : (resp.data && resp.data.list) ? resp.data.list : []\n          this.appOptions = data.map(item => {\n            if (item.label && (item.value !== undefined)) return item\n            return { label: item.name || item.applicationName || item.label, value: String(item.id || item.value) }\n          })\n        } else {\n          this.appOptions = []\n        }\n      } catch (e) {\n        this.appOptions = []\n      } finally {\n        this.appOptionsLoading = false\n      }\n    },\n    handleStartContainer(row) {\n      startContainer(row.id).then(() => {\n        this.$message.success('容器启动成功')\n        this.getList()\n      })\n    },\n    handleStopContainer(row) {\n      stopContainer(row.id).then(() => {\n        this.$message.success('容器停止成功')\n        this.getList()\n      })\n    },\n    handleRestartContainer(row) {\n      restartContainer(row.id).then(() => {\n        this.$message.success('容器重启成功')\n        this.getList()\n      })\n    },\n    handleRemoveContainer(row) {\n      this.$msgbox.confirm(`确认要删除容器 ${row.containerName} 吗？`, '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        removeContainer(row.id).then(() => {\n          this.$message.success('删除成功')\n          this.getList()\n        })\n      }).catch(() => {\n        // 取消删除\n      })\n    },\n    handleScaleContainer(row) {\n      this.scaleForm = {\n        id: row.id,\n        replicas: row.replicas || 1, // 使用当前副本数或默认值\n        distributionStrategy: row.distributionStrategy || 'SPREAD_ACROSS_NODES'\n      }\n      this.scaleDialogVisible = true\n      this.$nextTick(() => {\n        this.$refs['scaleForm'].clearValidate()\n      })\n    },\n    confirmScaleContainer() {\n      this.$refs['scaleForm'].validate((valid) => {\n        if (valid) {\n          scaleContainer(this.scaleForm.id, this.scaleForm.replicas).then(() => {\n            this.scaleDialogVisible = false\n            this.$message.success('容器扩缩容任务已提交')\n            this.getList()\n          })\n        }\n      })\n    },\n    handleViewLogs(row) {\n      this.currentContainer = row\n      getContainerLogs(row.id).then(response => {\n        if (response.code === 20000) {\n          this.containerLogs = response.data || ''\n          this.logsDialogVisible = true\n        }\n      })\n    },\n    handleSyncStatus() {\n      syncContainerStatus().then(() => {\n        this.$message.success('容器状态同步任务已提交')\n        this.getList()\n      })\n    },\n    getList() {\n      this.loading = true\n      getContainerList(this.listQuery).then(response => {\n        if (response.code === 20000) {\n          this.tableList = response.data.list\n          this.total = response.data.totalCount\n        }\n        this.loading = false\n      }).catch(() => {\n        this.loading = false\n      })\n    },\n    // 格式化端口映射显示\n    formatPorts(portMappings) {\n      if (!portMappings) return '-'\n      try {\n        const ports = JSON.parse(portMappings)\n        if (Array.isArray(ports) && ports.length > 0) {\n          return ports.map(p => `${p.hostPort || ''}:${p.containerPort}/${p.protocol || 'tcp'}`).join(', ')\n        }\n        return '-'\n      } catch (e) {\n        return portMappings\n      }\n    },\n    // 格式化日期显示\n    formatDate(dateTime) {\n      if (!dateTime) return '-'\n      return new Date(dateTime).toLocaleString('zh-CN')\n    },\n    \n    // 复制到剪贴板\n    copyToClipboard(text) {\n      if (navigator.clipboard) {\n        navigator.clipboard.writeText(text).then(() => {\n          this.$message.success('地址已复制到剪贴板')\n        })\n      } else {\n        // 降级方案\n        const textArea = document.createElement('textarea')\n        textArea.value = text\n        document.body.appendChild(textArea)\n        textArea.select()\n        document.execCommand('copy')\n        document.body.removeChild(textArea)\n        this.$message.success('地址已复制到剪贴板')\n      }\n    },\n    \n    // 配置访问路由\n    handleConfigRoute(row) {\n      this.$prompt('请输入服务端口（容器内部端口）', '配置访问路由', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        inputValue: '80',\n        inputValidator: (value) => {\n          const port = parseInt(value)\n          if (!port || port < 1 || port > 65535) {\n            return '请输入有效的端口号(1-65535)'\n          }\n          return true\n        }\n      }).then(({ value }) => {\n        const routeConfig = {\n          containerId: row.id,\n          servicePort: parseInt(value),\n          enableTraefik: true\n        }\n        \n        this.configContainerRoute(routeConfig).then((response) => {\n          if (response.code === 20000 && response.data && response.data.accessUrl) {\n            this.$message.success(`路由配置成功！访问地址：${response.data.accessUrl}`)\n            this.getList()\n          } else {\n            this.$message.error(response.message || '配置失败')\n          }\n        }).catch(() => {\n          // 错误处理已在拦截器中处理\n        })\n      }).catch(() => {\n        // 取消操作\n      })\n    },\n    \n    // 更新路由配置\n    handleUpdateRoute(row) {\n      this.$prompt('请输入新的服务端口', '更新路由', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        inputValidator: (value) => {\n          const port = parseInt(value)\n          if (!port || port < 1 || port > 65535) {\n            return '请输入有效的端口号(1-65535)'\n          }\n          return true\n        }\n      }).then(({ value }) => {\n        const routeConfig = {\n          containerId: row.id,\n          servicePort: parseInt(value)\n        }\n        \n        this.updateContainerRoute(routeConfig).then((response) => {\n          if (response.code === 20000 && response.data && response.data.accessUrl) {\n            this.$message.success(`路由更新成功！新地址：${response.data.accessUrl}`)\n            this.getList()\n          } else {\n            this.$message.error(response.message || '更新失败')\n          }\n        }).catch(() => {\n          // 错误处理已在拦截器中处理\n        })\n      }).catch(() => {\n        // 取消操作\n      })\n    },\n    \n    // 带Traefik的部署方法\n    async deployWithTraefik(deployData) {\n      return deployContainerWithTraefik(deployData)\n    },\n    \n    // 配置容器路由方法\n    async configContainerRoute(routeConfig) {\n      return configContainerRoute(routeConfig)\n    },\n    \n    // 更新容器路由方法\n    async updateContainerRoute(routeConfig) {\n      return updateContainerRoute(routeConfig)\n    },\n    \n    // 加载镜像列表\n    async loadImageList() {\n      this.imageListLoading = true\n      try {\n        const response = await getImageList()\n        if (response.code === 20000) {\n          // 处理分页数据结构\n          if (response.data && response.data.list) {\n            // 如果返回的是分页结构\n            this.imageList = response.data.list.map(image => {\n              return {\n                ...image,\n                // 确保 sizeMb 是数字类型\n                sizeMb: typeof image.sizeMb === 'string' ? parseInt(image.sizeMb) : image.sizeMb\n              }\n            })\n          } else if (Array.isArray(response.data)) {\n            // 如果返回的是直接数组\n            this.imageList = response.data.map(image => {\n              return {\n                ...image,\n                sizeMb: typeof image.sizeMb === 'string' ? parseInt(image.sizeMb) : image.sizeMb\n              }\n            })\n          } else {\n            this.imageList = []\n          }\n          \n          if (this.imageList.length === 0) {\n            this.$message.info('当前没有可用的镜像，请先构建或导入镜像')\n          }\n        } else {\n          this.$message.error('获取镜像列表失败：' + (response.message || '未知错误'))\n          this.imageList = []\n        }\n      } catch (error) {\n        console.error('加载镜像列表失败:', error)\n        this.$message.error('加载镜像列表失败')\n        this.imageList = []\n      } finally {\n        this.imageListLoading = false\n      }\n    },\n    \n    // 处理镜像选择变化\n    handleImageChange(imageId) {\n      if (imageId) {\n        this.selectedImage = this.imageList.find(img => img.id === imageId)\n        if (this.selectedImage) {\n          // 自动填充镜像名称和标签\n          this.deployForm.imageName = this.selectedImage.name\n          this.deployForm.imageTag = this.selectedImage.tag\n          \n          // 智能生成容器名称（如果当前为空）\n          if (!this.deployForm.containerName) {\n            this.suggestContainerName(this.selectedImage.name, this.selectedImage.tag)\n          }\n          \n          // 根据镜像类型智能推荐端口\n          this.suggestServicePort(this.selectedImage.name)\n        }\n      } else {\n        this.selectedImage = null\n        this.deployForm.imageName = ''\n        this.deployForm.imageTag = 'latest'\n      }\n    },\n    \n    // 智能生成容器名称\n    suggestContainerName(imageName, imageTag) {\n      // 移除镜像名称中的特殊字符，生成简洁的名称\n      let baseName = imageName.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase()\n      \n      // 如果标签不是latest，则加入标签\n      if (imageTag && imageTag !== 'latest') {\n        baseName += '-' + imageTag.replace(/[^a-zA-Z0-9]/g, '-')\n      }\n      \n      // 添加时间戳保证唯一性\n      const timestamp = Date.now().toString().slice(-6)\n      this.deployForm.containerName = `${baseName}-${timestamp}`\n    },\n    \n    // 根据镜像类型智能推荐端口\n    suggestServicePort(imageName) {\n      const portMap = {\n        'nginx': 80,\n        'apache': 80,\n        'httpd': 80,\n        'mysql': 3306,\n        'mariadb': 3306,\n        'postgres': 5432,\n        'postgresql': 5432,\n        'redis': 6379,\n        'mongodb': 27017,\n        'mongo': 27017,\n        'tomcat': 8080,\n        'node': 3000,\n        'spring': 8080,\n        'java': 8080\n      }\n      \n      // 查找匹配的镜像类型\n      for (const [key, port] of Object.entries(portMap)) {\n        if (imageName.toLowerCase().includes(key)) {\n          this.deployForm.servicePort = port\n          break\n        }\n      }\n    },\n    \n    // 格式化镜像大小\n    formatImageSize(sizeMb) {\n      if (!sizeMb || sizeMb === 0) return '-'\n      \n      // 确保是数字类型\n      const size = typeof sizeMb === 'string' ? parseInt(sizeMb) : sizeMb\n      if (isNaN(size)) return '-'\n      \n      // 如果已经是MB单位，直接显示\n      if (size < 1024) {\n        return `${size} MB`\n      }\n      \n      // 转换为GB\n      const sizeGb = size / 1024\n      return `${sizeGb.toFixed(1)} GB`\n    },\n    \n    // 获取分布策略显示名称\n    getStrategyDisplayName(strategy) {\n      const strategyMap = {\n        'SPREAD_ACROSS_NODES': '跨节点分散',\n        'WORKER_NODES_ONLY': '仅Worker节点',\n        'MANAGER_NODES_ONLY': '仅Manager节点',\n        'BALANCED': '平衡分布',\n        'SPREAD_ACROSS_ZONES': '跨可用区分散'\n      }\n      return strategyMap[strategy] || strategy\n    },\n    \n    // 获取推荐的分布策略\n    async getRecommendedDistributionStrategy(replicas) {\n      try {\n        const response = await getRecommendedStrategy(replicas)\n        if (response.code === 20000 && response.data) {\n          return response.data.recommendedStrategy\n        }\n      } catch (error) {\n        console.warn('获取推荐分布策略失败:', error)\n      }\n      return 'SPREAD_ACROSS_NODES' // 默认值\n    },\n    \n    // === HSM设备相关方法 ===\n    \n    // 处理HSM开关变化\n    handleHsmToggle(enabled) {\n      if (enabled) {\n        // 启用HSM时加载设备列表\n        this.loadHsmDeviceList()\n      } else {\n        // 禁用HSM时清空选择\n        this.deployForm.hsmDeviceId = null\n        this.selectedHsmDevice = null\n      }\n    },\n    \n    // 加载HSM设备列表\n    async loadHsmDeviceList() {\n      this.hsmDeviceListLoading = true\n      try {\n        const response = await getAvailableHsmDevices({\n          status: 'running'\n        })\n        \n        if (response.code === 20000) {\n          if (response.data && response.data.list) {\n            this.hsmDeviceList = response.data.list\n          } else if (Array.isArray(response.data)) {\n            this.hsmDeviceList = response.data\n          } else {\n            this.hsmDeviceList = []\n          }\n          \n          if (this.hsmDeviceList.length === 0) {\n            this.$message.info('当前没有可用的HSM设备')\n          }\n        } else {\n          this.$message.error('获取HSM设备列表失败：' + (response.message || '未知错误'))\n          this.hsmDeviceList = []\n        }\n      } catch (error) {\n        console.error('加载HSM设备列表失败:', error)\n        this.$message.error('加载HSM设备列表失败')\n        this.hsmDeviceList = []\n      } finally {\n        this.hsmDeviceListLoading = false\n      }\n    },\n    \n    // 处理HSM设备选择变化\n    handleHsmDeviceChange(deviceId) {\n      if (deviceId) {\n        this.selectedHsmDevice = this.hsmDeviceList.find(device => device.deviceId === deviceId)\n        if (this.selectedHsmDevice) {\n          // 可以在这里加载更详细的设备信息\n          this.loadHsmDeviceDetail(deviceId)\n        }\n      } else {\n        this.selectedHsmDevice = null\n      }\n    },\n    \n    // 加载HSM设备详细信息\n    async loadHsmDeviceDetail(deviceId) {\n      try {\n        const response = await getHsmDeviceDetail(deviceId)\n        if (response.code === 20000 && response.data) {\n          this.selectedHsmDevice = response.data\n        }\n      } catch (error) {\n        console.warn('获取HSM设备详细信息失败:', error)\n      }\n    },\n    \n    // 带HSM配置的部署方法\n    async deployWithHsm(deployData) {\n      return deployContainerWithHsm(deployData)\n    }\n  },\n  mounted() {\n    this.getList()\n    // 预加载镜像列表，提升用户体验\n    this.loadImageList()\n    // 预加载HSM设备列表\n    this.loadHsmDeviceList()\n  },\n  computed: {\n    ...mapGetters([\n      'user'\n    ])\n  },\n  watch: {\n    // 监听副本数变化，自动更新推荐的分布策略\n    'deployForm.replicas'(newReplicas, oldReplicas) {\n      if (newReplicas > 1 && newReplicas !== oldReplicas) {\n        this.getRecommendedDistributionStrategy(newReplicas).then(strategy => {\n          this.deployForm.distributionStrategy = strategy\n        })\n      } else if (newReplicas === 1) {\n        // 单副本时不需要分布策略\n        this.deployForm.distributionStrategy = 'BALANCED'\n      }\n    },\n    // 监听扩缩容副本数变化\n    'scaleForm.replicas'(newReplicas, oldReplicas) {\n      if (newReplicas > 1 && newReplicas !== oldReplicas) {\n        this.getRecommendedDistributionStrategy(newReplicas).then(strategy => {\n          this.scaleForm.distributionStrategy = strategy\n        })\n      } else if (newReplicas === 1) {\n        this.scaleForm.distributionStrategy = 'BALANCED'\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.filter-inner {\n  display: flex;\n  align-items: center;\n  flex-wrap: wrap;\n  \n  .el-form-item {\n    margin-bottom: 10px;\n    margin-right: 15px;\n  }\n}\n\n.pull-right {\n  float: right;\n}\n\n.clearfix::after {\n  content: \"\";\n  display: table;\n  clear: both;\n}\n\n.form-help {\n  font-size: 12px;\n  color: #909399;\n  margin-top: 4px;\n}\n\n.el-link {\n  font-size: 12px;\n  max-width: 150px;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  display: inline-block;\n}\n\n.image-info {\n  background-color: #f5f7fa;\n  padding: 12px;\n  border-radius: 4px;\n  border: 1px solid #e4e7ed;\n  \n  p {\n    margin: 4px 0;\n    font-size: 13px;\n    \n    strong {\n      color: #303133;\n      font-weight: 500;\n    }\n  }\n}\n\n.device-info {\n  background-color: #f0f9ff;\n  padding: 12px;\n  border-radius: 4px;\n  border: 1px solid #a7f3d0;\n  \n  p {\n    margin: 4px 0;\n    font-size: 13px;\n    \n    strong {\n      color: #303133;\n      font-weight: 500;\n    }\n  }\n}\n\n.strategy-info {\n  font-size: 11px;\n  color: #909399;\n  margin-top: 2px;\n}\n\n.hsm-info {\n  font-size: 11px;\n  color: #67c23a;\n  margin-top: 2px;\n}\n</style>"]}]}