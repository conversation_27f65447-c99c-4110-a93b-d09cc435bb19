{"remainingRequest": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\src\\views\\docker\\container\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\src\\views\\docker\\container\\index.vue", "mtime": 1756805140667}, {"path": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1729062151209}, {"path": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1729062151198}, {"path": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1729062151209}, {"path": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1729062152627}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IG1hcEdldHRlcnMgfSBmcm9tICd2dWV4JwppbXBvcnQgUGFnaW5hdGlvbiBmcm9tICdAL2NvbXBvbmVudHMvUGFnaW5hdGlvbicKaW1wb3J0IHsKICBnZXRDb250YWluZXJMaXN0LAogIGRlcGxveUNvbnRhaW5lciwKICBnZXRSZWNvbW1lbmRlZFN0cmF0ZWd5LAogIHN0YXJ0Q29udGFpbmVyLAogIHN0b3BDb250YWluZXIsCiAgcmVzdGFydENvbnRhaW5lciwKICByZW1vdmVDb250YWluZXIsCiAgZ2V0Q29udGFpbmVyTG9ncywKICBzeW5jQ29udGFpbmVyU3RhdHVzLAogIHNjYWxlQ29udGFpbmVyLAogIHVwZGF0ZUNvbnRhaW5lckltYWdlLAogIGNvbmZpZ0NvbnRhaW5lclJvdXRlLAogIHVwZGF0ZUNvbnRhaW5lclJvdXRlCn0gZnJvbSAnQC9hcGkvZG9ja2VyL2NvbnRhaW5lcicKaW1wb3J0IHsgZ2V0SW1hZ2VMaXN0IH0gZnJvbSAnQC9hcGkvZG9ja2VyL2ltYWdlJwppbXBvcnQgeyBmZXRjaEFwcGxpY2F0aW9uT3B0aW9ucywgZmV0Y2hVc2VyQXBwcyB9IGZyb20gJ0AvYXBpL2FwcGxpY2F0aW9uJwppbXBvcnQgeyBnZXRBdmFpbGFibGVIc21EZXZpY2VzLCBnZXRIc21EZXZpY2VEZXRhaWwgfSBmcm9tICdAL2FwaS9kb2NrZXIvaHNtJwoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdEb2NrZXJDb250YWluZXJJbmRleCcsCiAgY29tcG9uZW50czogewogICAgUGFnaW5hdGlvbgogIH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIHRhYmxlTGlzdDogW10sCiAgICAgIGxvYWRpbmc6IGZhbHNlLAogICAgICB0b3RhbDogMCwKICAgICAgbGlzdFF1ZXJ5OiB7CiAgICAgICAgcGFnZTogMSwKICAgICAgICBwYWdlU2l6ZTogMjAsCiAgICAgICAgY29udGFpbmVyTmFtZTogdW5kZWZpbmVkLAogICAgICAgIHN0YXR1czogdW5kZWZpbmVkCiAgICAgIH0sCiAgICAgIGxvZ3NEaWFsb2dWaXNpYmxlOiBmYWxzZSwKICAgICAgY29udGFpbmVyTG9nczogJycsCiAgICAgIGN1cnJlbnRDb250YWluZXI6IG51bGwsCiAgICAgIGRlcGxveURpYWxvZ1Zpc2libGU6IGZhbHNlLAogICAgICBkZXBsb3lGb3JtOiB7CiAgICAgICAgY29udGFpbmVyTmFtZTogJycsCiAgICAgICAgaW1hZ2VJZDogbnVsbCwKICAgICAgICBpbWFnZU5hbWU6ICcnLAogICAgICAgIGltYWdlVGFnOiAnbGF0ZXN0JywKICAgICAgICBzZXJ2aWNlUG9ydDogODAsCiAgICAgICAgcmVwbGljYXM6IDEsCiAgICAgICAgZGlzdHJpYnV0aW9uU3RyYXRlZ3k6ICdTUFJFQURfQUNST1NTX05PREVTJywKICAgICAgICBlbmFibGVUcmFlZmlrOiB0cnVlLAogICAgICAgIGVuYWJsZUhzbTogZmFsc2UsCiAgICAgICAgaHNtRGV2aWNlSWQ6IG51bGwsCiAgICAgICAgZGVwbG95ZWRCeTogbnVsbCwKICAgICAgICBhcHBsaWNhdGlvbklkOiBudWxsLAogICAgICAgIGFwcGxpY2F0aW9uTmFtZTogJycKICAgICAgfSwKICAgICAgZGVwbG95UnVsZXM6IHsKICAgICAgICBhcHBsaWNhdGlvbklkOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+mAieaLqeWFs+iBlOW6lOeUqCcsIHRyaWdnZXI6ICdjaGFuZ2UnIH1dLAogICAgICAgIGNvbnRhaW5lck5hbWU6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWl5a655Zmo5ZCN56ewJywgdHJpZ2dlcjogJ2JsdXInIH1dLAogICAgICAgIGltYWdlSWQ6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36YCJ5oup6ZWc5YOPJywgdHJpZ2dlcjogJ2NoYW5nZScgfV0sCiAgICAgICAgc2VydmljZVBvcnQ6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWl5pyN5Yqh56uv5Y+jJywgdHJpZ2dlcjogJ2JsdXInIH1dCiAgICAgIH0sCiAgICAgIHNjYWxlRGlhbG9nVmlzaWJsZTogZmFsc2UsCiAgICAgIHNjYWxlRm9ybTogewogICAgICAgIGlkOiAnJywKICAgICAgICByZXBsaWNhczogMSwKICAgICAgICBkaXN0cmlidXRpb25TdHJhdGVneTogJ1NQUkVBRF9BQ1JPU1NfTk9ERVMnCiAgICAgIH0sCiAgICAgIHNjYWxlUnVsZXM6IHsKICAgICAgICByZXBsaWNhczogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaXlia/mnKzmlbDph48nLCB0cmlnZ2VyOiAnYmx1cicgfV0KICAgICAgfSwKICAgICAgLy8g6ZWc5YOP55u45YWz5pWw5o2uCiAgICAgIGltYWdlTGlzdDogW10sCiAgICAgIGltYWdlTGlzdExvYWRpbmc6IGZhbHNlLAogICAgICBzZWxlY3RlZEltYWdlOiBudWxsLAogICAgICAvLyBIU03orr7lpIfnm7jlhbPmlbDmja4KICAgICAgaHNtRGV2aWNlTGlzdDogW10sCiAgICAgIGhzbURldmljZUxpc3RMb2FkaW5nOiBmYWxzZSwKICAgICAgc2VsZWN0ZWRIc21EZXZpY2U6IG51bGwsCiAgICAgIC8vIOW6lOeUqOS4i+aLiQogICAgICBhcHBPcHRpb25zOiBbXSwKICAgICAgYXBwT3B0aW9uc0xvYWRpbmc6IGZhbHNlCiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICBoYW5kbGVTZWFyY2goKSB7CiAgICAgIHRoaXMuZ2V0TGlzdCgpCiAgICB9LAogICAgaGFuZGxlUmVzZXRTZWFyY2goKSB7CiAgICAgIHRoaXMubGlzdFF1ZXJ5ID0gewogICAgICAgIHBhZ2U6IDEsCiAgICAgICAgcGFnZVNpemU6IDIwLAogICAgICAgIGNvbnRhaW5lck5hbWU6IHVuZGVmaW5lZCwKICAgICAgICBzdGF0dXM6IHVuZGVmaW5lZAogICAgICB9CiAgICAgIHRoaXMuZ2V0TGlzdCgpCiAgICB9LAogICAgaGFuZGxlRGVwbG95Q29udGFpbmVyKCkgewogICAgICAvLyDpppblhYjliqDovb3plZzlg4/liJfooagKICAgICAgUHJvbWlzZS5hbGwoW3RoaXMubG9hZEltYWdlTGlzdCgpLCB0aGlzLmxvYWRBcHBPcHRpb25zKCldKS50aGVuKCgpID0+IHsKICAgICAgICB0aGlzLmRlcGxveUZvcm0gPSB7CiAgICAgICAgICBjb250YWluZXJOYW1lOiAnJywKICAgICAgICAgIGltYWdlSWQ6IG51bGwsCiAgICAgICAgICBpbWFnZU5hbWU6ICcnLAogICAgICAgICAgaW1hZ2VUYWc6ICdsYXRlc3QnLAogICAgICAgICAgc2VydmljZVBvcnQ6IDgwLAogICAgICAgICAgcmVwbGljYXM6IDEsCiAgICAgICAgICBkaXN0cmlidXRpb25TdHJhdGVneTogJ1NQUkVBRF9BQ1JPU1NfTk9ERVMnLAogICAgICAgICAgZW5hYmxlVHJhZWZpazogdHJ1ZSwKICAgICAgICAgIGVuYWJsZUhzbTogZmFsc2UsCiAgICAgICAgICBoc21EZXZpY2VJZDogbnVsbCwKICAgICAgICAgIGRlcGxveWVkQnk6IHRoaXMudXNlciA/IHRoaXMudXNlci5pZCA6IG51bGwsCiAgICAgICAgICBhcHBsaWNhdGlvbklkOiBudWxsLAogICAgICAgICAgYXBwbGljYXRpb25OYW1lOiAnJwogICAgICAgIH0KICAgICAgICB0aGlzLnNlbGVjdGVkSW1hZ2UgPSBudWxsCiAgICAgICAgdGhpcy5zZWxlY3RlZEhzbURldmljZSA9IG51bGwKICAgICAgICB0aGlzLmRlcGxveURpYWxvZ1Zpc2libGUgPSB0cnVlCiAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICAgICAgdGhpcy4kcmVmc1snZGVwbG95Rm9ybSddLmNsZWFyVmFsaWRhdGUoKQogICAgICAgIH0pCiAgICAgIH0pCiAgICB9LAogICAgY29uZmlybURlcGxveUNvbnRhaW5lcigpIHsKICAgICAgdGhpcy4kcmVmc1snZGVwbG95Rm9ybSddLnZhbGlkYXRlKCh2YWxpZCkgPT4gewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgLy8g5qOA5p+lSFNN6K6+5aSH6YWN572uCiAgICAgICAgICBpZiAodGhpcy5kZXBsb3lGb3JtLmVuYWJsZUhzbSAmJiAhdGhpcy5kZXBsb3lGb3JtLmhzbURldmljZUlkKSB7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+ivt+mAieaLqUhTTeiuvuWkhycpCiAgICAgICAgICAgIHJldHVybgogICAgICAgICAgfQogICAgICAgICAgCiAgICAgICAgICBjb25zdCBkZXBsb3lEYXRhID0geyAuLi50aGlzLmRlcGxveUZvcm0gfQogICAgICAgICAgCiAgICAgICAgICAvLyDnoa7kv53plZzlg4/kv6Hmga/lrozmlbQKICAgICAgICAgIGlmICh0aGlzLnNlbGVjdGVkSW1hZ2UpIHsKICAgICAgICAgICAgZGVwbG95RGF0YS5pbWFnZU5hbWUgPSB0aGlzLnNlbGVjdGVkSW1hZ2UuaW1hZ2VOYW1lCiAgICAgICAgICAgIGRlcGxveURhdGEuaW1hZ2VUYWcgPSB0aGlzLnNlbGVjdGVkSW1hZ2UuaW1hZ2VUYWcKICAgICAgICAgIH0KICAgICAgICAgIAogICAgICAgICAgLy8g5p6E5bu6SFNN6K6+5aSH6YWN572uCiAgICAgICAgICBpZiAodGhpcy5kZXBsb3lGb3JtLmVuYWJsZUhzbSAmJiB0aGlzLnNlbGVjdGVkSHNtRGV2aWNlKSB7CiAgICAgICAgICAgIGRlcGxveURhdGEuaHNtRGV2aWNlQ29uZmlnID0gewogICAgICAgICAgICAgIGVuY3J5cHRvckdyb3VwSWQ6IDEsIC8vIOm7mOiupOe7hElECiAgICAgICAgICAgICAgZW5jcnlwdG9ySWQ6IHRoaXMuc2VsZWN0ZWRIc21EZXZpY2UuZGV2aWNlSWQsCiAgICAgICAgICAgICAgZW5jcnlwdG9yTmFtZTogdGhpcy5zZWxlY3RlZEhzbURldmljZS5kZXZpY2VOYW1lLAogICAgICAgICAgICAgIHNlcnZlcklwQWRkcjogdGhpcy5zZWxlY3RlZEhzbURldmljZS5pcEFkZHJlc3MsCiAgICAgICAgICAgICAgc2VydmVyUG9ydDogdGhpcy5zZWxlY3RlZEhzbURldmljZS5zZXJ2aWNlUG9ydCwKICAgICAgICAgICAgICB0Y3BDb25uTnVtOiB0aGlzLnNlbGVjdGVkSHNtRGV2aWNlLnRjcENvbm5OdW0gfHwgNSwKICAgICAgICAgICAgICBtc2dIZWFkTGVuOiB0aGlzLnNlbGVjdGVkSHNtRGV2aWNlLm1zZ0hlYWRMZW4gfHwgNCwKICAgICAgICAgICAgICBtc2dUYWlsTGVuOiB0aGlzLnNlbGVjdGVkSHNtRGV2aWNlLm1zZ1RhaWxMZW4gfHwgMCwKICAgICAgICAgICAgICBhc2NpaU9yRWJjZGljOiB0aGlzLnNlbGVjdGVkSHNtRGV2aWNlLmVuY29kaW5nIHx8IDAsCiAgICAgICAgICAgICAgZHluYW1pY0xpYlBhdGg6ICcuL2xpYmRldmljZWFwaS5zbycKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgICAgCiAgICAgICAgICAvLyDmnoTlu7pUcmFlZmlr6YWN572uCiAgICAgICAgICBpZiAodGhpcy5kZXBsb3lGb3JtLmVuYWJsZVRyYWVmaWspIHsKICAgICAgICAgICAgZGVwbG95RGF0YS50cmFlZmlrQ29uZmlnID0gewogICAgICAgICAgICAgIGVuYWJsZWQ6IHRydWUsCiAgICAgICAgICAgICAgZG9tYWluOiBgJHtkZXBsb3lEYXRhLmNvbnRhaW5lck5hbWUudG9Mb3dlckNhc2UoKS5yZXBsYWNlKC9bXmEtejAtOS1dL2csICctJyl9LmNjc3AubG9jYWxgLAogICAgICAgICAgICAgIHNlcnZpY2VQb3J0OiBkZXBsb3lEYXRhLnNlcnZpY2VQb3J0IHx8IDgwLAogICAgICAgICAgICAgIHBhdGhQcmVmaXg6ICcvJywKICAgICAgICAgICAgICBodHRwc0VuYWJsZWQ6IGZhbHNlCiAgICAgICAgICAgIH0KICAgICAgICAgIH0KCiAgICAgICAgICAvLyDorr7nva7liIbluIPnrZbnlaUKICAgICAgICAgIGlmIChkZXBsb3lEYXRhLnJlcGxpY2FzID4gMSAmJiBkZXBsb3lEYXRhLmRpc3RyaWJ1dGlvblN0cmF0ZWd5KSB7CiAgICAgICAgICAgIGRlcGxveURhdGEuZGlzdHJpYnV0aW9uU3RyYXRlZ3kgPSBkZXBsb3lEYXRhLmRpc3RyaWJ1dGlvblN0cmF0ZWd5CiAgICAgICAgICB9CgogICAgICAgICAgLy8g6K6+5aSH6LWE5rqQ6YWN572uCiAgICAgICAgICBpZiAodGhpcy5kZXBsb3lGb3JtLmVuYWJsZUhzbSAmJiB0aGlzLnNlbGVjdGVkSHNtRGV2aWNlICYmIGRlcGxveURhdGEuaHNtRGV2aWNlQ29uZmlnKSB7CiAgICAgICAgICAgIGRlcGxveURhdGEuZGV2aWNlUmVzb3VyY2VDb25maWcgPSB7CiAgICAgICAgICAgICAgZGV2aWNlUmVzb3VyY2VUeXBlOiAnVlNNJywKICAgICAgICAgICAgICBkZXZpY2VSZXNvdXJjZUlkczogW3RoaXMuc2VsZWN0ZWRIc21EZXZpY2UuZGV2aWNlSWRdLAogICAgICAgICAgICAgIGFsbG9jYXRpb25UeXBlOiAnZXhjbHVzaXZlJywKICAgICAgICAgICAgICBwcmlvcml0eTogMSwKICAgICAgICAgICAgICBjb25maWdEYXRhOiBKU09OLnN0cmluZ2lmeShkZXBsb3lEYXRhLmhzbURldmljZUNvbmZpZykKICAgICAgICAgICAgfQogICAgICAgICAgfQoKICAgICAgICAgIC8vIOS9v+eUqOe7n+S4gOeahOmDqOe9suaOpeWPowogICAgICAgICAgY29uc29sZS5sb2coJ+S9v+eUqOe7n+S4gOeahOmDqOe9suaOpeWPo++8jOmFjee9ru+8micsIHsKICAgICAgICAgICAgaHNtOiB0aGlzLmRlcGxveUZvcm0uZW5hYmxlSHNtLAogICAgICAgICAgICB0cmFlZmlrOiB0aGlzLmRlcGxveUZvcm0uZW5hYmxlVHJhZWZpaywKICAgICAgICAgICAgc3RyYXRlZ3k6IGRlcGxveURhdGEuZGlzdHJpYnV0aW9uU3RyYXRlZ3ksCiAgICAgICAgICAgIHJlcGxpY2FzOiBkZXBsb3lEYXRhLnJlcGxpY2FzCiAgICAgICAgICB9KQoKICAgICAgICAgIGRlcGxveUNvbnRhaW5lcihkZXBsb3lEYXRhKS50aGVuKChyZXNwb25zZSkgPT4gewogICAgICAgICAgICB0aGlzLmRlcGxveURpYWxvZ1Zpc2libGUgPSBmYWxzZQogICAgICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwMDApIHsKICAgICAgICAgICAgICBpZiAocmVzcG9uc2UuZGF0YSkgewogICAgICAgICAgICAgICAgLy8g5aSE55CG5LiN5ZCM55qE5ZON5bqU57uT5p6ECiAgICAgICAgICAgICAgICBsZXQgbWVzc2FnZSA9ICflrrnlmajpg6jnvbLmiJDlip/vvIEnCiAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgIGlmIChyZXNwb25zZS5kYXRhLmFjY2Vzc1VybCkgewogICAgICAgICAgICAgICAgICBtZXNzYWdlICs9IGDorr/pl67lnLDlnYDvvJoke3Jlc3BvbnNlLmRhdGEuYWNjZXNzVXJsfWAKICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAocmVzcG9uc2UuZGF0YS5jb250YWluZXIgJiYgcmVzcG9uc2UuZGF0YS5jb250YWluZXIuYWNjZXNzVXJsKSB7CiAgICAgICAgICAgICAgICAgIG1lc3NhZ2UgKz0gYOiuv+mXruWcsOWdgO+8miR7cmVzcG9uc2UuZGF0YS5jb250YWluZXIuYWNjZXNzVXJsfWAKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEuZGlzdHJpYnV0aW9uU3RyYXRlZ3kpIHsKICAgICAgICAgICAgICAgICAgbWVzc2FnZSArPSBg77yM5YiG5biD562W55Wl77yaJHt0aGlzLmdldFN0cmF0ZWd5RGlzcGxheU5hbWUocmVzcG9uc2UuZGF0YS5kaXN0cmlidXRpb25TdHJhdGVneSl9YAogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICBpZiAocmVzcG9uc2UuZGF0YS5oc21Db25maWd1cmVkKSB7CiAgICAgICAgICAgICAgICAgIG1lc3NhZ2UgKz0gYO+8jEhTTeiuvuWkh++8miR7dGhpcy5zZWxlY3RlZEhzbURldmljZS5kZXZpY2VOYW1lfWAKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKG1lc3NhZ2UpCiAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5a655Zmo6YOo572y5Lu75Yqh5bey5o+Q5LqkJykKICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCkKICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlc3BvbnNlLm1lc3NhZ2UgfHwgJ+mDqOe9suWksei0pScpCiAgICAgICAgICAgIH0KICAgICAgICAgIH0pLmNhdGNoKCgpID0+IHsKICAgICAgICAgICAgLy8g6ZSZ6K+v5aSE55CG5bey5Zyo5oum5oiq5Zmo5Lit5aSE55CGCiAgICAgICAgICB9KQogICAgICAgIH0KICAgICAgfSkKICAgIH0sCgogICAgLy8g5Yqg6L295bqU55So6YCJ6aG5CiAgICBhc3luYyBsb2FkQXBwT3B0aW9ucygpIHsKICAgICAgdGhpcy5hcHBPcHRpb25zTG9hZGluZyA9IHRydWUKICAgICAgdHJ5IHsKICAgICAgICBsZXQgcmVzcAogICAgICAgIC8vIOenn+aIt+inkuiJsuS9v+eUqCB1c2VyL2xpc3Qtb3B0aW9uc++8jOWFtuWug+S9v+eUqCBmZXRjaC1vcHRpb25zCiAgICAgICAgaWYgKHRoaXMudXNlciAmJiBBcnJheS5pc0FycmF5KHRoaXMudXNlci5yb2xlcykgJiYgdGhpcy51c2VyLnJvbGVzLmluY2x1ZGVzKCdST0xFX1RFTkFOVCcpKSB7CiAgICAgICAgICByZXNwID0gYXdhaXQgZmV0Y2hVc2VyQXBwcygpCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHJlc3AgPSBhd2FpdCBmZXRjaEFwcGxpY2F0aW9uT3B0aW9ucygpCiAgICAgICAgfQogICAgICAgIGlmIChyZXNwICYmIHJlc3AuY29kZSA9PT0gMjAwMDApIHsKICAgICAgICAgIGNvbnN0IGRhdGEgPSBBcnJheS5pc0FycmF5KHJlc3AuZGF0YSkgPyByZXNwLmRhdGEgOiAocmVzcC5kYXRhICYmIHJlc3AuZGF0YS5saXN0KSA/IHJlc3AuZGF0YS5saXN0IDogW10KICAgICAgICAgIHRoaXMuYXBwT3B0aW9ucyA9IGRhdGEubWFwKGl0ZW0gPT4gewogICAgICAgICAgICBpZiAoaXRlbS5sYWJlbCAmJiAoaXRlbS52YWx1ZSAhPT0gdW5kZWZpbmVkKSkgcmV0dXJuIGl0ZW0KICAgICAgICAgICAgcmV0dXJuIHsgbGFiZWw6IGl0ZW0ubmFtZSB8fCBpdGVtLmFwcGxpY2F0aW9uTmFtZSB8fCBpdGVtLmxhYmVsLCB2YWx1ZTogU3RyaW5nKGl0ZW0uaWQgfHwgaXRlbS52YWx1ZSkgfQogICAgICAgICAgfSkKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy5hcHBPcHRpb25zID0gW10KICAgICAgICB9CiAgICAgIH0gY2F0Y2ggKGUpIHsKICAgICAgICB0aGlzLmFwcE9wdGlvbnMgPSBbXQogICAgICB9IGZpbmFsbHkgewogICAgICAgIHRoaXMuYXBwT3B0aW9uc0xvYWRpbmcgPSBmYWxzZQogICAgICB9CiAgICB9LAogICAgaGFuZGxlU3RhcnRDb250YWluZXIocm93KSB7CiAgICAgIHN0YXJ0Q29udGFpbmVyKHJvdy5pZCkudGhlbigoKSA9PiB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCflrrnlmajlkK/liqjmiJDlip8nKQogICAgICAgIHRoaXMuZ2V0TGlzdCgpCiAgICAgIH0pCiAgICB9LAogICAgaGFuZGxlU3RvcENvbnRhaW5lcihyb3cpIHsKICAgICAgc3RvcENvbnRhaW5lcihyb3cuaWQpLnRoZW4oKCkgPT4gewogICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5a655Zmo5YGc5q2i5oiQ5YqfJykKICAgICAgICB0aGlzLmdldExpc3QoKQogICAgICB9KQogICAgfSwKICAgIGhhbmRsZVJlc3RhcnRDb250YWluZXIocm93KSB7CiAgICAgIHJlc3RhcnRDb250YWluZXIocm93LmlkKS50aGVuKCgpID0+IHsKICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+WuueWZqOmHjeWQr+aIkOWKnycpCiAgICAgICAgdGhpcy5nZXRMaXN0KCkKICAgICAgfSkKICAgIH0sCiAgICBoYW5kbGVSZW1vdmVDb250YWluZXIocm93KSB7CiAgICAgIHRoaXMuJG1zZ2JveC5jb25maXJtKGDnoa7orqTopoHliKDpmaTlrrnlmaggJHtyb3cuY29udGFpbmVyTmFtZX0g5ZCX77yfYCwgJ+aPkOekuicsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsCiAgICAgICAgdHlwZTogJ3dhcm5pbmcnCiAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgIHJlbW92ZUNvbnRhaW5lcihyb3cuaWQpLnRoZW4oKCkgPT4gewogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfliKDpmaTmiJDlip8nKQogICAgICAgICAgdGhpcy5nZXRMaXN0KCkKICAgICAgICB9KQogICAgICB9KS5jYXRjaCgoKSA9PiB7CiAgICAgICAgLy8g5Y+W5raI5Yig6ZmkCiAgICAgIH0pCiAgICB9LAogICAgaGFuZGxlU2NhbGVDb250YWluZXIocm93KSB7CiAgICAgIHRoaXMuc2NhbGVGb3JtID0gewogICAgICAgIGlkOiByb3cuaWQsCiAgICAgICAgcmVwbGljYXM6IHJvdy5yZXBsaWNhcyB8fCAxLCAvLyDkvb/nlKjlvZPliY3lia/mnKzmlbDmiJbpu5jorqTlgLwKICAgICAgICBkaXN0cmlidXRpb25TdHJhdGVneTogcm93LmRpc3RyaWJ1dGlvblN0cmF0ZWd5IHx8ICdTUFJFQURfQUNST1NTX05PREVTJwogICAgICB9CiAgICAgIHRoaXMuc2NhbGVEaWFsb2dWaXNpYmxlID0gdHJ1ZQogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgdGhpcy4kcmVmc1snc2NhbGVGb3JtJ10uY2xlYXJWYWxpZGF0ZSgpCiAgICAgIH0pCiAgICB9LAogICAgY29uZmlybVNjYWxlQ29udGFpbmVyKCkgewogICAgICB0aGlzLiRyZWZzWydzY2FsZUZvcm0nXS52YWxpZGF0ZSgodmFsaWQpID0+IHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIHNjYWxlQ29udGFpbmVyKHRoaXMuc2NhbGVGb3JtLmlkLCB0aGlzLnNjYWxlRm9ybS5yZXBsaWNhcykudGhlbigoKSA9PiB7CiAgICAgICAgICAgIHRoaXMuc2NhbGVEaWFsb2dWaXNpYmxlID0gZmFsc2UKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCflrrnlmajmiannvKnlrrnku7vliqHlt7Lmj5DkuqQnKQogICAgICAgICAgICB0aGlzLmdldExpc3QoKQogICAgICAgICAgfSkKICAgICAgICB9CiAgICAgIH0pCiAgICB9LAogICAgaGFuZGxlVmlld0xvZ3Mocm93KSB7CiAgICAgIHRoaXMuY3VycmVudENvbnRhaW5lciA9IHJvdwogICAgICBnZXRDb250YWluZXJMb2dzKHJvdy5pZCkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMDAwKSB7CiAgICAgICAgICB0aGlzLmNvbnRhaW5lckxvZ3MgPSByZXNwb25zZS5kYXRhIHx8ICcnCiAgICAgICAgICB0aGlzLmxvZ3NEaWFsb2dWaXNpYmxlID0gdHJ1ZQogICAgICAgIH0KICAgICAgfSkKICAgIH0sCiAgICBoYW5kbGVTeW5jU3RhdHVzKCkgewogICAgICBzeW5jQ29udGFpbmVyU3RhdHVzKCkudGhlbigoKSA9PiB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCflrrnlmajnirbmgIHlkIzmraXku7vliqHlt7Lmj5DkuqQnKQogICAgICAgIHRoaXMuZ2V0TGlzdCgpCiAgICAgIH0pCiAgICB9LAogICAgZ2V0TGlzdCgpIHsKICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZQogICAgICBnZXRDb250YWluZXJMaXN0KHRoaXMubGlzdFF1ZXJ5KS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwMDApIHsKICAgICAgICAgIHRoaXMudGFibGVMaXN0ID0gcmVzcG9uc2UuZGF0YS5saXN0CiAgICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UuZGF0YS50b3RhbENvdW50CiAgICAgICAgfQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlCiAgICAgIH0pLmNhdGNoKCgpID0+IHsKICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZQogICAgICB9KQogICAgfSwKICAgIC8vIOagvOW8j+WMluerr+WPo+aYoOWwhOaYvuekugogICAgZm9ybWF0UG9ydHMocG9ydE1hcHBpbmdzKSB7CiAgICAgIGlmICghcG9ydE1hcHBpbmdzKSByZXR1cm4gJy0nCiAgICAgIHRyeSB7CiAgICAgICAgY29uc3QgcG9ydHMgPSBKU09OLnBhcnNlKHBvcnRNYXBwaW5ncykKICAgICAgICBpZiAoQXJyYXkuaXNBcnJheShwb3J0cykgJiYgcG9ydHMubGVuZ3RoID4gMCkgewogICAgICAgICAgcmV0dXJuIHBvcnRzLm1hcChwID0+IGAke3AuaG9zdFBvcnQgfHwgJyd9OiR7cC5jb250YWluZXJQb3J0fS8ke3AucHJvdG9jb2wgfHwgJ3RjcCd9YCkuam9pbignLCAnKQogICAgICAgIH0KICAgICAgICByZXR1cm4gJy0nCiAgICAgIH0gY2F0Y2ggKGUpIHsKICAgICAgICByZXR1cm4gcG9ydE1hcHBpbmdzCiAgICAgIH0KICAgIH0sCiAgICAvLyDmoLzlvI/ljJbml6XmnJ/mmL7npLoKICAgIGZvcm1hdERhdGUoZGF0ZVRpbWUpIHsKICAgICAgaWYgKCFkYXRlVGltZSkgcmV0dXJuICctJwogICAgICByZXR1cm4gbmV3IERhdGUoZGF0ZVRpbWUpLnRvTG9jYWxlU3RyaW5nKCd6aC1DTicpCiAgICB9LAogICAgCiAgICAvLyDlpI3liLbliLDliarotLTmnb8KICAgIGNvcHlUb0NsaXBib2FyZCh0ZXh0KSB7CiAgICAgIGlmIChuYXZpZ2F0b3IuY2xpcGJvYXJkKSB7CiAgICAgICAgbmF2aWdhdG9yLmNsaXBib2FyZC53cml0ZVRleHQodGV4dCkudGhlbigoKSA9PiB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+WcsOWdgOW3suWkjeWItuWIsOWJqui0tOadvycpCiAgICAgICAgfSkKICAgICAgfSBlbHNlIHsKICAgICAgICAvLyDpmY3nuqfmlrnmoYgKICAgICAgICBjb25zdCB0ZXh0QXJlYSA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ3RleHRhcmVhJykKICAgICAgICB0ZXh0QXJlYS52YWx1ZSA9IHRleHQKICAgICAgICBkb2N1bWVudC5ib2R5LmFwcGVuZENoaWxkKHRleHRBcmVhKQogICAgICAgIHRleHRBcmVhLnNlbGVjdCgpCiAgICAgICAgZG9jdW1lbnQuZXhlY0NvbW1hbmQoJ2NvcHknKQogICAgICAgIGRvY3VtZW50LmJvZHkucmVtb3ZlQ2hpbGQodGV4dEFyZWEpCiAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCflnLDlnYDlt7LlpI3liLbliLDliarotLTmnb8nKQogICAgICB9CiAgICB9LAogICAgCiAgICAvLyDphY3nva7orr/pl67ot6/nlLEKICAgIGhhbmRsZUNvbmZpZ1JvdXRlKHJvdykgewogICAgICB0aGlzLiRwcm9tcHQoJ+ivt+i+k+WFpeacjeWKoeerr+WPo++8iOWuueWZqOWGhemDqOerr+WPo++8iScsICfphY3nva7orr/pl67ot6/nlLEnLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLAogICAgICAgIGlucHV0VmFsdWU6ICc4MCcsCiAgICAgICAgaW5wdXRWYWxpZGF0b3I6ICh2YWx1ZSkgPT4gewogICAgICAgICAgY29uc3QgcG9ydCA9IHBhcnNlSW50KHZhbHVlKQogICAgICAgICAgaWYgKCFwb3J0IHx8IHBvcnQgPCAxIHx8IHBvcnQgPiA2NTUzNSkgewogICAgICAgICAgICByZXR1cm4gJ+ivt+i+k+WFpeacieaViOeahOerr+WPo+WPtygxLTY1NTM1KScKICAgICAgICAgIH0KICAgICAgICAgIHJldHVybiB0cnVlCiAgICAgICAgfQogICAgICB9KS50aGVuKCh7IHZhbHVlIH0pID0+IHsKICAgICAgICBjb25zdCByb3V0ZUNvbmZpZyA9IHsKICAgICAgICAgIGNvbnRhaW5lcklkOiByb3cuaWQsCiAgICAgICAgICBzZXJ2aWNlUG9ydDogcGFyc2VJbnQodmFsdWUpLAogICAgICAgICAgZW5hYmxlVHJhZWZpazogdHJ1ZQogICAgICAgIH0KICAgICAgICAKICAgICAgICB0aGlzLmNvbmZpZ0NvbnRhaW5lclJvdXRlKHJvdXRlQ29uZmlnKS50aGVuKChyZXNwb25zZSkgPT4gewogICAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMDAwICYmIHJlc3BvbnNlLmRhdGEgJiYgcmVzcG9uc2UuZGF0YS5hY2Nlc3NVcmwpIHsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKGDot6/nlLHphY3nva7miJDlip/vvIHorr/pl67lnLDlnYDvvJoke3Jlc3BvbnNlLmRhdGEuYWNjZXNzVXJsfWApCiAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpCiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlc3BvbnNlLm1lc3NhZ2UgfHwgJ+mFjee9ruWksei0pScpCiAgICAgICAgICB9CiAgICAgICAgfSkuY2F0Y2goKCkgPT4gewogICAgICAgICAgLy8g6ZSZ6K+v5aSE55CG5bey5Zyo5oum5oiq5Zmo5Lit5aSE55CGCiAgICAgICAgfSkKICAgICAgfSkuY2F0Y2goKCkgPT4gewogICAgICAgIC8vIOWPlua2iOaTjeS9nAogICAgICB9KQogICAgfSwKICAgIAogICAgLy8g5pu05paw6Lev55Sx6YWN572uCiAgICBoYW5kbGVVcGRhdGVSb3V0ZShyb3cpIHsKICAgICAgdGhpcy4kcHJvbXB0KCfor7fovpPlhaXmlrDnmoTmnI3liqHnq6/lj6MnLCAn5pu05paw6Lev55SxJywgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywKICAgICAgICBpbnB1dFZhbGlkYXRvcjogKHZhbHVlKSA9PiB7CiAgICAgICAgICBjb25zdCBwb3J0ID0gcGFyc2VJbnQodmFsdWUpCiAgICAgICAgICBpZiAoIXBvcnQgfHwgcG9ydCA8IDEgfHwgcG9ydCA+IDY1NTM1KSB7CiAgICAgICAgICAgIHJldHVybiAn6K+36L6T5YWl5pyJ5pWI55qE56uv5Y+j5Y+3KDEtNjU1MzUpJwogICAgICAgICAgfQogICAgICAgICAgcmV0dXJuIHRydWUKICAgICAgICB9CiAgICAgIH0pLnRoZW4oKHsgdmFsdWUgfSkgPT4gewogICAgICAgIGNvbnN0IHJvdXRlQ29uZmlnID0gewogICAgICAgICAgY29udGFpbmVySWQ6IHJvdy5pZCwKICAgICAgICAgIHNlcnZpY2VQb3J0OiBwYXJzZUludCh2YWx1ZSkKICAgICAgICB9CiAgICAgICAgCiAgICAgICAgdGhpcy51cGRhdGVDb250YWluZXJSb3V0ZShyb3V0ZUNvbmZpZykudGhlbigocmVzcG9uc2UpID0+IHsKICAgICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDAwMCAmJiByZXNwb25zZS5kYXRhICYmIHJlc3BvbnNlLmRhdGEuYWNjZXNzVXJsKSB7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcyhg6Lev55Sx5pu05paw5oiQ5Yqf77yB5paw5Zyw5Z2A77yaJHtyZXNwb25zZS5kYXRhLmFjY2Vzc1VybH1gKQogICAgICAgICAgICB0aGlzLmdldExpc3QoKQogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXNwb25zZS5tZXNzYWdlIHx8ICfmm7TmlrDlpLHotKUnKQogICAgICAgICAgfQogICAgICAgIH0pLmNhdGNoKCgpID0+IHsKICAgICAgICAgIC8vIOmUmeivr+WkhOeQhuW3suWcqOaLpuaIquWZqOS4reWkhOeQhgogICAgICAgIH0pCiAgICAgIH0pLmNhdGNoKCgpID0+IHsKICAgICAgICAvLyDlj5bmtojmk43kvZwKICAgICAgfSkKICAgIH0sCiAgICAKCiAgICAKICAgIC8vIOmFjee9ruWuueWZqOi3r+eUseaWueazlQogICAgYXN5bmMgY29uZmlnQ29udGFpbmVyUm91dGUocm91dGVDb25maWcpIHsKICAgICAgcmV0dXJuIGNvbmZpZ0NvbnRhaW5lclJvdXRlKHJvdXRlQ29uZmlnKQogICAgfSwKICAgIAogICAgLy8g5pu05paw5a655Zmo6Lev55Sx5pa55rOVCiAgICBhc3luYyB1cGRhdGVDb250YWluZXJSb3V0ZShyb3V0ZUNvbmZpZykgewogICAgICByZXR1cm4gdXBkYXRlQ29udGFpbmVyUm91dGUocm91dGVDb25maWcpCiAgICB9LAogICAgCiAgICAvLyDliqDovb3plZzlg4/liJfooagKICAgIGFzeW5jIGxvYWRJbWFnZUxpc3QoKSB7CiAgICAgIHRoaXMuaW1hZ2VMaXN0TG9hZGluZyA9IHRydWUKICAgICAgdHJ5IHsKICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGdldEltYWdlTGlzdCgpCiAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMDAwKSB7CiAgICAgICAgICAvLyDlpITnkIbliIbpobXmlbDmja7nu5PmnoQKICAgICAgICAgIGlmIChyZXNwb25zZS5kYXRhICYmIHJlc3BvbnNlLmRhdGEubGlzdCkgewogICAgICAgICAgICAvLyDlpoLmnpzov5Tlm57nmoTmmK/liIbpobXnu5PmnoQKICAgICAgICAgICAgdGhpcy5pbWFnZUxpc3QgPSByZXNwb25zZS5kYXRhLmxpc3QubWFwKGltYWdlID0+IHsKICAgICAgICAgICAgICByZXR1cm4gewogICAgICAgICAgICAgICAgLi4uaW1hZ2UsCiAgICAgICAgICAgICAgICAvLyDnoa7kv50gc2l6ZU1iIOaYr+aVsOWtl+exu+WeiwogICAgICAgICAgICAgICAgc2l6ZU1iOiB0eXBlb2YgaW1hZ2Uuc2l6ZU1iID09PSAnc3RyaW5nJyA/IHBhcnNlSW50KGltYWdlLnNpemVNYikgOiBpbWFnZS5zaXplTWIKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0pCiAgICAgICAgICB9IGVsc2UgaWYgKEFycmF5LmlzQXJyYXkocmVzcG9uc2UuZGF0YSkpIHsKICAgICAgICAgICAgLy8g5aaC5p6c6L+U5Zue55qE5piv55u05o6l5pWw57uECiAgICAgICAgICAgIHRoaXMuaW1hZ2VMaXN0ID0gcmVzcG9uc2UuZGF0YS5tYXAoaW1hZ2UgPT4gewogICAgICAgICAgICAgIHJldHVybiB7CiAgICAgICAgICAgICAgICAuLi5pbWFnZSwKICAgICAgICAgICAgICAgIHNpemVNYjogdHlwZW9mIGltYWdlLnNpemVNYiA9PT0gJ3N0cmluZycgPyBwYXJzZUludChpbWFnZS5zaXplTWIpIDogaW1hZ2Uuc2l6ZU1iCiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9KQogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgdGhpcy5pbWFnZUxpc3QgPSBbXQogICAgICAgICAgfQogICAgICAgICAgCiAgICAgICAgICBpZiAodGhpcy5pbWFnZUxpc3QubGVuZ3RoID09PSAwKSB7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuaW5mbygn5b2T5YmN5rKh5pyJ5Y+v55So55qE6ZWc5YOP77yM6K+35YWI5p6E5bu65oiW5a+85YWl6ZWc5YOPJykKICAgICAgICAgIH0KICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign6I635Y+W6ZWc5YOP5YiX6KGo5aSx6LSl77yaJyArIChyZXNwb25zZS5tZXNzYWdlIHx8ICfmnKrnn6XplJnor68nKSkKICAgICAgICAgIHRoaXMuaW1hZ2VMaXN0ID0gW10KICAgICAgICB9CiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgY29uc29sZS5lcnJvcign5Yqg6L296ZWc5YOP5YiX6KGo5aSx6LSlOicsIGVycm9yKQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+WKoOi9vemVnOWDj+WIl+ihqOWksei0pScpCiAgICAgICAgdGhpcy5pbWFnZUxpc3QgPSBbXQogICAgICB9IGZpbmFsbHkgewogICAgICAgIHRoaXMuaW1hZ2VMaXN0TG9hZGluZyA9IGZhbHNlCiAgICAgIH0KICAgIH0sCiAgICAKICAgIC8vIOWkhOeQhumVnOWDj+mAieaLqeWPmOWMlgogICAgaGFuZGxlSW1hZ2VDaGFuZ2UoaW1hZ2VJZCkgewogICAgICBpZiAoaW1hZ2VJZCkgewogICAgICAgIHRoaXMuc2VsZWN0ZWRJbWFnZSA9IHRoaXMuaW1hZ2VMaXN0LmZpbmQoaW1nID0+IGltZy5pZCA9PT0gaW1hZ2VJZCkKICAgICAgICBpZiAodGhpcy5zZWxlY3RlZEltYWdlKSB7CiAgICAgICAgICAvLyDoh6rliqjloavlhYXplZzlg4/lkI3np7DlkozmoIfnrb4KICAgICAgICAgIHRoaXMuZGVwbG95Rm9ybS5pbWFnZU5hbWUgPSB0aGlzLnNlbGVjdGVkSW1hZ2UubmFtZQogICAgICAgICAgdGhpcy5kZXBsb3lGb3JtLmltYWdlVGFnID0gdGhpcy5zZWxlY3RlZEltYWdlLnRhZwogICAgICAgICAgCiAgICAgICAgICAvLyDmmbrog73nlJ/miJDlrrnlmajlkI3np7DvvIjlpoLmnpzlvZPliY3kuLrnqbrvvIkKICAgICAgICAgIGlmICghdGhpcy5kZXBsb3lGb3JtLmNvbnRhaW5lck5hbWUpIHsKICAgICAgICAgICAgdGhpcy5zdWdnZXN0Q29udGFpbmVyTmFtZSh0aGlzLnNlbGVjdGVkSW1hZ2UubmFtZSwgdGhpcy5zZWxlY3RlZEltYWdlLnRhZykKICAgICAgICAgIH0KICAgICAgICAgIAogICAgICAgICAgLy8g5qC55o2u6ZWc5YOP57G75Z6L5pm66IO95o6o6I2Q56uv5Y+jCiAgICAgICAgICB0aGlzLnN1Z2dlc3RTZXJ2aWNlUG9ydCh0aGlzLnNlbGVjdGVkSW1hZ2UubmFtZSkKICAgICAgICB9CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5zZWxlY3RlZEltYWdlID0gbnVsbAogICAgICAgIHRoaXMuZGVwbG95Rm9ybS5pbWFnZU5hbWUgPSAnJwogICAgICAgIHRoaXMuZGVwbG95Rm9ybS5pbWFnZVRhZyA9ICdsYXRlc3QnCiAgICAgIH0KICAgIH0sCiAgICAKICAgIC8vIOaZuuiDveeUn+aIkOWuueWZqOWQjeensAogICAgc3VnZ2VzdENvbnRhaW5lck5hbWUoaW1hZ2VOYW1lLCBpbWFnZVRhZykgewogICAgICAvLyDnp7vpmaTplZzlg4/lkI3np7DkuK3nmoTnibnmrorlrZfnrKbvvIznlJ/miJDnroDmtIHnmoTlkI3np7AKICAgICAgbGV0IGJhc2VOYW1lID0gaW1hZ2VOYW1lLnJlcGxhY2UoL1teYS16QS1aMC05XS9nLCAnLScpLnRvTG93ZXJDYXNlKCkKICAgICAgCiAgICAgIC8vIOWmguaenOagh+etvuS4jeaYr2xhdGVzdO+8jOWImeWKoOWFpeagh+etvgogICAgICBpZiAoaW1hZ2VUYWcgJiYgaW1hZ2VUYWcgIT09ICdsYXRlc3QnKSB7CiAgICAgICAgYmFzZU5hbWUgKz0gJy0nICsgaW1hZ2VUYWcucmVwbGFjZSgvW15hLXpBLVowLTldL2csICctJykKICAgICAgfQogICAgICAKICAgICAgLy8g5re75Yqg5pe26Ze05oiz5L+d6K+B5ZSv5LiA5oCnCiAgICAgIGNvbnN0IHRpbWVzdGFtcCA9IERhdGUubm93KCkudG9TdHJpbmcoKS5zbGljZSgtNikKICAgICAgdGhpcy5kZXBsb3lGb3JtLmNvbnRhaW5lck5hbWUgPSBgJHtiYXNlTmFtZX0tJHt0aW1lc3RhbXB9YAogICAgfSwKICAgIAogICAgLy8g5qC55o2u6ZWc5YOP57G75Z6L5pm66IO95o6o6I2Q56uv5Y+jCiAgICBzdWdnZXN0U2VydmljZVBvcnQoaW1hZ2VOYW1lKSB7CiAgICAgIGNvbnN0IHBvcnRNYXAgPSB7CiAgICAgICAgJ25naW54JzogODAsCiAgICAgICAgJ2FwYWNoZSc6IDgwLAogICAgICAgICdodHRwZCc6IDgwLAogICAgICAgICdteXNxbCc6IDMzMDYsCiAgICAgICAgJ21hcmlhZGInOiAzMzA2LAogICAgICAgICdwb3N0Z3Jlcyc6IDU0MzIsCiAgICAgICAgJ3Bvc3RncmVzcWwnOiA1NDMyLAogICAgICAgICdyZWRpcyc6IDYzNzksCiAgICAgICAgJ21vbmdvZGInOiAyNzAxNywKICAgICAgICAnbW9uZ28nOiAyNzAxNywKICAgICAgICAndG9tY2F0JzogODA4MCwKICAgICAgICAnbm9kZSc6IDMwMDAsCiAgICAgICAgJ3NwcmluZyc6IDgwODAsCiAgICAgICAgJ2phdmEnOiA4MDgwCiAgICAgIH0KICAgICAgCiAgICAgIC8vIOafpeaJvuWMuemFjeeahOmVnOWDj+exu+WeiwogICAgICBmb3IgKGNvbnN0IFtrZXksIHBvcnRdIG9mIE9iamVjdC5lbnRyaWVzKHBvcnRNYXApKSB7CiAgICAgICAgaWYgKGltYWdlTmFtZS50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKGtleSkpIHsKICAgICAgICAgIHRoaXMuZGVwbG95Rm9ybS5zZXJ2aWNlUG9ydCA9IHBvcnQKICAgICAgICAgIGJyZWFrCiAgICAgICAgfQogICAgICB9CiAgICB9LAogICAgCiAgICAvLyDmoLzlvI/ljJbplZzlg4/lpKflsI8KICAgIGZvcm1hdEltYWdlU2l6ZShzaXplTWIpIHsKICAgICAgaWYgKCFzaXplTWIgfHwgc2l6ZU1iID09PSAwKSByZXR1cm4gJy0nCiAgICAgIAogICAgICAvLyDnoa7kv53mmK/mlbDlrZfnsbvlnosKICAgICAgY29uc3Qgc2l6ZSA9IHR5cGVvZiBzaXplTWIgPT09ICdzdHJpbmcnID8gcGFyc2VJbnQoc2l6ZU1iKSA6IHNpemVNYgogICAgICBpZiAoaXNOYU4oc2l6ZSkpIHJldHVybiAnLScKICAgICAgCiAgICAgIC8vIOWmguaenOW3sue7j+aYr01C5Y2V5L2N77yM55u05o6l5pi+56S6CiAgICAgIGlmIChzaXplIDwgMTAyNCkgewogICAgICAgIHJldHVybiBgJHtzaXplfSBNQmAKICAgICAgfQogICAgICAKICAgICAgLy8g6L2s5o2i5Li6R0IKICAgICAgY29uc3Qgc2l6ZUdiID0gc2l6ZSAvIDEwMjQKICAgICAgcmV0dXJuIGAke3NpemVHYi50b0ZpeGVkKDEpfSBHQmAKICAgIH0sCiAgICAKICAgIC8vIOiOt+WPluWIhuW4g+etlueVpeaYvuekuuWQjeensAogICAgZ2V0U3RyYXRlZ3lEaXNwbGF5TmFtZShzdHJhdGVneSkgewogICAgICBjb25zdCBzdHJhdGVneU1hcCA9IHsKICAgICAgICAnU1BSRUFEX0FDUk9TU19OT0RFUyc6ICfot6joioLngrnliIbmlaMnLAogICAgICAgICdXT1JLRVJfTk9ERVNfT05MWSc6ICfku4VXb3JrZXLoioLngrknLAogICAgICAgICdNQU5BR0VSX05PREVTX09OTFknOiAn5LuFTWFuYWdlcuiKgueCuScsCiAgICAgICAgJ0JBTEFOQ0VEJzogJ+W5s+ihoeWIhuW4gycsCiAgICAgICAgJ1NQUkVBRF9BQ1JPU1NfWk9ORVMnOiAn6Leo5Y+v55So5Yy65YiG5pWjJwogICAgICB9CiAgICAgIHJldHVybiBzdHJhdGVneU1hcFtzdHJhdGVneV0gfHwgc3RyYXRlZ3kKICAgIH0sCiAgICAKICAgIC8vIOiOt+WPluaOqOiNkOeahOWIhuW4g+etlueVpQogICAgYXN5bmMgZ2V0UmVjb21tZW5kZWREaXN0cmlidXRpb25TdHJhdGVneShyZXBsaWNhcykgewogICAgICB0cnkgewogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZ2V0UmVjb21tZW5kZWRTdHJhdGVneShyZXBsaWNhcykKICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwMDAgJiYgcmVzcG9uc2UuZGF0YSkgewogICAgICAgICAgcmV0dXJuIHJlc3BvbnNlLmRhdGEucmVjb21tZW5kZWRTdHJhdGVneQogICAgICAgIH0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICBjb25zb2xlLndhcm4oJ+iOt+WPluaOqOiNkOWIhuW4g+etlueVpeWksei0pTonLCBlcnJvcikKICAgICAgfQogICAgICByZXR1cm4gJ1NQUkVBRF9BQ1JPU1NfTk9ERVMnIC8vIOm7mOiupOWAvAogICAgfSwKICAgIAogICAgLy8gPT09IEhTTeiuvuWkh+ebuOWFs+aWueazlSA9PT0KICAgIAogICAgLy8g5aSE55CGSFNN5byA5YWz5Y+Y5YyWCiAgICBoYW5kbGVIc21Ub2dnbGUoZW5hYmxlZCkgewogICAgICBpZiAoZW5hYmxlZCkgewogICAgICAgIC8vIOWQr+eUqEhTTeaXtuWKoOi9veiuvuWkh+WIl+ihqAogICAgICAgIHRoaXMubG9hZEhzbURldmljZUxpc3QoKQogICAgICB9IGVsc2UgewogICAgICAgIC8vIOemgeeUqEhTTeaXtua4heepuumAieaLqQogICAgICAgIHRoaXMuZGVwbG95Rm9ybS5oc21EZXZpY2VJZCA9IG51bGwKICAgICAgICB0aGlzLnNlbGVjdGVkSHNtRGV2aWNlID0gbnVsbAogICAgICB9CiAgICB9LAogICAgCiAgICAvLyDliqDovb1IU03orr7lpIfliJfooagKICAgIGFzeW5jIGxvYWRIc21EZXZpY2VMaXN0KCkgewogICAgICB0aGlzLmhzbURldmljZUxpc3RMb2FkaW5nID0gdHJ1ZQogICAgICB0cnkgewogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZ2V0QXZhaWxhYmxlSHNtRGV2aWNlcyh7CiAgICAgICAgICBzdGF0dXM6ICdydW5uaW5nJwogICAgICAgIH0pCiAgICAgICAgCiAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMDAwKSB7CiAgICAgICAgICBpZiAocmVzcG9uc2UuZGF0YSAmJiByZXNwb25zZS5kYXRhLmxpc3QpIHsKICAgICAgICAgICAgdGhpcy5oc21EZXZpY2VMaXN0ID0gcmVzcG9uc2UuZGF0YS5saXN0CiAgICAgICAgICB9IGVsc2UgaWYgKEFycmF5LmlzQXJyYXkocmVzcG9uc2UuZGF0YSkpIHsKICAgICAgICAgICAgdGhpcy5oc21EZXZpY2VMaXN0ID0gcmVzcG9uc2UuZGF0YQogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgdGhpcy5oc21EZXZpY2VMaXN0ID0gW10KICAgICAgICAgIH0KICAgICAgICAgIAogICAgICAgICAgaWYgKHRoaXMuaHNtRGV2aWNlTGlzdC5sZW5ndGggPT09IDApIHsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5pbmZvKCflvZPliY3msqHmnInlj6/nlKjnmoRIU03orr7lpIcnKQogICAgICAgICAgfQogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfojrflj5ZIU03orr7lpIfliJfooajlpLHotKXvvJonICsgKHJlc3BvbnNlLm1lc3NhZ2UgfHwgJ+acquefpemUmeivrycpKQogICAgICAgICAgdGhpcy5oc21EZXZpY2VMaXN0ID0gW10KICAgICAgICB9CiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgY29uc29sZS5lcnJvcign5Yqg6L29SFNN6K6+5aSH5YiX6KGo5aSx6LSlOicsIGVycm9yKQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+WKoOi9vUhTTeiuvuWkh+WIl+ihqOWksei0pScpCiAgICAgICAgdGhpcy5oc21EZXZpY2VMaXN0ID0gW10KICAgICAgfSBmaW5hbGx5IHsKICAgICAgICB0aGlzLmhzbURldmljZUxpc3RMb2FkaW5nID0gZmFsc2UKICAgICAgfQogICAgfSwKICAgIAogICAgLy8g5aSE55CGSFNN6K6+5aSH6YCJ5oup5Y+Y5YyWCiAgICBoYW5kbGVIc21EZXZpY2VDaGFuZ2UoZGV2aWNlSWQpIHsKICAgICAgaWYgKGRldmljZUlkKSB7CiAgICAgICAgdGhpcy5zZWxlY3RlZEhzbURldmljZSA9IHRoaXMuaHNtRGV2aWNlTGlzdC5maW5kKGRldmljZSA9PiBkZXZpY2UuZGV2aWNlSWQgPT09IGRldmljZUlkKQogICAgICAgIGlmICh0aGlzLnNlbGVjdGVkSHNtRGV2aWNlKSB7CiAgICAgICAgICAvLyDlj6/ku6XlnKjov5nph4zliqDovb3mm7Tor6bnu4bnmoTorr7lpIfkv6Hmga8KICAgICAgICAgIHRoaXMubG9hZEhzbURldmljZURldGFpbChkZXZpY2VJZCkKICAgICAgICB9CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5zZWxlY3RlZEhzbURldmljZSA9IG51bGwKICAgICAgfQogICAgfSwKICAgIAogICAgLy8g5Yqg6L29SFNN6K6+5aSH6K+m57uG5L+h5oGvCiAgICBhc3luYyBsb2FkSHNtRGV2aWNlRGV0YWlsKGRldmljZUlkKSB7CiAgICAgIHRyeSB7CiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBnZXRIc21EZXZpY2VEZXRhaWwoZGV2aWNlSWQpCiAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMDAwICYmIHJlc3BvbnNlLmRhdGEpIHsKICAgICAgICAgIHRoaXMuc2VsZWN0ZWRIc21EZXZpY2UgPSByZXNwb25zZS5kYXRhCiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIGNvbnNvbGUud2Fybign6I635Y+WSFNN6K6+5aSH6K+m57uG5L+h5oGv5aSx6LSlOicsIGVycm9yKQogICAgICB9CiAgICB9LAogICAgCgogIH0sCiAgbW91bnRlZCgpIHsKICAgIHRoaXMuZ2V0TGlzdCgpCiAgICAvLyDpooTliqDovb3plZzlg4/liJfooajvvIzmj5DljYfnlKjmiLfkvZPpqowKICAgIHRoaXMubG9hZEltYWdlTGlzdCgpCiAgICAvLyDpooTliqDovb1IU03orr7lpIfliJfooagKICAgIHRoaXMubG9hZEhzbURldmljZUxpc3QoKQogIH0sCiAgY29tcHV0ZWQ6IHsKICAgIC4uLm1hcEdldHRlcnMoWwogICAgICAndXNlcicKICAgIF0pCiAgfSwKICB3YXRjaDogewogICAgLy8g55uR5ZCs5Ymv5pys5pWw5Y+Y5YyW77yM6Ieq5Yqo5pu05paw5o6o6I2Q55qE5YiG5biD562W55WlCiAgICAnZGVwbG95Rm9ybS5yZXBsaWNhcycobmV3UmVwbGljYXMsIG9sZFJlcGxpY2FzKSB7CiAgICAgIGlmIChuZXdSZXBsaWNhcyA+IDEgJiYgbmV3UmVwbGljYXMgIT09IG9sZFJlcGxpY2FzKSB7CiAgICAgICAgdGhpcy5nZXRSZWNvbW1lbmRlZERpc3RyaWJ1dGlvblN0cmF0ZWd5KG5ld1JlcGxpY2FzKS50aGVuKHN0cmF0ZWd5ID0+IHsKICAgICAgICAgIHRoaXMuZGVwbG95Rm9ybS5kaXN0cmlidXRpb25TdHJhdGVneSA9IHN0cmF0ZWd5CiAgICAgICAgfSkKICAgICAgfSBlbHNlIGlmIChuZXdSZXBsaWNhcyA9PT0gMSkgewogICAgICAgIC8vIOWNleWJr+acrOaXtuS4jemcgOimgeWIhuW4g+etlueVpQogICAgICAgIHRoaXMuZGVwbG95Rm9ybS5kaXN0cmlidXRpb25TdHJhdGVneSA9ICdCQUxBTkNFRCcKICAgICAgfQogICAgfSwKICAgIC8vIOebkeWQrOaJqee8qeWuueWJr+acrOaVsOWPmOWMlgogICAgJ3NjYWxlRm9ybS5yZXBsaWNhcycobmV3UmVwbGljYXMsIG9sZFJlcGxpY2FzKSB7CiAgICAgIGlmIChuZXdSZXBsaWNhcyA+IDEgJiYgbmV3UmVwbGljYXMgIT09IG9sZFJlcGxpY2FzKSB7CiAgICAgICAgdGhpcy5nZXRSZWNvbW1lbmRlZERpc3RyaWJ1dGlvblN0cmF0ZWd5KG5ld1JlcGxpY2FzKS50aGVuKHN0cmF0ZWd5ID0+IHsKICAgICAgICAgIHRoaXMuc2NhbGVGb3JtLmRpc3RyaWJ1dGlvblN0cmF0ZWd5ID0gc3RyYXRlZ3kKICAgICAgICB9KQogICAgICB9IGVsc2UgaWYgKG5ld1JlcGxpY2FzID09PSAxKSB7CiAgICAgICAgdGhpcy5zY2FsZUZvcm0uZGlzdHJpYnV0aW9uU3RyYXRlZ3kgPSAnQkFMQU5DRUQnCiAgICAgIH0KICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAo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file": "index.vue", "sourceRoot": "src/views/docker/container", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-card shadow=\"never\">\n      <div class=\"filter-container clearfix\">\n        <el-form ref=\"searchForm\" :model=\"listQuery\" inline @submit.native.prevent=\"getList\">\n          <div class=\"filter-inner\">\n            <el-form-item label=\"容器名称\">\n              <el-input v-model.trim=\"listQuery.containerName\" clearable placeholder=\"容器名称\" />\n            </el-form-item>\n            <el-form-item label=\"状态\">\n              <el-select v-model=\"listQuery.status\" clearable placeholder=\"请选择状态\">\n                <el-option label=\"运行中\" value=\"running\" />\n                <el-option label=\"已停止\" value=\"stopped\" />\n                <el-option label=\"已暂停\" value=\"paused\" />\n              </el-select>\n            </el-form-item>\n          </div>\n          <div class=\"pull-right\">\n            <el-button-group>\n              <el-form-item>\n                <el-button type=\"primary\" @click=\"handleSearch\"><i class=\"el-icon-search\" /> 查询</el-button>\n                <el-button type=\"info\" @click=\"handleResetSearch\"><i class=\"el-icon-refresh\" /> 重置</el-button>\n              </el-form-item>\n            </el-button-group>\n          </div>\n        </el-form>\n      </div>\n    </el-card>\n    \n    <el-card shadow=\"never\">\n      <div class=\"filter-container clearfix\">\n        <div class=\"filter-inner\">\n          <el-button type=\"primary\" @click=\"handleDeployContainer\">部署容器</el-button>\n          <el-button type=\"success\" @click=\"handleSyncStatus\">同步状态</el-button>\n        </div>\n      </div>\n      \n      <el-table\n        ref=\"dataTable\"\n        v-loading=\"loading\"\n        :data=\"tableList\"\n        style=\"width: 100%;\"\n      >\n        <el-table-column align=\"center\" label=\"序号\" type=\"index\" width=\"50\" />\n        <el-table-column align=\"center\" label=\"容器ID\" min-width=\"120\" prop=\"id\" />\n        <el-table-column align=\"center\" label=\"容器名称\" min-width=\"150\" prop=\"containerName\" />\n        <el-table-column align=\"center\" label=\"所属应用\" min-width=\"140\">\n          <template v-slot=\"{row}\">\n            <span>{{ row.applicationName || '-' }}</span>\n          </template>\n        </el-table-column>\n        <el-table-column align=\"center\" label=\"镜像\" min-width=\"150\">\n          <template v-slot=\"{row}\">\n            <span>{{ row.imageName }}:{{ row.imageTag || 'latest' }}</span>\n          </template>\n        </el-table-column>\n        <el-table-column align=\"center\" label=\"状态\" min-width=\"100\" prop=\"status\">\n          <template v-slot=\"{row}\">\n            <el-tag v-if=\"row.status === 'running'\" type=\"success\">运行中</el-tag>\n            <el-tag v-else-if=\"row.status === 'stopped'\" type=\"danger\">已停止</el-tag>\n            <el-tag v-else-if=\"row.status === 'paused'\" type=\"warning\">已暂停</el-tag>\n            <el-tag v-else>{{ row.status }}</el-tag>\n          </template>\n        </el-table-column>\n        <el-table-column align=\"center\" label=\"端口\" min-width=\"120\">\n          <template v-slot=\"{row}\">\n            <span>{{ formatPorts(row.portMappings) }}</span>\n          </template>\n        </el-table-column>\n        <el-table-column align=\"center\" label=\"副本信息\" min-width=\"120\">\n          <template v-slot=\"{row}\">\n            <div v-if=\"row.replicas\">\n              <div>副本数：{{ row.replicas || '-' }}</div>\n              <div v-if=\"row.distributionStrategy\" class=\"strategy-info\">\n                分布：{{ getStrategyDisplayName(row.distributionStrategy) }}\n              </div>\n            </div>\n            <span v-else>-</span>\n          </template>\n        </el-table-column>\n        <el-table-column align=\"center\" label=\"访问地址\" min-width=\"180\">\n          <template v-slot=\"{row}\">\n            <div v-if=\"row.accessUrl\">\n              <el-link :href=\"row.accessUrl\" target=\"_blank\" type=\"primary\">\n                {{ row.accessUrl }}\n              </el-link>\n              <el-button \n                type=\"text\" \n                size=\"mini\" \n                @click=\"copyToClipboard(row.accessUrl)\"\n                style=\"margin-left: 5px;\"\n              >\n                <i class=\"el-icon-copy-document\"></i>\n              </el-button>\n            </div>\n            <el-button \n              v-else-if=\"row.status === 'running'\" \n              type=\"text\" \n              size=\"mini\" \n              @click=\"handleConfigRoute(row)\"\n            >\n              配置访问\n            </el-button>\n            <span v-else>-</span>\n          </template>\n        </el-table-column>\n        <el-table-column align=\"center\" label=\"HSM设备\" min-width=\"120\">\n          <template v-slot=\"{row}\">\n            <div v-if=\"row.hsmDeviceId || row.hsmConfigured\">\n              <el-tag type=\"success\" size=\"mini\">已配置</el-tag>\n              <div v-if=\"row.hsmDeviceName\" class=\"hsm-info\">\n                {{ row.hsmDeviceName }}\n              </div>\n            </div>\n            <span v-else>-</span>\n          </template>\n        </el-table-column>\n        <el-table-column align=\"center\" label=\"节点\" min-width=\"100\" prop=\"nodeName\" />\n        <el-table-column align=\"center\" label=\"创建时间\" min-width=\"150\">\n          <template v-slot=\"{row}\">\n            <span>{{ formatDate(row.createTime) }}</span>\n          </template>\n        </el-table-column>\n        <el-table-column align=\"center\" fixed=\"right\" label=\"操作\" width=\"250\">\n          <template v-slot=\"{row}\">\n            <el-button v-if=\"row.status === 'running'\" type=\"text\" @click=\"handleStopContainer(row)\">停止</el-button>\n            <el-button v-else type=\"text\" @click=\"handleStartContainer(row)\">启动</el-button>\n            <el-button type=\"text\" @click=\"handleRestartContainer(row)\">重启</el-button>\n            <el-button type=\"text\" @click=\"handleScaleContainer(row)\">扩缩容</el-button>\n            <el-button type=\"text\" @click=\"handleViewLogs(row)\">日志</el-button>\n            <el-button v-if=\"row.accessUrl\" type=\"text\" @click=\"handleUpdateRoute(row)\">更新路由</el-button>\n            <el-button type=\"text\" @click=\"handleRemoveContainer(row)\">删除</el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n      \n      <pagination\n        v-show=\"total > listQuery.pageSize\"\n        :limit.sync=\"listQuery.pageSize\"\n        :page.sync=\"listQuery.page\"\n        :total=\"total\"\n        layout=\"prev, pager, next\"\n        @pagination=\"getList\"\n      />\n    </el-card>\n    \n    <!-- 部署容器对话框 -->\n    <el-dialog :visible.sync=\"deployDialogVisible\" title=\"部署容器\" width=\"600px\">\n      <el-form ref=\"deployForm\" :model=\"deployForm\" :rules=\"deployRules\" label-width=\"120px\">\n        <el-form-item label=\"关联应用\" prop=\"applicationId\">\n          <el-select v-model=\"deployForm.applicationId\" filterable placeholder=\"请选择应用\" style=\"width: 100%;\" :loading=\"appOptionsLoading\">\n            <el-option v-for=\"opt in appOptions\" :key=\"opt.value\" :label=\"opt.label\" :value=\"String(opt.value)\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"容器名称\" prop=\"containerName\">\n          <el-input v-model.trim=\"deployForm.containerName\" placeholder=\"例如: my-nginx\" />\n        </el-form-item>\n        <el-form-item label=\"镜像选择\" prop=\"imageId\">\n          <el-select \n            v-model=\"deployForm.imageId\" \n            filterable \n            placeholder=\"请选择镜像\"\n            @change=\"handleImageChange\"\n            style=\"width: 100%;\"\n            :loading=\"imageListLoading\"\n            :disabled=\"imageList.length === 0\"\n          >\n            <el-option\n              v-for=\"image in imageList\"\n              :key=\"image.id\"\n              :label=\"`${image.name}:${image.tag}`\"\n              :value=\"image.id\"\n            >\n              <span style=\"float: left\">{{ image.name }}:{{ image.tag }}</span>\n              <span style=\"float: right; color: #8492a6; font-size: 13px\">{{ formatImageSize(image.sizeMb) }}</span>\n            </el-option>\n            <div v-if=\"imageList.length === 0\" slot=\"empty\">\n              <span v-if=\"imageListLoading\">加载中...</span>\n              <span v-else>暂无可用镜像，请先构建或导入镜像</span>\n            </div>\n          </el-select>\n          <div class=\"form-help\">\n            选择已有的Docker镜像进行部署\n            <el-button \n              type=\"text\" \n              size=\"mini\" \n              @click=\"loadImageList\"\n              style=\"margin-left: 8px;\"\n            >\n              <i class=\"el-icon-refresh\"></i> 刷新\n            </el-button>\n          </div>\n        </el-form-item>\n        <el-form-item label=\"镜像信息\" v-if=\"selectedImage\">\n          <div class=\"image-info\">\n            <p><strong>名称：</strong>{{ selectedImage.name }}</p>\n            <p><strong>标签：</strong>{{ selectedImage.tag }}</p>\n            <p><strong>大小：</strong>{{ formatImageSize(selectedImage.sizeMb) }}</p>\n            <p v-if=\"selectedImage.description\"><strong>描述：</strong>{{ selectedImage.description }}</p>\n          </div>\n        </el-form-item>\n        <el-form-item label=\"服务端口\" prop=\"servicePort\">\n          <el-input-number v-model=\"deployForm.servicePort\" placeholder=\"容器内部端口，如: 80\" :min=\"1\" :max=\"65535\" />\n          <div class=\"form-help\">容器内应用监听的端口号</div>\n        </el-form-item>\n        <el-form-item label=\"副本数\" prop=\"replicas\">\n          <el-input-number v-model=\"deployForm.replicas\" :min=\"1\" />\n        </el-form-item>\n        <el-form-item label=\"分布策略\" v-if=\"deployForm.replicas > 1\">\n          <el-select v-model=\"deployForm.distributionStrategy\" placeholder=\"请选择分布策略\" style=\"width: 100%;\">\n            <el-option label=\"跨节点分散（推荐）\" value=\"SPREAD_ACROSS_NODES\" />\n            <el-option label=\"仅Worker节点\" value=\"WORKER_NODES_ONLY\" />\n            <el-option label=\"仅Manager节点\" value=\"MANAGER_NODES_ONLY\" />\n            <el-option label=\"平衡分布\" value=\"BALANCED\" />\n            <el-option label=\"跨可用区分散\" value=\"SPREAD_ACROSS_ZONES\" />\n          </el-select>\n          <div class=\"form-help\">多副本时的部署分布策略，推荐选择跨节点分散</div>\n        </el-form-item>\n        <el-form-item label=\"启用路由\">\n          <el-switch v-model=\"deployForm.enableTraefik\" active-text=\"启用\" inactive-text=\"禁用\" />\n          <div class=\"form-help\">启用后可通过内网地址访问服务</div>\n        </el-form-item>\n        <el-form-item label=\"HSM设备资源\">\n          <el-switch v-model=\"deployForm.enableHsm\" active-text=\"启用\" inactive-text=\"禁用\" @change=\"handleHsmToggle\" />\n          <div class=\"form-help\">启用后可配置HSM加密设备资源</div>\n        </el-form-item>\n        <el-form-item v-if=\"deployForm.enableHsm\" label=\"选择设备\" prop=\"hsmDeviceId\">\n          <el-select \n            v-model=\"deployForm.hsmDeviceId\" \n            filterable \n            placeholder=\"请选择HSM设备\"\n            @change=\"handleHsmDeviceChange\"\n            style=\"width: 100%;\"\n            :loading=\"hsmDeviceListLoading\"\n            :disabled=\"hsmDeviceList.length === 0\"\n          >\n            <el-option\n              v-for=\"device in hsmDeviceList\"\n              :key=\"device.deviceId\"\n              :label=\"device.deviceName\"\n              :value=\"device.deviceId\"\n            >\n              <span style=\"float: left\">{{ device.deviceName }}</span>\n              <span style=\"float: right; color: #8492a6; font-size: 13px\">{{ device.ipAddress }}:{{ device.servicePort }}</span>\n            </el-option>\n            <div v-if=\"hsmDeviceList.length === 0\" slot=\"empty\">\n              <span v-if=\"hsmDeviceListLoading\">加载中...</span>\n              <span v-else>暂无可用设备</span>\n            </div>\n          </el-select>\n          <div class=\"form-help\">\n            选择可用的HSM加密设备\n            <el-button \n              type=\"text\" \n              size=\"mini\" \n              @click=\"loadHsmDeviceList\"\n              style=\"margin-left: 8px;\"\n            >\n              <i class=\"el-icon-refresh\"></i> 刷新\n            </el-button>\n          </div>\n        </el-form-item>\n        <el-form-item v-if=\"deployForm.enableHsm && selectedHsmDevice\" label=\"设备信息\">\n          <div class=\"device-info\">\n            <p><strong>设备名称：</strong>{{ selectedHsmDevice.deviceName }}</p>\n            <p><strong>IP地址：</strong>{{ selectedHsmDevice.ipAddress }}</p>\n            <p><strong>服务端口：</strong>{{ selectedHsmDevice.servicePort }}</p>\n            <p><strong>管理端口：</strong>{{ selectedHsmDevice.managementPort }}</p>\n            <p><strong>状态：</strong>\n              <el-tag v-if=\"selectedHsmDevice.status === 'available'\" type=\"success\" size=\"mini\">可用</el-tag>\n              <el-tag v-else-if=\"selectedHsmDevice.status === 'running'\" type=\"success\" size=\"mini\">运行中</el-tag>\n              <el-tag v-else-if=\"selectedHsmDevice.status === 'active'\" type=\"success\" size=\"mini\">活跃</el-tag>\n              <el-tag v-else type=\"warning\" size=\"mini\">{{ selectedHsmDevice.status }}</el-tag>\n            </p>\n            <p v-if=\"selectedHsmDevice.description\"><strong>描述：</strong>{{ selectedHsmDevice.description }}</p>\n            <p v-if=\"selectedHsmDevice.deviceGroupName\"><strong>设备组：</strong>{{ selectedHsmDevice.deviceGroupName }}</p>\n          </div>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"deployDialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"confirmDeployContainer\">部署</el-button>\n      </div>\n    </el-dialog>\n    \n    <!-- 容器日志对话框 -->\n    <el-dialog :visible.sync=\"logsDialogVisible\" title=\"容器日志\" width=\"800px\">\n      <el-input\n        v-model=\"containerLogs\"\n        :rows=\"20\"\n        readonly\n        type=\"textarea\"\n      />\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"logsDialogVisible = false\">关闭</el-button>\n      </div>\n    </el-dialog>\n    \n    <!-- 扩缩容对话框 -->\n    <el-dialog :visible.sync=\"scaleDialogVisible\" title=\"容器扩缩容\" width=\"500px\">\n      <el-form ref=\"scaleForm\" :model=\"scaleForm\" :rules=\"scaleRules\" label-width=\"100px\">\n        <el-form-item label=\"副本数\" prop=\"replicas\">\n          <el-input-number v-model=\"scaleForm.replicas\" :min=\"1\" />\n          <div class=\"form-help\">当前运行的容器实例数量</div>\n        </el-form-item>\n        <el-form-item label=\"分布策略\" v-if=\"scaleForm.replicas > 1\">\n          <el-select v-model=\"scaleForm.distributionStrategy\" placeholder=\"请选择分布策略\" style=\"width: 100%;\">\n            <el-option label=\"跨节点分散（推荐）\" value=\"SPREAD_ACROSS_NODES\" />\n            <el-option label=\"仅Worker节点\" value=\"WORKER_NODES_ONLY\" />\n            <el-option label=\"仅Manager节点\" value=\"MANAGER_NODES_ONLY\" />\n            <el-option label=\"平衡分布\" value=\"BALANCED\" />\n          </el-select>\n          <div class=\"form-help\">多副本时的部署分布策略</div>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"scaleDialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"confirmScaleContainer\">确认</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { mapGetters } from 'vuex'\nimport Pagination from '@/components/Pagination'\nimport {\n  getContainerList,\n  deployContainer,\n  getRecommendedStrategy,\n  startContainer,\n  stopContainer,\n  restartContainer,\n  removeContainer,\n  getContainerLogs,\n  syncContainerStatus,\n  scaleContainer,\n  updateContainerImage,\n  configContainerRoute,\n  updateContainerRoute\n} from '@/api/docker/container'\nimport { getImageList } from '@/api/docker/image'\nimport { fetchApplicationOptions, fetchUserApps } from '@/api/application'\nimport { getAvailableHsmDevices, getHsmDeviceDetail } from '@/api/docker/hsm'\n\nexport default {\n  name: 'DockerContainerIndex',\n  components: {\n    Pagination\n  },\n  data() {\n    return {\n      tableList: [],\n      loading: false,\n      total: 0,\n      listQuery: {\n        page: 1,\n        pageSize: 20,\n        containerName: undefined,\n        status: undefined\n      },\n      logsDialogVisible: false,\n      containerLogs: '',\n      currentContainer: null,\n      deployDialogVisible: false,\n      deployForm: {\n        containerName: '',\n        imageId: null,\n        imageName: '',\n        imageTag: 'latest',\n        servicePort: 80,\n        replicas: 1,\n        distributionStrategy: 'SPREAD_ACROSS_NODES',\n        enableTraefik: true,\n        enableHsm: false,\n        hsmDeviceId: null,\n        deployedBy: null,\n        applicationId: null,\n        applicationName: ''\n      },\n      deployRules: {\n        applicationId: [{ required: true, message: '请选择关联应用', trigger: 'change' }],\n        containerName: [{ required: true, message: '请输入容器名称', trigger: 'blur' }],\n        imageId: [{ required: true, message: '请选择镜像', trigger: 'change' }],\n        servicePort: [{ required: true, message: '请输入服务端口', trigger: 'blur' }]\n      },\n      scaleDialogVisible: false,\n      scaleForm: {\n        id: '',\n        replicas: 1,\n        distributionStrategy: 'SPREAD_ACROSS_NODES'\n      },\n      scaleRules: {\n        replicas: [{ required: true, message: '请输入副本数量', trigger: 'blur' }]\n      },\n      // 镜像相关数据\n      imageList: [],\n      imageListLoading: false,\n      selectedImage: null,\n      // HSM设备相关数据\n      hsmDeviceList: [],\n      hsmDeviceListLoading: false,\n      selectedHsmDevice: null,\n      // 应用下拉\n      appOptions: [],\n      appOptionsLoading: false\n    }\n  },\n  methods: {\n    handleSearch() {\n      this.getList()\n    },\n    handleResetSearch() {\n      this.listQuery = {\n        page: 1,\n        pageSize: 20,\n        containerName: undefined,\n        status: undefined\n      }\n      this.getList()\n    },\n    handleDeployContainer() {\n      // 首先加载镜像列表\n      Promise.all([this.loadImageList(), this.loadAppOptions()]).then(() => {\n        this.deployForm = {\n          containerName: '',\n          imageId: null,\n          imageName: '',\n          imageTag: 'latest',\n          servicePort: 80,\n          replicas: 1,\n          distributionStrategy: 'SPREAD_ACROSS_NODES',\n          enableTraefik: true,\n          enableHsm: false,\n          hsmDeviceId: null,\n          deployedBy: this.user ? this.user.id : null,\n          applicationId: null,\n          applicationName: ''\n        }\n        this.selectedImage = null\n        this.selectedHsmDevice = null\n        this.deployDialogVisible = true\n        this.$nextTick(() => {\n          this.$refs['deployForm'].clearValidate()\n        })\n      })\n    },\n    confirmDeployContainer() {\n      this.$refs['deployForm'].validate((valid) => {\n        if (valid) {\n          // 检查HSM设备配置\n          if (this.deployForm.enableHsm && !this.deployForm.hsmDeviceId) {\n            this.$message.error('请选择HSM设备')\n            return\n          }\n          \n          const deployData = { ...this.deployForm }\n          \n          // 确保镜像信息完整\n          if (this.selectedImage) {\n            deployData.imageName = this.selectedImage.imageName\n            deployData.imageTag = this.selectedImage.imageTag\n          }\n          \n          // 构建HSM设备配置\n          if (this.deployForm.enableHsm && this.selectedHsmDevice) {\n            deployData.hsmDeviceConfig = {\n              encryptorGroupId: 1, // 默认组ID\n              encryptorId: this.selectedHsmDevice.deviceId,\n              encryptorName: this.selectedHsmDevice.deviceName,\n              serverIpAddr: this.selectedHsmDevice.ipAddress,\n              serverPort: this.selectedHsmDevice.servicePort,\n              tcpConnNum: this.selectedHsmDevice.tcpConnNum || 5,\n              msgHeadLen: this.selectedHsmDevice.msgHeadLen || 4,\n              msgTailLen: this.selectedHsmDevice.msgTailLen || 0,\n              asciiOrEbcdic: this.selectedHsmDevice.encoding || 0,\n              dynamicLibPath: './libdeviceapi.so'\n            }\n          }\n          \n          // 构建Traefik配置\n          if (this.deployForm.enableTraefik) {\n            deployData.traefikConfig = {\n              enabled: true,\n              domain: `${deployData.containerName.toLowerCase().replace(/[^a-z0-9-]/g, '-')}.ccsp.local`,\n              servicePort: deployData.servicePort || 80,\n              pathPrefix: '/',\n              httpsEnabled: false\n            }\n          }\n\n          // 设置分布策略\n          if (deployData.replicas > 1 && deployData.distributionStrategy) {\n            deployData.distributionStrategy = deployData.distributionStrategy\n          }\n\n          // 设备资源配置\n          if (this.deployForm.enableHsm && this.selectedHsmDevice && deployData.hsmDeviceConfig) {\n            deployData.deviceResourceConfig = {\n              deviceResourceType: 'VSM',\n              deviceResourceIds: [this.selectedHsmDevice.deviceId],\n              allocationType: 'exclusive',\n              priority: 1,\n              configData: JSON.stringify(deployData.hsmDeviceConfig)\n            }\n          }\n\n          // 使用统一的部署接口\n          console.log('使用统一的部署接口，配置：', {\n            hsm: this.deployForm.enableHsm,\n            traefik: this.deployForm.enableTraefik,\n            strategy: deployData.distributionStrategy,\n            replicas: deployData.replicas\n          })\n\n          deployContainer(deployData).then((response) => {\n            this.deployDialogVisible = false\n            if (response.code === 20000) {\n              if (response.data) {\n                // 处理不同的响应结构\n                let message = '容器部署成功！'\n                \n                if (response.data.accessUrl) {\n                  message += `访问地址：${response.data.accessUrl}`\n                } else if (response.data.container && response.data.container.accessUrl) {\n                  message += `访问地址：${response.data.container.accessUrl}`\n                }\n                \n                if (response.data.distributionStrategy) {\n                  message += `，分布策略：${this.getStrategyDisplayName(response.data.distributionStrategy)}`\n                }\n                \n                if (response.data.hsmConfigured) {\n                  message += `，HSM设备：${this.selectedHsmDevice.deviceName}`\n                }\n                \n                this.$message.success(message)\n              } else {\n                this.$message.success('容器部署任务已提交')\n              }\n              this.getList()\n            } else {\n              this.$message.error(response.message || '部署失败')\n            }\n          }).catch(() => {\n            // 错误处理已在拦截器中处理\n          })\n        }\n      })\n    },\n\n    // 加载应用选项\n    async loadAppOptions() {\n      this.appOptionsLoading = true\n      try {\n        let resp\n        // 租户角色使用 user/list-options，其它使用 fetch-options\n        if (this.user && Array.isArray(this.user.roles) && this.user.roles.includes('ROLE_TENANT')) {\n          resp = await fetchUserApps()\n        } else {\n          resp = await fetchApplicationOptions()\n        }\n        if (resp && resp.code === 20000) {\n          const data = Array.isArray(resp.data) ? resp.data : (resp.data && resp.data.list) ? resp.data.list : []\n          this.appOptions = data.map(item => {\n            if (item.label && (item.value !== undefined)) return item\n            return { label: item.name || item.applicationName || item.label, value: String(item.id || item.value) }\n          })\n        } else {\n          this.appOptions = []\n        }\n      } catch (e) {\n        this.appOptions = []\n      } finally {\n        this.appOptionsLoading = false\n      }\n    },\n    handleStartContainer(row) {\n      startContainer(row.id).then(() => {\n        this.$message.success('容器启动成功')\n        this.getList()\n      })\n    },\n    handleStopContainer(row) {\n      stopContainer(row.id).then(() => {\n        this.$message.success('容器停止成功')\n        this.getList()\n      })\n    },\n    handleRestartContainer(row) {\n      restartContainer(row.id).then(() => {\n        this.$message.success('容器重启成功')\n        this.getList()\n      })\n    },\n    handleRemoveContainer(row) {\n      this.$msgbox.confirm(`确认要删除容器 ${row.containerName} 吗？`, '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        removeContainer(row.id).then(() => {\n          this.$message.success('删除成功')\n          this.getList()\n        })\n      }).catch(() => {\n        // 取消删除\n      })\n    },\n    handleScaleContainer(row) {\n      this.scaleForm = {\n        id: row.id,\n        replicas: row.replicas || 1, // 使用当前副本数或默认值\n        distributionStrategy: row.distributionStrategy || 'SPREAD_ACROSS_NODES'\n      }\n      this.scaleDialogVisible = true\n      this.$nextTick(() => {\n        this.$refs['scaleForm'].clearValidate()\n      })\n    },\n    confirmScaleContainer() {\n      this.$refs['scaleForm'].validate((valid) => {\n        if (valid) {\n          scaleContainer(this.scaleForm.id, this.scaleForm.replicas).then(() => {\n            this.scaleDialogVisible = false\n            this.$message.success('容器扩缩容任务已提交')\n            this.getList()\n          })\n        }\n      })\n    },\n    handleViewLogs(row) {\n      this.currentContainer = row\n      getContainerLogs(row.id).then(response => {\n        if (response.code === 20000) {\n          this.containerLogs = response.data || ''\n          this.logsDialogVisible = true\n        }\n      })\n    },\n    handleSyncStatus() {\n      syncContainerStatus().then(() => {\n        this.$message.success('容器状态同步任务已提交')\n        this.getList()\n      })\n    },\n    getList() {\n      this.loading = true\n      getContainerList(this.listQuery).then(response => {\n        if (response.code === 20000) {\n          this.tableList = response.data.list\n          this.total = response.data.totalCount\n        }\n        this.loading = false\n      }).catch(() => {\n        this.loading = false\n      })\n    },\n    // 格式化端口映射显示\n    formatPorts(portMappings) {\n      if (!portMappings) return '-'\n      try {\n        const ports = JSON.parse(portMappings)\n        if (Array.isArray(ports) && ports.length > 0) {\n          return ports.map(p => `${p.hostPort || ''}:${p.containerPort}/${p.protocol || 'tcp'}`).join(', ')\n        }\n        return '-'\n      } catch (e) {\n        return portMappings\n      }\n    },\n    // 格式化日期显示\n    formatDate(dateTime) {\n      if (!dateTime) return '-'\n      return new Date(dateTime).toLocaleString('zh-CN')\n    },\n    \n    // 复制到剪贴板\n    copyToClipboard(text) {\n      if (navigator.clipboard) {\n        navigator.clipboard.writeText(text).then(() => {\n          this.$message.success('地址已复制到剪贴板')\n        })\n      } else {\n        // 降级方案\n        const textArea = document.createElement('textarea')\n        textArea.value = text\n        document.body.appendChild(textArea)\n        textArea.select()\n        document.execCommand('copy')\n        document.body.removeChild(textArea)\n        this.$message.success('地址已复制到剪贴板')\n      }\n    },\n    \n    // 配置访问路由\n    handleConfigRoute(row) {\n      this.$prompt('请输入服务端口（容器内部端口）', '配置访问路由', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        inputValue: '80',\n        inputValidator: (value) => {\n          const port = parseInt(value)\n          if (!port || port < 1 || port > 65535) {\n            return '请输入有效的端口号(1-65535)'\n          }\n          return true\n        }\n      }).then(({ value }) => {\n        const routeConfig = {\n          containerId: row.id,\n          servicePort: parseInt(value),\n          enableTraefik: true\n        }\n        \n        this.configContainerRoute(routeConfig).then((response) => {\n          if (response.code === 20000 && response.data && response.data.accessUrl) {\n            this.$message.success(`路由配置成功！访问地址：${response.data.accessUrl}`)\n            this.getList()\n          } else {\n            this.$message.error(response.message || '配置失败')\n          }\n        }).catch(() => {\n          // 错误处理已在拦截器中处理\n        })\n      }).catch(() => {\n        // 取消操作\n      })\n    },\n    \n    // 更新路由配置\n    handleUpdateRoute(row) {\n      this.$prompt('请输入新的服务端口', '更新路由', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        inputValidator: (value) => {\n          const port = parseInt(value)\n          if (!port || port < 1 || port > 65535) {\n            return '请输入有效的端口号(1-65535)'\n          }\n          return true\n        }\n      }).then(({ value }) => {\n        const routeConfig = {\n          containerId: row.id,\n          servicePort: parseInt(value)\n        }\n        \n        this.updateContainerRoute(routeConfig).then((response) => {\n          if (response.code === 20000 && response.data && response.data.accessUrl) {\n            this.$message.success(`路由更新成功！新地址：${response.data.accessUrl}`)\n            this.getList()\n          } else {\n            this.$message.error(response.message || '更新失败')\n          }\n        }).catch(() => {\n          // 错误处理已在拦截器中处理\n        })\n      }).catch(() => {\n        // 取消操作\n      })\n    },\n    \n\n    \n    // 配置容器路由方法\n    async configContainerRoute(routeConfig) {\n      return configContainerRoute(routeConfig)\n    },\n    \n    // 更新容器路由方法\n    async updateContainerRoute(routeConfig) {\n      return updateContainerRoute(routeConfig)\n    },\n    \n    // 加载镜像列表\n    async loadImageList() {\n      this.imageListLoading = true\n      try {\n        const response = await getImageList()\n        if (response.code === 20000) {\n          // 处理分页数据结构\n          if (response.data && response.data.list) {\n            // 如果返回的是分页结构\n            this.imageList = response.data.list.map(image => {\n              return {\n                ...image,\n                // 确保 sizeMb 是数字类型\n                sizeMb: typeof image.sizeMb === 'string' ? parseInt(image.sizeMb) : image.sizeMb\n              }\n            })\n          } else if (Array.isArray(response.data)) {\n            // 如果返回的是直接数组\n            this.imageList = response.data.map(image => {\n              return {\n                ...image,\n                sizeMb: typeof image.sizeMb === 'string' ? parseInt(image.sizeMb) : image.sizeMb\n              }\n            })\n          } else {\n            this.imageList = []\n          }\n          \n          if (this.imageList.length === 0) {\n            this.$message.info('当前没有可用的镜像，请先构建或导入镜像')\n          }\n        } else {\n          this.$message.error('获取镜像列表失败：' + (response.message || '未知错误'))\n          this.imageList = []\n        }\n      } catch (error) {\n        console.error('加载镜像列表失败:', error)\n        this.$message.error('加载镜像列表失败')\n        this.imageList = []\n      } finally {\n        this.imageListLoading = false\n      }\n    },\n    \n    // 处理镜像选择变化\n    handleImageChange(imageId) {\n      if (imageId) {\n        this.selectedImage = this.imageList.find(img => img.id === imageId)\n        if (this.selectedImage) {\n          // 自动填充镜像名称和标签\n          this.deployForm.imageName = this.selectedImage.name\n          this.deployForm.imageTag = this.selectedImage.tag\n          \n          // 智能生成容器名称（如果当前为空）\n          if (!this.deployForm.containerName) {\n            this.suggestContainerName(this.selectedImage.name, this.selectedImage.tag)\n          }\n          \n          // 根据镜像类型智能推荐端口\n          this.suggestServicePort(this.selectedImage.name)\n        }\n      } else {\n        this.selectedImage = null\n        this.deployForm.imageName = ''\n        this.deployForm.imageTag = 'latest'\n      }\n    },\n    \n    // 智能生成容器名称\n    suggestContainerName(imageName, imageTag) {\n      // 移除镜像名称中的特殊字符，生成简洁的名称\n      let baseName = imageName.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase()\n      \n      // 如果标签不是latest，则加入标签\n      if (imageTag && imageTag !== 'latest') {\n        baseName += '-' + imageTag.replace(/[^a-zA-Z0-9]/g, '-')\n      }\n      \n      // 添加时间戳保证唯一性\n      const timestamp = Date.now().toString().slice(-6)\n      this.deployForm.containerName = `${baseName}-${timestamp}`\n    },\n    \n    // 根据镜像类型智能推荐端口\n    suggestServicePort(imageName) {\n      const portMap = {\n        'nginx': 80,\n        'apache': 80,\n        'httpd': 80,\n        'mysql': 3306,\n        'mariadb': 3306,\n        'postgres': 5432,\n        'postgresql': 5432,\n        'redis': 6379,\n        'mongodb': 27017,\n        'mongo': 27017,\n        'tomcat': 8080,\n        'node': 3000,\n        'spring': 8080,\n        'java': 8080\n      }\n      \n      // 查找匹配的镜像类型\n      for (const [key, port] of Object.entries(portMap)) {\n        if (imageName.toLowerCase().includes(key)) {\n          this.deployForm.servicePort = port\n          break\n        }\n      }\n    },\n    \n    // 格式化镜像大小\n    formatImageSize(sizeMb) {\n      if (!sizeMb || sizeMb === 0) return '-'\n      \n      // 确保是数字类型\n      const size = typeof sizeMb === 'string' ? parseInt(sizeMb) : sizeMb\n      if (isNaN(size)) return '-'\n      \n      // 如果已经是MB单位，直接显示\n      if (size < 1024) {\n        return `${size} MB`\n      }\n      \n      // 转换为GB\n      const sizeGb = size / 1024\n      return `${sizeGb.toFixed(1)} GB`\n    },\n    \n    // 获取分布策略显示名称\n    getStrategyDisplayName(strategy) {\n      const strategyMap = {\n        'SPREAD_ACROSS_NODES': '跨节点分散',\n        'WORKER_NODES_ONLY': '仅Worker节点',\n        'MANAGER_NODES_ONLY': '仅Manager节点',\n        'BALANCED': '平衡分布',\n        'SPREAD_ACROSS_ZONES': '跨可用区分散'\n      }\n      return strategyMap[strategy] || strategy\n    },\n    \n    // 获取推荐的分布策略\n    async getRecommendedDistributionStrategy(replicas) {\n      try {\n        const response = await getRecommendedStrategy(replicas)\n        if (response.code === 20000 && response.data) {\n          return response.data.recommendedStrategy\n        }\n      } catch (error) {\n        console.warn('获取推荐分布策略失败:', error)\n      }\n      return 'SPREAD_ACROSS_NODES' // 默认值\n    },\n    \n    // === HSM设备相关方法 ===\n    \n    // 处理HSM开关变化\n    handleHsmToggle(enabled) {\n      if (enabled) {\n        // 启用HSM时加载设备列表\n        this.loadHsmDeviceList()\n      } else {\n        // 禁用HSM时清空选择\n        this.deployForm.hsmDeviceId = null\n        this.selectedHsmDevice = null\n      }\n    },\n    \n    // 加载HSM设备列表\n    async loadHsmDeviceList() {\n      this.hsmDeviceListLoading = true\n      try {\n        const response = await getAvailableHsmDevices({\n          status: 'running'\n        })\n        \n        if (response.code === 20000) {\n          if (response.data && response.data.list) {\n            this.hsmDeviceList = response.data.list\n          } else if (Array.isArray(response.data)) {\n            this.hsmDeviceList = response.data\n          } else {\n            this.hsmDeviceList = []\n          }\n          \n          if (this.hsmDeviceList.length === 0) {\n            this.$message.info('当前没有可用的HSM设备')\n          }\n        } else {\n          this.$message.error('获取HSM设备列表失败：' + (response.message || '未知错误'))\n          this.hsmDeviceList = []\n        }\n      } catch (error) {\n        console.error('加载HSM设备列表失败:', error)\n        this.$message.error('加载HSM设备列表失败')\n        this.hsmDeviceList = []\n      } finally {\n        this.hsmDeviceListLoading = false\n      }\n    },\n    \n    // 处理HSM设备选择变化\n    handleHsmDeviceChange(deviceId) {\n      if (deviceId) {\n        this.selectedHsmDevice = this.hsmDeviceList.find(device => device.deviceId === deviceId)\n        if (this.selectedHsmDevice) {\n          // 可以在这里加载更详细的设备信息\n          this.loadHsmDeviceDetail(deviceId)\n        }\n      } else {\n        this.selectedHsmDevice = null\n      }\n    },\n    \n    // 加载HSM设备详细信息\n    async loadHsmDeviceDetail(deviceId) {\n      try {\n        const response = await getHsmDeviceDetail(deviceId)\n        if (response.code === 20000 && response.data) {\n          this.selectedHsmDevice = response.data\n        }\n      } catch (error) {\n        console.warn('获取HSM设备详细信息失败:', error)\n      }\n    },\n    \n\n  },\n  mounted() {\n    this.getList()\n    // 预加载镜像列表，提升用户体验\n    this.loadImageList()\n    // 预加载HSM设备列表\n    this.loadHsmDeviceList()\n  },\n  computed: {\n    ...mapGetters([\n      'user'\n    ])\n  },\n  watch: {\n    // 监听副本数变化，自动更新推荐的分布策略\n    'deployForm.replicas'(newReplicas, oldReplicas) {\n      if (newReplicas > 1 && newReplicas !== oldReplicas) {\n        this.getRecommendedDistributionStrategy(newReplicas).then(strategy => {\n          this.deployForm.distributionStrategy = strategy\n        })\n      } else if (newReplicas === 1) {\n        // 单副本时不需要分布策略\n        this.deployForm.distributionStrategy = 'BALANCED'\n      }\n    },\n    // 监听扩缩容副本数变化\n    'scaleForm.replicas'(newReplicas, oldReplicas) {\n      if (newReplicas > 1 && newReplicas !== oldReplicas) {\n        this.getRecommendedDistributionStrategy(newReplicas).then(strategy => {\n          this.scaleForm.distributionStrategy = strategy\n        })\n      } else if (newReplicas === 1) {\n        this.scaleForm.distributionStrategy = 'BALANCED'\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.filter-inner {\n  display: flex;\n  align-items: center;\n  flex-wrap: wrap;\n  \n  .el-form-item {\n    margin-bottom: 10px;\n    margin-right: 15px;\n  }\n}\n\n.pull-right {\n  float: right;\n}\n\n.clearfix::after {\n  content: \"\";\n  display: table;\n  clear: both;\n}\n\n.form-help {\n  font-size: 12px;\n  color: #909399;\n  margin-top: 4px;\n}\n\n.el-link {\n  font-size: 12px;\n  max-width: 150px;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  display: inline-block;\n}\n\n.image-info {\n  background-color: #f5f7fa;\n  padding: 12px;\n  border-radius: 4px;\n  border: 1px solid #e4e7ed;\n  \n  p {\n    margin: 4px 0;\n    font-size: 13px;\n    \n    strong {\n      color: #303133;\n      font-weight: 500;\n    }\n  }\n}\n\n.device-info {\n  background-color: #f0f9ff;\n  padding: 12px;\n  border-radius: 4px;\n  border: 1px solid #a7f3d0;\n  \n  p {\n    margin: 4px 0;\n    font-size: 13px;\n    \n    strong {\n      color: #303133;\n      font-weight: 500;\n    }\n  }\n}\n\n.strategy-info {\n  font-size: 11px;\n  color: #909399;\n  margin-top: 2px;\n}\n\n.hsm-info {\n  font-size: 11px;\n  color: #67c23a;\n  margin-top: 2px;\n}\n</style>"]}]}