# 容器部署API测试示例

## 测试用例

### 1. 基础部署测试

```bash
curl -X POST http://localhost:8080/api-management/swarm/container/v1/deploy \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "containerName": "test-basic",
    "imageId": 1,
    "imageName": "nginx",
    "imageTag": "latest",
    "applicationId": 1,
    "replicas": 1,
    "deployedBy": 1
  }'
```

### 2. HSM设备配置测试

```bash
curl -X POST http://localhost:8080/api-management/swarm/container/v1/deploy \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "containerName": "test-hsm",
    "imageId": 1,
    "applicationId": 1,
    "hsmDeviceConfig": {
      "encryptorId": 1,
      "encryptorName": "HSM-001",
      "serverIpAddr": "*************",
      "serverPort": 1024,
      "tcpConnNum": 5,
      "msgHeadLen": 4,
      "msgTailLen": 0,
      "asciiOrEbcdic": 0,
      "dynamicLibPath": "./libdeviceapi.so"
    },
    "deployedBy": 1
  }'
```

### 3. 分布策略测试

```bash
curl -X POST http://localhost:8080/api-management/swarm/container/v1/deploy \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "containerName": "test-strategy",
    "imageId": 1,
    "applicationId": 1,
    "replicas": 3,
    "distributionStrategy": "SPREAD_ACROSS_NODES",
    "deployedBy": 1
  }'
```

### 4. Traefik路由测试

```bash
curl -X POST http://localhost:8080/api-management/swarm/container/v1/deploy \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "containerName": "test-traefik",
    "imageId": 1,
    "applicationId": 1,
    "traefikConfig": {
      "enabled": true,
      "domain": "test-traefik.ccsp.local",
      "servicePort": 80,
      "pathPrefix": "/",
      "httpsEnabled": false
    },
    "deployedBy": 1
  }'
```

### 5. 设备资源配置测试

```bash
curl -X POST http://localhost:8080/api-management/swarm/container/v1/deploy \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "containerName": "test-device-resource",
    "imageId": 1,
    "applicationId": 1,
    "deviceResourceConfig": {
      "deviceResourceType": "VSM",
      "deviceResourceIds": [1, 2],
      "allocationType": "exclusive",
      "priority": 1,
      "configData": "{\"key\": \"value\"}"
    },
    "deployedBy": 1
  }'
```

### 6. 完整配置测试

```bash
curl -X POST http://localhost:8080/api-management/swarm/container/v1/deploy \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "containerName": "test-full-config",
    "imageId": 1,
    "applicationId": 1,
    "replicas": 2,
    "hsmDeviceConfig": {
      "encryptorId": 1,
      "serverIpAddr": "*************",
      "serverPort": 1024
    },
    "distributionStrategy": "SPREAD_ACROSS_NODES",
    "traefikConfig": {
      "enabled": true,
      "domain": "test-full.ccsp.local",
      "servicePort": 8080
    },
    "deviceResourceConfig": {
      "deviceResourceType": "VSM",
      "deviceResourceIds": [1],
      "allocationType": "exclusive"
    },
    "environmentVars": {
      "ENV": "test",
      "DEBUG": "true"
    },
    "constraints": ["node.role==worker"],
    "labels": {
      "app": "test-full-config",
      "version": "1.0"
    },
    "deployedBy": 1
  }'
```

## 预期响应

### 成功响应示例

```json
{
  "code": 20000,
  "message": "success",
  "data": {
    "container": {
      "id": 123,
      "containerName": "test-full-config",
      "status": "running",
      "serviceId": "service_abc123",
      "imageId": 1,
      "imageName": "nginx",
      "imageTag": "latest",
      "replicas": 2,
      "createTime": "2024-01-01T10:00:00"
    },
    "hsmConfigured": true,
    "hsmDeviceConfig": {
      "encryptorId": 1,
      "serverIpAddr": "*************",
      "serverPort": 1024
    },
    "distributionStrategy": "SPREAD_ACROSS_NODES",
    "appliedConstraints": ["node.role==worker"],
    "traefikConfigured": true,
    "traefikConfig": {
      "enabled": true,
      "domain": "test-full.ccsp.local",
      "servicePort": 8080
    },
    "accessUrl": "http://test-full.ccsp.local",
    "deviceResourceConfigured": true,
    "deviceResourceConfig": {
      "deviceResourceType": "VSM",
      "deviceResourceIds": [1],
      "allocationType": "exclusive"
    }
  }
}
```

### 错误响应示例

```json
{
  "code": 50000,
  "message": "部署容器失败：指定的镜像不存在",
  "data": null
}
```

## 验证步骤

### 1. 验证容器创建
```bash
# 查看容器列表
curl -X GET http://localhost:8080/api-management/swarm/container/v1/list-page \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 2. 验证HSM环境变量
```bash
# 查看容器详情，检查环境变量
curl -X GET http://localhost:8080/api-management/swarm/container/v1/{containerId} \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 3. 验证Traefik访问
```bash
# 访问Traefik配置的域名
curl -H "Host: test-traefik.ccsp.local" http://localhost
```

### 4. 验证设备资源关联
```bash
# 查看容器关联的设备资源
curl -X GET http://localhost:8080/api-management/swarm/container/v1/{containerId}/device-resources \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 5. 验证分布策略
```bash
# 检查Docker Swarm服务的约束条件
docker service inspect {serviceId} --format '{{.Spec.TaskTemplate.Placement.Constraints}}'
```

## 兼容性测试

### 测试已弃用接口仍然可用

```bash
# 测试 /deploy-with-hsm（应该仍然工作）
curl -X POST http://localhost:8080/api-management/swarm/container/v1/deploy-with-hsm \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "containerName": "test-deprecated-hsm",
    "imageId": 1,
    "applicationId": 1,
    "hsmDeviceConfig": {
      "encryptorId": 1,
      "serverIpAddr": "*************",
      "serverPort": 1024
    },
    "deployedBy": 1
  }'

# 测试 /deploy-with-strategy（应该仍然工作）
curl -X POST http://localhost:8080/api-management/swarm/container/v1/deploy-with-strategy \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "containerName": "test-deprecated-strategy",
    "imageId": 1,
    "applicationId": 1,
    "replicas": 2,
    "distributionStrategy": "SPREAD_ACROSS_NODES",
    "deployedBy": 1
  }'

# 测试 /deploy-with-traefik（应该仍然工作）
curl -X POST http://localhost:8080/api-management/swarm/container/v1/deploy-with-traefik \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "containerName": "test-deprecated-traefik",
    "imageId": 1,
    "applicationId": 1,
    "traefikConfig": {
      "enabled": true,
      "domain": "test-deprecated.ccsp.local",
      "servicePort": 80
    },
    "deployedBy": 1
  }'
```

## 注意事项

1. 替换 `YOUR_TOKEN` 为实际的认证令牌
2. 替换 `{containerId}` 和 `{serviceId}` 为实际的ID
3. 确保测试环境中有可用的镜像和设备资源
4. 检查日志中的弃用警告信息
5. 验证所有配置选项都能正确应用
