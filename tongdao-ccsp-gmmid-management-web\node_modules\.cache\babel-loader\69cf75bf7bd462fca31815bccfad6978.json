{"remainingRequest": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\src\\api\\docker\\hsm.js", "dependencies": [{"path": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\src\\api\\docker\\hsm.js", "mtime": 1756805153627}, {"path": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\babel.config.js", "mtime": 1699511114284}, {"path": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1729062151209}, {"path": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1729062151198}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "getAvailableHsmDevices", "params", "url", "method", "getHsmDeviceDetail", "vsmId", "concat", "deployContainerWithHsm", "data", "console", "warn", "Promise", "resolve", "then", "_interopRequireWildcard", "require", "containerApi", "deployContainer"], "sources": ["D:/workspace/ccsp_v3/tongdao-ccsp-gmmid-management-web/src/api/docker/hsm.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n/**\r\n * 获取可用的HSM设备资源列表\r\n * @param {Object} params 查询参数\r\n * @param {number} params.page 页码\r\n * @param {number} params.pageSize 页大小\r\n * @param {string} params.status 设备状态\r\n * @param {number} params.deviceGroupId 设备组ID\r\n */\r\nexport function getAvailableHsmDevices(params) {\r\n  return request({\r\n    url: '/api-management/swarm/container/v1/hsm-devices',\r\n    method: 'get',\r\n    params\r\n  })\r\n}\r\n\r\n/**\r\n * 获取HSM设备详细信息\r\n * @param {number} vsmId 设备ID\r\n */\r\nexport function getHsmDeviceDetail(vsmId) {\r\n  return request({\r\n    url: `/api-management/swarm/container/v1/hsm-devices/${vsmId}`,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n/**\r\n * 部署容器（带HSM配置）\r\n * @deprecated 请使用 container.js 中的统一 deployContainer 方法\r\n * @param {Object} data 部署数据\r\n */\r\nexport function deployContainerWithHsm(data) {\r\n  console.warn('deployContainerWithHsm 已弃用，请使用统一的 deployContainer')\r\n  // 导入并调用统一的部署方法\r\n  import('./container').then(containerApi => {\r\n    return containerApi.deployContainer(data)\r\n  })\r\n}"], "mappings": ";;;;AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,sBAAsBA,CAACC,MAAM,EAAE;EAC7C,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,gDAAgD;IACrDC,MAAM,EAAE,KAAK;IACbF,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASG,kBAAkBA,CAACC,KAAK,EAAE;EACxC,OAAON,OAAO,CAAC;IACbG,GAAG,oDAAAI,MAAA,CAAoDD,KAAK,CAAE;IAC9DF,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASI,sBAAsBA,CAACC,IAAI,EAAE;EAC3CC,OAAO,CAACC,IAAI,CAAC,mDAAmD,CAAC;EACjE;EACAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;IAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAO,aAAa;EAAA,GAAEF,IAAI,CAAC,UAAAG,YAAY,EAAI;IACzC,OAAOA,YAAY,CAACC,eAAe,CAACT,IAAI,CAAC;EAC3C,CAAC,CAAC;AACJ", "ignoreList": []}]}