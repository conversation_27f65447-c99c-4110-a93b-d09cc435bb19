package tech.tongdao.ccsp.management.modules.swarm.form;

import tech.tongdao.ccsp.management.modules.swarm.utils.SwarmPlacementHelper;
import tech.tongdao.ccsp.management.modules.swarm.utils.TraefikConfigHelper.TraefikConfig;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 容器部署配置表单对象
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
public class ContainerDeployForm implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 容器名称
     */
    private String containerName;

    /**
     * 使用的镜像ID
     */
    private Long imageId;

    /**
     * 镜像名称
     */
    private String imageName;

    /**
     * 镜像标签
     */
    private String imageTag;

    /**
     * 关联的应用ID（必填）
     */
    private Long applicationId;

    /**
     * 关联的应用名称（可选，便于透传与展示）
     */
    private String applicationName;

    /**
     * 部署节点ID（可选，不指定则由Swarm自动调度）
     */
    private Long nodeId;

    /**
     * 节点名称
     */
    private String nodeName;

    /**
     * 副本数量
     */
    private Integer replicas = 1;

    /**
     * 端口映射配置
     * 格式：[{"containerPort": 8080, "hostPort": 8080, "protocol": "tcp"}]
     */
    private List<PortMapping> portMappings;

    /**
     * 环境变量
     * 格式：{"ENV_NAME": "ENV_VALUE"}
     */
    private Map<String, String> environmentVars;

    /**
     * 卷挂载配置
     * 格式：[{"source": "/host/path", "target": "/container/path", "type": "bind"}]
     */
    private List<VolumeMount> volumeMounts;

    /**
     * 资源限制配置
     */
    private ResourceLimits resourceLimits;

    /**
     * 网络配置
     */
    private List<String> networks;

    /**
     * 约束条件
     * 格式：["node.role==worker", "node.labels.env==prod"]
     */
    private List<String> constraints;

    /**
     * 容器标签
     * 格式：{"key": "value"}
     */
    private Map<String, String> labels;

    /**
     * 重启策略
     */
    private String restartPolicy = "any";

    /**
     * 更新配置
     */
    private UpdateConfig updateConfig;

    /**
     * 部署用户ID
     */
    private Long deployedBy;

    /**
     * HSM设备资源配置
     */
    private HsmDeviceConfig hsmDeviceConfig;

    /**
     * 设备资源配置
     */
    private DeviceResourceConfig deviceResourceConfig;

    /**
     * 分布策略配置
     */
    private SwarmPlacementHelper.DistributionStrategy distributionStrategy;

    /**
     * Traefik路由配置
     */
    private TraefikConfig traefikConfig;

    /**
     * 端口映射内部类
     */
    public static class PortMapping implements Serializable {
        private Integer containerPort;
        
        private Integer hostPort;
        
        private String protocol = "tcp";

        public PortMapping() {
        }

        public PortMapping(Integer containerPort, Integer hostPort, String protocol) {
            this.containerPort = containerPort;
            this.hostPort = hostPort;
            this.protocol = protocol;
        }

        public Integer getContainerPort() {
            return containerPort;
        }

        public void setContainerPort(Integer containerPort) {
            this.containerPort = containerPort;
        }

        public Integer getHostPort() {
            return hostPort;
        }

        public void setHostPort(Integer hostPort) {
            this.hostPort = hostPort;
        }

        public String getProtocol() {
            return protocol;
        }

        public void setProtocol(String protocol) {
            this.protocol = protocol;
        }

        @Override
        public String toString() {
            return "PortMapping{" +
                    "containerPort=" + containerPort +
                    ", hostPort=" + hostPort +
                    ", protocol='" + protocol + '\'' +
                    '}';
        }
    }

    /**
     * 卷挂载内部类
     */
    public static class VolumeMount implements Serializable {
        private String source;
        private String target;
        private String type = "bind";
        private Boolean readOnly = false;

        public VolumeMount() {
        }

        public VolumeMount(String source, String target, String type, Boolean readOnly) {
            this.source = source;
            this.target = target;
            this.type = type;
            this.readOnly = readOnly;
        }

        public String getSource() {
            return source;
        }

        public void setSource(String source) {
            this.source = source;
        }

        public String getTarget() {
            return target;
        }

        public void setTarget(String target) {
            this.target = target;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public Boolean getReadOnly() {
            return readOnly;
        }

        public void setReadOnly(Boolean readOnly) {
            this.readOnly = readOnly;
        }

        @Override
        public String toString() {
            return "VolumeMount{" +
                    "source='" + source + '\'' +
                    ", target='" + target + '\'' +
                    ", type='" + type + '\'' +
                    ", readOnly=" + readOnly +
                    '}';
        }
    }

    /**
     * 资源限制内部类
     */
    public static class ResourceLimits implements Serializable {
        private Long memoryMb;
        private Double cpuLimit;
        private Long memoryReservationMb;
        private Double cpuReservation;

        public ResourceLimits() {
        }

        public ResourceLimits(Long memoryMb, Double cpuLimit, Long memoryReservationMb, Double cpuReservation) {
            this.memoryMb = memoryMb;
            this.cpuLimit = cpuLimit;
            this.memoryReservationMb = memoryReservationMb;
            this.cpuReservation = cpuReservation;
        }

        public Long getMemoryMb() {
            return memoryMb;
        }

        public void setMemoryMb(Long memoryMb) {
            this.memoryMb = memoryMb;
        }

        public Double getCpuLimit() {
            return cpuLimit;
        }

        public void setCpuLimit(Double cpuLimit) {
            this.cpuLimit = cpuLimit;
        }

        public Long getMemoryReservationMb() {
            return memoryReservationMb;
        }

        public void setMemoryReservationMb(Long memoryReservationMb) {
            this.memoryReservationMb = memoryReservationMb;
        }

        public Double getCpuReservation() {
            return cpuReservation;
        }

        public void setCpuReservation(Double cpuReservation) {
            this.cpuReservation = cpuReservation;
        }

        @Override
        public String toString() {
            return "ResourceLimits{" +
                    "memoryMb=" + memoryMb +
                    ", cpuLimit=" + cpuLimit +
                    ", memoryReservationMb=" + memoryReservationMb +
                    ", cpuReservation=" + cpuReservation +
                    '}';
        }
    }

    /**
     * 更新配置内部类
     */
    public static class UpdateConfig implements Serializable {
        private Integer parallelism = 1;
        private Long delaySeconds = 10L;
        private String failureAction = "pause";
        private String order = "stop-first";

        public UpdateConfig() {
        }

        public UpdateConfig(Integer parallelism, Long delaySeconds, String failureAction, String order) {
            this.parallelism = parallelism;
            this.delaySeconds = delaySeconds;
            this.failureAction = failureAction;
            this.order = order;
        }

        public Integer getParallelism() {
            return parallelism;
        }

        public void setParallelism(Integer parallelism) {
            this.parallelism = parallelism;
        }

        public Long getDelaySeconds() {
            return delaySeconds;
        }

        public void setDelaySeconds(Long delaySeconds) {
            this.delaySeconds = delaySeconds;
        }

        public String getFailureAction() {
            return failureAction;
        }

        public void setFailureAction(String failureAction) {
            this.failureAction = failureAction;
        }

        public String getOrder() {
            return order;
        }

        public void setOrder(String order) {
            this.order = order;
        }

        @Override
        public String toString() {
            return "UpdateConfig{" +
                    "parallelism=" + parallelism +
                    ", delaySeconds=" + delaySeconds +
                    ", failureAction='" + failureAction + '\'' +
                    ", order='" + order + '\'' +
                    '}';
        }
    }

    public String getContainerName() {
        return containerName;
    }

    public void setContainerName(String containerName) {
        this.containerName = containerName;
    }

    public Long getImageId() {
        return imageId;
    }

    public void setImageId(Long imageId) {
        this.imageId = imageId;
    }

    public String getImageName() {
        return imageName;
    }

    public void setImageName(String imageName) {
        this.imageName = imageName;
    }

    public String getImageTag() {
        return imageTag;
    }

    public void setImageTag(String imageTag) {
        this.imageTag = imageTag;
    }

    public Long getApplicationId() {
        return applicationId;
    }

    public void setApplicationId(Long applicationId) {
        this.applicationId = applicationId;
    }

    public String getApplicationName() {
        return applicationName;
    }

    public void setApplicationName(String applicationName) {
        this.applicationName = applicationName;
    }

    public Long getNodeId() {
        return nodeId;
    }

    public void setNodeId(Long nodeId) {
        this.nodeId = nodeId;
    }

    public String getNodeName() {
        return nodeName;
    }

    public void setNodeName(String nodeName) {
        this.nodeName = nodeName;
    }

    public Integer getReplicas() {
        return replicas;
    }

    public void setReplicas(Integer replicas) {
        this.replicas = replicas;
    }

    public List<PortMapping> getPortMappings() {
        return portMappings;
    }

    public void setPortMappings(List<PortMapping> portMappings) {
        this.portMappings = portMappings;
    }

    public Map<String, String> getEnvironmentVars() {
        return environmentVars;
    }

    public void setEnvironmentVars(Map<String, String> environmentVars) {
        this.environmentVars = environmentVars;
    }

    public List<VolumeMount> getVolumeMounts() {
        return volumeMounts;
    }

    public void setVolumeMounts(List<VolumeMount> volumeMounts) {
        this.volumeMounts = volumeMounts;
    }

    public ResourceLimits getResourceLimits() {
        return resourceLimits;
    }

    public void setResourceLimits(ResourceLimits resourceLimits) {
        this.resourceLimits = resourceLimits;
    }

    public List<String> getNetworks() {
        return networks;
    }

    public void setNetworks(List<String> networks) {
        this.networks = networks;
    }

    public List<String> getConstraints() {
        return constraints;
    }

    public void setConstraints(List<String> constraints) {
        this.constraints = constraints;
    }

    public Map<String, String> getLabels() {
        return labels;
    }

    public void setLabels(Map<String, String> labels) {
        this.labels = labels;
    }

    public String getRestartPolicy() {
        return restartPolicy;
    }

    public void setRestartPolicy(String restartPolicy) {
        this.restartPolicy = restartPolicy;
    }

    public UpdateConfig getUpdateConfig() {
        return updateConfig;
    }

    public void setUpdateConfig(UpdateConfig updateConfig) {
        this.updateConfig = updateConfig;
    }

    public Long getDeployedBy() {
        return deployedBy;
    }

    public void setDeployedBy(Long deployedBy) {
        this.deployedBy = deployedBy;
    }

    public HsmDeviceConfig getHsmDeviceConfig() {
        return hsmDeviceConfig;
    }

    public void setHsmDeviceConfig(HsmDeviceConfig hsmDeviceConfig) {
        this.hsmDeviceConfig = hsmDeviceConfig;
    }

    public DeviceResourceConfig getDeviceResourceConfig() {
        return deviceResourceConfig;
    }

    public void setDeviceResourceConfig(DeviceResourceConfig deviceResourceConfig) {
        this.deviceResourceConfig = deviceResourceConfig;
    }

    public SwarmPlacementHelper.DistributionStrategy getDistributionStrategy() {
        return distributionStrategy;
    }

    public void setDistributionStrategy(SwarmPlacementHelper.DistributionStrategy distributionStrategy) {
        this.distributionStrategy = distributionStrategy;
    }

    public TraefikConfig getTraefikConfig() {
        return traefikConfig;
    }

    public void setTraefikConfig(TraefikConfig traefikConfig) {
        this.traefikConfig = traefikConfig;
    }

    /**
     * 验证部署配置是否有效
     */
    public boolean isValidDeployConfig() {
        // 检查必填字段
        if (containerName == null || containerName.trim().isEmpty()) {
            return false;
        }
        if (imageId == null) {
            return false;
        }
        if (imageName == null || imageName.trim().isEmpty()) {
            return false;
        }
        if (imageTag == null || imageTag.trim().isEmpty()) {
            return false;
        }
        // 应用ID必须存在
        if (applicationId == null) {
            return false;
        }

        // 检查副本数量
        if (replicas == null || replicas < 1 || replicas > 100) {
            return false;
        }

        // 检查端口映射
        if (portMappings != null && !portMappings.isEmpty()) {
            for (PortMapping mapping : portMappings) {
                if (mapping.getContainerPort() == null || mapping.getContainerPort() < 1 || mapping.getContainerPort() > 65535) {
                    return false;
                }
                if (mapping.getHostPort() != null && (mapping.getHostPort() < 1 || mapping.getHostPort() > 65535)) {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * 获取完整的镜像名称（包含标签）
     */
    public String getFullImageName() {
        if (imageName == null || imageTag == null) {
            return null;
        }
        return imageName + ":" + imageTag;
    }

    /**
     * 添加端口映射
     */
    public void addPortMapping(Integer containerPort, Integer hostPort, String protocol) {
        if (portMappings == null) {
            portMappings = new java.util.ArrayList<>();
        }
        portMappings.add(new PortMapping(containerPort, hostPort, protocol));
    }

    /**
     * 添加环境变量
     */
    public void addEnvironmentVar(String name, String value) {
        if (environmentVars == null) {
            environmentVars = new java.util.HashMap<>();
        }
        environmentVars.put(name, value);
    }

    /**
     * 添加卷挂载
     */
    public void addVolumeMount(String source, String target, String type, Boolean readOnly) {
        if (volumeMounts == null) {
            volumeMounts = new java.util.ArrayList<>();
        }
        volumeMounts.add(new VolumeMount(source, target, type, readOnly));
    }

    /**
     * 添加约束条件
     */
    public void addConstraint(String constraint) {
        if (constraints == null) {
            constraints = new java.util.ArrayList<>();
        }
        constraints.add(constraint);
    }

    /**
     * 添加网络
     */
    public void addNetwork(String network) {
        if (networks == null) {
            networks = new java.util.ArrayList<>();
        }
        networks.add(network);
    }

    @Override
    public String toString() {
        return "ContainerDeployForm{" +
                "containerName='" + containerName + '\'' +
                ", imageId=" + imageId +
                ", imageName='" + imageName + '\'' +
                ", imageTag='" + imageTag + '\'' +
                ", applicationId=" + applicationId +
                ", applicationName='" + applicationName + '\'' +
                ", nodeId=" + nodeId +
                ", nodeName='" + nodeName + '\'' +
                ", replicas=" + replicas +
                ", portMappings=" + portMappings +
                ", environmentVars=" + environmentVars +
                ", volumeMounts=" + volumeMounts +
                ", resourceLimits=" + resourceLimits +
                ", networks=" + networks +
                ", constraints=" + constraints +
                ", restartPolicy='" + restartPolicy + '\'' +
                ", updateConfig=" + updateConfig +
                ", deployedBy=" + deployedBy +
                ", hsmDeviceConfig=" + hsmDeviceConfig +
                '}';
    }

    /**
     * HSM设备资源配置内部类
     */
    public static class HsmDeviceConfig implements Serializable {
        /**
         * 选中的加密机组ID
         */
        private Long encryptorGroupId;
        
        /**
         * 选中的加密机ID
         */
        private Long encryptorId;
        
        /**
         * 加密机名称
         */
        private String encryptorName;
        
        /**
         * 加密机IP地址
         */
        private String serverIpAddr;
        
        /**
         * 加密机端口
         */
        private Integer serverPort;
        
        /**
         * TCP连接数
         */
        private Integer tcpConnNum;
        
        /**
         * 消息头长度
         */
        private Integer msgHeadLen;
        
        /**
         * 消息尾长度
         */
        private Integer msgTailLen;
        
        /**
         * 编码类型(0-ASCII, 1-EBCDIC)
         */
        private Integer asciiOrEbcdic;
        
        /**
         * 动态库路径
         */
        private String dynamicLibPath;

        public HsmDeviceConfig() {
        }

        public HsmDeviceConfig(Long encryptorGroupId, Long encryptorId, String encryptorName, 
                               String serverIpAddr, Integer serverPort, Integer tcpConnNum, 
                               Integer msgHeadLen, Integer msgTailLen, Integer asciiOrEbcdic, 
                               String dynamicLibPath) {
            this.encryptorGroupId = encryptorGroupId;
            this.encryptorId = encryptorId;
            this.encryptorName = encryptorName;
            this.serverIpAddr = serverIpAddr;
            this.serverPort = serverPort;
            this.tcpConnNum = tcpConnNum;
            this.msgHeadLen = msgHeadLen;
            this.msgTailLen = msgTailLen;
            this.asciiOrEbcdic = asciiOrEbcdic;
            this.dynamicLibPath = dynamicLibPath;
        }

        public Long getEncryptorGroupId() {
            return encryptorGroupId;
        }

        public void setEncryptorGroupId(Long encryptorGroupId) {
            this.encryptorGroupId = encryptorGroupId;
        }

        public Long getEncryptorId() {
            return encryptorId;
        }

        public void setEncryptorId(Long encryptorId) {
            this.encryptorId = encryptorId;
        }

        public String getEncryptorName() {
            return encryptorName;
        }

        public void setEncryptorName(String encryptorName) {
            this.encryptorName = encryptorName;
        }

        public String getServerIpAddr() {
            return serverIpAddr;
        }

        public void setServerIpAddr(String serverIpAddr) {
            this.serverIpAddr = serverIpAddr;
        }

        public Integer getServerPort() {
            return serverPort;
        }

        public void setServerPort(Integer serverPort) {
            this.serverPort = serverPort;
        }

        public Integer getTcpConnNum() {
            return tcpConnNum;
        }

        public void setTcpConnNum(Integer tcpConnNum) {
            this.tcpConnNum = tcpConnNum;
        }

        public Integer getMsgHeadLen() {
            return msgHeadLen;
        }

        public void setMsgHeadLen(Integer msgHeadLen) {
            this.msgHeadLen = msgHeadLen;
        }

        public Integer getMsgTailLen() {
            return msgTailLen;
        }

        public void setMsgTailLen(Integer msgTailLen) {
            this.msgTailLen = msgTailLen;
        }

        public Integer getAsciiOrEbcdic() {
            return asciiOrEbcdic;
        }

        public void setAsciiOrEbcdic(Integer asciiOrEbcdic) {
            this.asciiOrEbcdic = asciiOrEbcdic;
        }

        public String getDynamicLibPath() {
            return dynamicLibPath;
        }

        public void setDynamicLibPath(String dynamicLibPath) {
            this.dynamicLibPath = dynamicLibPath;
        }

        @Override
        public String toString() {
            return "HsmDeviceConfig{" +
                    "encryptorGroupId=" + encryptorGroupId +
                    ", encryptorId=" + encryptorId +
                    ", encryptorName='" + encryptorName + '\'' +
                    ", serverIpAddr='" + serverIpAddr + '\'' +
                    ", serverPort=" + serverPort +
                    ", tcpConnNum=" + tcpConnNum +
                    ", msgHeadLen=" + msgHeadLen +
                    ", msgTailLen=" + msgTailLen +
                    ", asciiOrEbcdic=" + asciiOrEbcdic +
                    ", dynamicLibPath='" + dynamicLibPath + '\'' +
                    '}';
        }
    }

    /**
     * 设备资源配置内部类
     */
    public static class DeviceResourceConfig implements Serializable {
        /**
         * 设备资源类型(VSM/CHSM)
         */
        private String deviceResourceType;

        /**
         * 选中的设备资源ID列表
         */
        private List<Long> deviceResourceIds;

        /**
         * 分配类型(exclusive/shared)
         */
        private String allocationType = "exclusive";

        /**
         * 优先级(1-10)
         */
        private Integer priority = 1;

        /**
         * 设备配置数据(JSON格式)
         */
        private String configData;

        public DeviceResourceConfig() {
        }

        public DeviceResourceConfig(String deviceResourceType, List<Long> deviceResourceIds,
                                  String allocationType, Integer priority, String configData) {
            this.deviceResourceType = deviceResourceType;
            this.deviceResourceIds = deviceResourceIds;
            this.allocationType = allocationType;
            this.priority = priority;
            this.configData = configData;
        }

        public String getDeviceResourceType() {
            return deviceResourceType;
        }

        public void setDeviceResourceType(String deviceResourceType) {
            this.deviceResourceType = deviceResourceType;
        }

        public List<Long> getDeviceResourceIds() {
            return deviceResourceIds;
        }

        public void setDeviceResourceIds(List<Long> deviceResourceIds) {
            this.deviceResourceIds = deviceResourceIds;
        }

        public String getAllocationType() {
            return allocationType;
        }

        public void setAllocationType(String allocationType) {
            this.allocationType = allocationType;
        }

        public Integer getPriority() {
            return priority;
        }

        public void setPriority(Integer priority) {
            this.priority = priority;
        }

        public String getConfigData() {
            return configData;
        }

        public void setConfigData(String configData) {
            this.configData = configData;
        }

        @Override
        public String toString() {
            return "DeviceResourceConfig{" +
                    "deviceResourceType='" + deviceResourceType + '\'' +
                    ", deviceResourceIds=" + deviceResourceIds +
                    ", allocationType='" + allocationType + '\'' +
                    ", priority=" + priority +
                    ", configData='" + configData + '\'' +
                    '}';
        }
    }
}