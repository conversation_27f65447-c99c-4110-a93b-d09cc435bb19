{"remainingRequest": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\src\\views\\docker\\container\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\src\\views\\docker\\container\\index.vue", "mtime": 1756804572859}, {"path": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\babel.config.js", "mtime": 1699511114284}, {"path": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1729062151209}, {"path": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1729062151198}, {"path": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1729062151209}, {"path": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1729062152627}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9zbGljZWRUb0FycmF5IGZyb20gIkQ6L3dvcmtzcGFjZS9jY3NwX3YzL3RvbmdkYW8tY2NzcC1nbW1pZC1tYW5hZ2VtZW50LXdlYi9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vc2xpY2VkVG9BcnJheS5qcyI7CmltcG9ydCBfcmVnZW5lcmF0b3IgZnJvbSAiRDovd29ya3NwYWNlL2Njc3BfdjMvdG9uZ2Rhby1jY3NwLWdtbWlkLW1hbmFnZW1lbnQtd2ViL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9yZWdlbmVyYXRvci5qcyI7CmltcG9ydCBfYXN5bmNUb0dlbmVyYXRvciBmcm9tICJEOi93b3Jrc3BhY2UvY2NzcF92My90b25nZGFvLWNjc3AtZ21taWQtbWFuYWdlbWVudC13ZWIvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL2FzeW5jVG9HZW5lcmF0b3IuanMiOwppbXBvcnQgX29iamVjdFNwcmVhZCBmcm9tICJEOi93b3Jrc3BhY2UvY2NzcF92My90b25nZGFvLWNjc3AtZ21taWQtbWFuYWdlbWVudC13ZWIvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL29iamVjdFNwcmVhZDIuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5jb25jYXQuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5maW5kLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuaW5jbHVkZXMuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5qb2luLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkubWFwLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuc2xpY2UuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5mdW5jdGlvbi5uYW1lLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMubnVtYmVyLnRvLWZpeGVkLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMub2JqZWN0LmVudHJpZXMuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3Qua2V5cy5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC50by1zdHJpbmcuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5yZWdleHAuZXhlYy5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLnJlZ2V4cC50by1zdHJpbmcuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcuaW5jbHVkZXMuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcuaXRlcmF0b3IuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcucmVwbGFjZS5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzbmV4dC5pdGVyYXRvci5jb25zdHJ1Y3Rvci5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzbmV4dC5pdGVyYXRvci5maW5kLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXNuZXh0Lml0ZXJhdG9yLm1hcC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL3dlYi5kb20tY29sbGVjdGlvbnMuaXRlcmF0b3IuanMiOwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwoKaW1wb3J0IHsgbWFwR2V0dGVycyB9IGZyb20gJ3Z1ZXgnOwppbXBvcnQgUGFnaW5hdGlvbiBmcm9tICdAL2NvbXBvbmVudHMvUGFnaW5hdGlvbic7CmltcG9ydCB7IGdldENvbnRhaW5lckxpc3QsIGRlcGxveUNvbnRhaW5lciwgZGVwbG95Q29udGFpbmVyV2l0aFRyYWVmaWssIGRlcGxveUNvbnRhaW5lcldpdGhTdHJhdGVneSwgZ2V0UmVjb21tZW5kZWRTdHJhdGVneSwgc3RhcnRDb250YWluZXIsIHN0b3BDb250YWluZXIsIHJlc3RhcnRDb250YWluZXIsIHJlbW92ZUNvbnRhaW5lciwgZ2V0Q29udGFpbmVyTG9ncywgc3luY0NvbnRhaW5lclN0YXR1cywgc2NhbGVDb250YWluZXIsIHVwZGF0ZUNvbnRhaW5lckltYWdlLCBjb25maWdDb250YWluZXJSb3V0ZSBhcyBfY29uZmlnQ29udGFpbmVyUm91dGUsIHVwZGF0ZUNvbnRhaW5lclJvdXRlIGFzIF91cGRhdGVDb250YWluZXJSb3V0ZSB9IGZyb20gJ0AvYXBpL2RvY2tlci9jb250YWluZXInOwppbXBvcnQgeyBnZXRJbWFnZUxpc3QgfSBmcm9tICdAL2FwaS9kb2NrZXIvaW1hZ2UnOwppbXBvcnQgeyBmZXRjaEFwcGxpY2F0aW9uT3B0aW9ucywgZmV0Y2hVc2VyQXBwcyB9IGZyb20gJ0AvYXBpL2FwcGxpY2F0aW9uJzsKaW1wb3J0IHsgZ2V0QXZhaWxhYmxlSHNtRGV2aWNlcywgZ2V0SHNtRGV2aWNlRGV0YWlsLCBkZXBsb3lDb250YWluZXJXaXRoSHNtIH0gZnJvbSAnQC9hcGkvZG9ja2VyL2hzbSc7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnRG9ja2VyQ29udGFpbmVySW5kZXgnLAogIGNvbXBvbmVudHM6IHsKICAgIFBhZ2luYXRpb246IFBhZ2luYXRpb24KICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICB0YWJsZUxpc3Q6IFtdLAogICAgICBsb2FkaW5nOiBmYWxzZSwKICAgICAgdG90YWw6IDAsCiAgICAgIGxpc3RRdWVyeTogewogICAgICAgIHBhZ2U6IDEsCiAgICAgICAgcGFnZVNpemU6IDIwLAogICAgICAgIGNvbnRhaW5lck5hbWU6IHVuZGVmaW5lZCwKICAgICAgICBzdGF0dXM6IHVuZGVmaW5lZAogICAgICB9LAogICAgICBsb2dzRGlhbG9nVmlzaWJsZTogZmFsc2UsCiAgICAgIGNvbnRhaW5lckxvZ3M6ICcnLAogICAgICBjdXJyZW50Q29udGFpbmVyOiBudWxsLAogICAgICBkZXBsb3lEaWFsb2dWaXNpYmxlOiBmYWxzZSwKICAgICAgZGVwbG95Rm9ybTogewogICAgICAgIGNvbnRhaW5lck5hbWU6ICcnLAogICAgICAgIGltYWdlSWQ6IG51bGwsCiAgICAgICAgaW1hZ2VOYW1lOiAnJywKICAgICAgICBpbWFnZVRhZzogJ2xhdGVzdCcsCiAgICAgICAgc2VydmljZVBvcnQ6IDgwLAogICAgICAgIHJlcGxpY2FzOiAxLAogICAgICAgIGRpc3RyaWJ1dGlvblN0cmF0ZWd5OiAnU1BSRUFEX0FDUk9TU19OT0RFUycsCiAgICAgICAgZW5hYmxlVHJhZWZpazogdHJ1ZSwKICAgICAgICBlbmFibGVIc206IGZhbHNlLAogICAgICAgIGhzbURldmljZUlkOiBudWxsLAogICAgICAgIGRlcGxveWVkQnk6IG51bGwsCiAgICAgICAgYXBwbGljYXRpb25JZDogbnVsbCwKICAgICAgICBhcHBsaWNhdGlvbk5hbWU6ICcnCiAgICAgIH0sCiAgICAgIGRlcGxveVJ1bGVzOiB7CiAgICAgICAgYXBwbGljYXRpb25JZDogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogJ+ivt+mAieaLqeWFs+iBlOW6lOeUqCcsCiAgICAgICAgICB0cmlnZ2VyOiAnY2hhbmdlJwogICAgICAgIH1dLAogICAgICAgIGNvbnRhaW5lck5hbWU6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICfor7fovpPlhaXlrrnlmajlkI3np7AnLAogICAgICAgICAgdHJpZ2dlcjogJ2JsdXInCiAgICAgICAgfV0sCiAgICAgICAgaW1hZ2VJZDogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogJ+ivt+mAieaLqemVnOWDjycsCiAgICAgICAgICB0cmlnZ2VyOiAnY2hhbmdlJwogICAgICAgIH1dLAogICAgICAgIHNlcnZpY2VQb3J0OiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAn6K+36L6T5YWl5pyN5Yqh56uv5Y+jJywKICAgICAgICAgIHRyaWdnZXI6ICdibHVyJwogICAgICAgIH1dCiAgICAgIH0sCiAgICAgIHNjYWxlRGlhbG9nVmlzaWJsZTogZmFsc2UsCiAgICAgIHNjYWxlRm9ybTogewogICAgICAgIGlkOiAnJywKICAgICAgICByZXBsaWNhczogMSwKICAgICAgICBkaXN0cmlidXRpb25TdHJhdGVneTogJ1NQUkVBRF9BQ1JPU1NfTk9ERVMnCiAgICAgIH0sCiAgICAgIHNjYWxlUnVsZXM6IHsKICAgICAgICByZXBsaWNhczogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogJ+ivt+i+k+WFpeWJr+acrOaVsOmHjycsCiAgICAgICAgICB0cmlnZ2VyOiAnYmx1cicKICAgICAgICB9XQogICAgICB9LAogICAgICAvLyDplZzlg4/nm7jlhbPmlbDmja4KICAgICAgaW1hZ2VMaXN0OiBbXSwKICAgICAgaW1hZ2VMaXN0TG9hZGluZzogZmFsc2UsCiAgICAgIHNlbGVjdGVkSW1hZ2U6IG51bGwsCiAgICAgIC8vIEhTTeiuvuWkh+ebuOWFs+aVsOaNrgogICAgICBoc21EZXZpY2VMaXN0OiBbXSwKICAgICAgaHNtRGV2aWNlTGlzdExvYWRpbmc6IGZhbHNlLAogICAgICBzZWxlY3RlZEhzbURldmljZTogbnVsbCwKICAgICAgLy8g5bqU55So5LiL5ouJCiAgICAgIGFwcE9wdGlvbnM6IFtdLAogICAgICBhcHBPcHRpb25zTG9hZGluZzogZmFsc2UKICAgIH07CiAgfSwKICBtZXRob2RzOiB7CiAgICBoYW5kbGVTZWFyY2g6IGZ1bmN0aW9uIGhhbmRsZVNlYXJjaCgpIHsKICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICB9LAogICAgaGFuZGxlUmVzZXRTZWFyY2g6IGZ1bmN0aW9uIGhhbmRsZVJlc2V0U2VhcmNoKCkgewogICAgICB0aGlzLmxpc3RRdWVyeSA9IHsKICAgICAgICBwYWdlOiAxLAogICAgICAgIHBhZ2VTaXplOiAyMCwKICAgICAgICBjb250YWluZXJOYW1lOiB1bmRlZmluZWQsCiAgICAgICAgc3RhdHVzOiB1bmRlZmluZWQKICAgICAgfTsKICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICB9LAogICAgaGFuZGxlRGVwbG95Q29udGFpbmVyOiBmdW5jdGlvbiBoYW5kbGVEZXBsb3lDb250YWluZXIoKSB7CiAgICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICAgIC8vIOmmluWFiOWKoOi9vemVnOWDj+WIl+ihqAogICAgICBQcm9taXNlLmFsbChbdGhpcy5sb2FkSW1hZ2VMaXN0KCksIHRoaXMubG9hZEFwcE9wdGlvbnMoKV0pLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzLmRlcGxveUZvcm0gPSB7CiAgICAgICAgICBjb250YWluZXJOYW1lOiAnJywKICAgICAgICAgIGltYWdlSWQ6IG51bGwsCiAgICAgICAgICBpbWFnZU5hbWU6ICcnLAogICAgICAgICAgaW1hZ2VUYWc6ICdsYXRlc3QnLAogICAgICAgICAgc2VydmljZVBvcnQ6IDgwLAogICAgICAgICAgcmVwbGljYXM6IDEsCiAgICAgICAgICBkaXN0cmlidXRpb25TdHJhdGVneTogJ1NQUkVBRF9BQ1JPU1NfTk9ERVMnLAogICAgICAgICAgZW5hYmxlVHJhZWZpazogdHJ1ZSwKICAgICAgICAgIGVuYWJsZUhzbTogZmFsc2UsCiAgICAgICAgICBoc21EZXZpY2VJZDogbnVsbCwKICAgICAgICAgIGRlcGxveWVkQnk6IF90aGlzLnVzZXIgPyBfdGhpcy51c2VyLmlkIDogbnVsbCwKICAgICAgICAgIGFwcGxpY2F0aW9uSWQ6IG51bGwsCiAgICAgICAgICBhcHBsaWNhdGlvbk5hbWU6ICcnCiAgICAgICAgfTsKICAgICAgICBfdGhpcy5zZWxlY3RlZEltYWdlID0gbnVsbDsKICAgICAgICBfdGhpcy5zZWxlY3RlZEhzbURldmljZSA9IG51bGw7CiAgICAgICAgX3RoaXMuZGVwbG95RGlhbG9nVmlzaWJsZSA9IHRydWU7CiAgICAgICAgX3RoaXMuJG5leHRUaWNrKGZ1bmN0aW9uICgpIHsKICAgICAgICAgIF90aGlzLiRyZWZzWydkZXBsb3lGb3JtJ10uY2xlYXJWYWxpZGF0ZSgpOwogICAgICAgIH0pOwogICAgICB9KTsKICAgIH0sCiAgICBjb25maXJtRGVwbG95Q29udGFpbmVyOiBmdW5jdGlvbiBjb25maXJtRGVwbG95Q29udGFpbmVyKCkgewogICAgICB2YXIgX3RoaXMyID0gdGhpczsKICAgICAgdGhpcy4kcmVmc1snZGVwbG95Rm9ybSddLnZhbGlkYXRlKGZ1bmN0aW9uICh2YWxpZCkgewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgLy8g5qOA5p+lSFNN6K6+5aSH6YWN572uCiAgICAgICAgICBpZiAoX3RoaXMyLmRlcGxveUZvcm0uZW5hYmxlSHNtICYmICFfdGhpczIuZGVwbG95Rm9ybS5oc21EZXZpY2VJZCkgewogICAgICAgICAgICBfdGhpczIuJG1lc3NhZ2UuZXJyb3IoJ+ivt+mAieaLqUhTTeiuvuWkhycpOwogICAgICAgICAgICByZXR1cm47CiAgICAgICAgICB9CiAgICAgICAgICB2YXIgZGVwbG95RGF0YSA9IF9vYmplY3RTcHJlYWQoe30sIF90aGlzMi5kZXBsb3lGb3JtKTsKCiAgICAgICAgICAvLyDnoa7kv53plZzlg4/kv6Hmga/lrozmlbQKICAgICAgICAgIGlmIChfdGhpczIuc2VsZWN0ZWRJbWFnZSkgewogICAgICAgICAgICBkZXBsb3lEYXRhLmltYWdlTmFtZSA9IF90aGlzMi5zZWxlY3RlZEltYWdlLmltYWdlTmFtZTsKICAgICAgICAgICAgZGVwbG95RGF0YS5pbWFnZVRhZyA9IF90aGlzMi5zZWxlY3RlZEltYWdlLmltYWdlVGFnOwogICAgICAgICAgfQoKICAgICAgICAgIC8vIOaehOW7ukhTTeiuvuWkh+mFjee9rgogICAgICAgICAgaWYgKF90aGlzMi5kZXBsb3lGb3JtLmVuYWJsZUhzbSAmJiBfdGhpczIuc2VsZWN0ZWRIc21EZXZpY2UpIHsKICAgICAgICAgICAgZGVwbG95RGF0YS5oc21EZXZpY2VDb25maWcgPSB7CiAgICAgICAgICAgICAgZW5jcnlwdG9yR3JvdXBJZDogMSwKICAgICAgICAgICAgICAvLyDpu5jorqTnu4RJRAogICAgICAgICAgICAgIGVuY3J5cHRvcklkOiBfdGhpczIuc2VsZWN0ZWRIc21EZXZpY2UuZGV2aWNlSWQsCiAgICAgICAgICAgICAgZW5jcnlwdG9yTmFtZTogX3RoaXMyLnNlbGVjdGVkSHNtRGV2aWNlLmRldmljZU5hbWUsCiAgICAgICAgICAgICAgc2VydmVySXBBZGRyOiBfdGhpczIuc2VsZWN0ZWRIc21EZXZpY2UuaXBBZGRyZXNzLAogICAgICAgICAgICAgIHNlcnZlclBvcnQ6IF90aGlzMi5zZWxlY3RlZEhzbURldmljZS5zZXJ2aWNlUG9ydCwKICAgICAgICAgICAgICB0Y3BDb25uTnVtOiBfdGhpczIuc2VsZWN0ZWRIc21EZXZpY2UudGNwQ29ubk51bSB8fCA1LAogICAgICAgICAgICAgIG1zZ0hlYWRMZW46IF90aGlzMi5zZWxlY3RlZEhzbURldmljZS5tc2dIZWFkTGVuIHx8IDQsCiAgICAgICAgICAgICAgbXNnVGFpbExlbjogX3RoaXMyLnNlbGVjdGVkSHNtRGV2aWNlLm1zZ1RhaWxMZW4gfHwgMCwKICAgICAgICAgICAgICBhc2NpaU9yRWJjZGljOiBfdGhpczIuc2VsZWN0ZWRIc21EZXZpY2UuZW5jb2RpbmcgfHwgMCwKICAgICAgICAgICAgICBkeW5hbWljTGliUGF0aDogJy4vbGliZGV2aWNlYXBpLnNvJwogICAgICAgICAgICB9OwogICAgICAgICAgfQoKICAgICAgICAgIC8vIOagueaNrumFjee9rumAieaLqemDqOe9suaOpeWPowogICAgICAgICAgdmFyIGRlcGxveUZuOwogICAgICAgICAgaWYgKF90aGlzMi5kZXBsb3lGb3JtLmVuYWJsZUhzbSkgewogICAgICAgICAgICAvLyDkvb/nlKjluKZIU03phY3nva7nmoTpg6jnvbLmjqXlj6MKICAgICAgICAgICAgY29uc29sZS5sb2coJ+S9v+eUqOW4pkhTTemFjee9rueahOmDqOe9suaOpeWPoycpOwogICAgICAgICAgICBkZXBsb3lGbiA9IF90aGlzMi5kZXBsb3lXaXRoSHNtOwogICAgICAgICAgfSBlbHNlIGlmIChkZXBsb3lEYXRhLnJlcGxpY2FzID4gMSAmJiBkZXBsb3lEYXRhLmRpc3RyaWJ1dGlvblN0cmF0ZWd5KSB7CiAgICAgICAgICAgIC8vIOS9v+eUqOW4puWIhuW4g+etlueVpeeahOmDqOe9suaOpeWPowogICAgICAgICAgICBjb25zb2xlLmxvZygn5L2/55So5bim5YiG5biD562W55Wl55qE6YOo572y5o6l5Y+jJyk7CiAgICAgICAgICAgIGRlcGxveUZuID0gZGVwbG95Q29udGFpbmVyV2l0aFN0cmF0ZWd5OwogICAgICAgICAgfSBlbHNlIGlmIChkZXBsb3lEYXRhLmVuYWJsZVRyYWVmaWspIHsKICAgICAgICAgICAgLy8g5L2/55So5bimVHJhZWZpa+eahOmDqOe9suaOpeWPowogICAgICAgICAgICBjb25zb2xlLmxvZygn5L2/55So5bimVHJhZWZpa+eahOmDqOe9suaOpeWPoycpOwogICAgICAgICAgICBkZXBsb3lGbiA9IF90aGlzMi5kZXBsb3lXaXRoVHJhZWZpazsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIC8vIOS9v+eUqOWfuuacrOmDqOe9suaOpeWPowogICAgICAgICAgICBjb25zb2xlLmxvZygn5L2/55So5Z+65pys6YOo572y5o6l5Y+jJyk7CiAgICAgICAgICAgIGRlcGxveUZuID0gZGVwbG95Q29udGFpbmVyOwogICAgICAgICAgfQogICAgICAgICAgZGVwbG95Rm4oZGVwbG95RGF0YSkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICAgICAgX3RoaXMyLmRlcGxveURpYWxvZ1Zpc2libGUgPSBmYWxzZTsKICAgICAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMDAwKSB7CiAgICAgICAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEpIHsKICAgICAgICAgICAgICAgIC8vIOWkhOeQhuS4jeWQjOeahOWTjeW6lOe7k+aehAogICAgICAgICAgICAgICAgdmFyIG1lc3NhZ2UgPSAn5a655Zmo6YOo572y5oiQ5Yqf77yBJzsKICAgICAgICAgICAgICAgIGlmIChyZXNwb25zZS5kYXRhLmFjY2Vzc1VybCkgewogICAgICAgICAgICAgICAgICBtZXNzYWdlICs9ICJcdThCQkZcdTk1RUVcdTU3MzBcdTU3NDBcdUZGMUEiLmNvbmNhdChyZXNwb25zZS5kYXRhLmFjY2Vzc1VybCk7CiAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKHJlc3BvbnNlLmRhdGEuY29udGFpbmVyICYmIHJlc3BvbnNlLmRhdGEuY29udGFpbmVyLmFjY2Vzc1VybCkgewogICAgICAgICAgICAgICAgICBtZXNzYWdlICs9ICJcdThCQkZcdTk1RUVcdTU3MzBcdTU3NDBcdUZGMUEiLmNvbmNhdChyZXNwb25zZS5kYXRhLmNvbnRhaW5lci5hY2Nlc3NVcmwpOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEuZGlzdHJpYnV0aW9uU3RyYXRlZ3kpIHsKICAgICAgICAgICAgICAgICAgbWVzc2FnZSArPSAiXHVGRjBDXHU1MjA2XHU1RTAzXHU3QjU2XHU3NTY1XHVGRjFBIi5jb25jYXQoX3RoaXMyLmdldFN0cmF0ZWd5RGlzcGxheU5hbWUocmVzcG9uc2UuZGF0YS5kaXN0cmlidXRpb25TdHJhdGVneSkpOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEuaHNtQ29uZmlndXJlZCkgewogICAgICAgICAgICAgICAgICBtZXNzYWdlICs9ICJcdUZGMENIU01cdThCQkVcdTU5MDdcdUZGMUEiLmNvbmNhdChfdGhpczIuc2VsZWN0ZWRIc21EZXZpY2UuZGV2aWNlTmFtZSk7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICBfdGhpczIuJG1lc3NhZ2Uuc3VjY2VzcyhtZXNzYWdlKTsKICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgX3RoaXMyLiRtZXNzYWdlLnN1Y2Nlc3MoJ+WuueWZqOmDqOe9suS7u+WKoeW3suaPkOS6pCcpOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBfdGhpczIuZ2V0TGlzdCgpOwogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgIF90aGlzMi4kbWVzc2FnZS5lcnJvcihyZXNwb25zZS5tZXNzYWdlIHx8ICfpg6jnvbLlpLHotKUnKTsKICAgICAgICAgICAgfQogICAgICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKCkgewogICAgICAgICAgICAvLyDplJnor6/lpITnkIblt7LlnKjmi6bmiKrlmajkuK3lpITnkIYKICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLy8g5Yqg6L295bqU55So6YCJ6aG5CiAgICBsb2FkQXBwT3B0aW9uczogZnVuY3Rpb24gbG9hZEFwcE9wdGlvbnMoKSB7CiAgICAgIHZhciBfdGhpczMgPSB0aGlzOwogICAgICByZXR1cm4gX2FzeW5jVG9HZW5lcmF0b3IoLyojX19QVVJFX18qL19yZWdlbmVyYXRvcigpLm0oZnVuY3Rpb24gX2NhbGxlZSgpIHsKICAgICAgICB2YXIgcmVzcCwgZGF0YSwgX3Q7CiAgICAgICAgcmV0dXJuIF9yZWdlbmVyYXRvcigpLncoZnVuY3Rpb24gKF9jb250ZXh0KSB7CiAgICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dC5uKSB7CiAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICBfdGhpczMuYXBwT3B0aW9uc0xvYWRpbmcgPSB0cnVlOwogICAgICAgICAgICAgIF9jb250ZXh0LnAgPSAxOwogICAgICAgICAgICAgIGlmICghKF90aGlzMy51c2VyICYmIEFycmF5LmlzQXJyYXkoX3RoaXMzLnVzZXIucm9sZXMpICYmIF90aGlzMy51c2VyLnJvbGVzLmluY2x1ZGVzKCdST0xFX1RFTkFOVCcpKSkgewogICAgICAgICAgICAgICAgX2NvbnRleHQubiA9IDM7CiAgICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgX2NvbnRleHQubiA9IDI7CiAgICAgICAgICAgICAgcmV0dXJuIGZldGNoVXNlckFwcHMoKTsKICAgICAgICAgICAgY2FzZSAyOgogICAgICAgICAgICAgIHJlc3AgPSBfY29udGV4dC52OwogICAgICAgICAgICAgIF9jb250ZXh0Lm4gPSA1OwogICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICBjYXNlIDM6CiAgICAgICAgICAgICAgX2NvbnRleHQubiA9IDQ7CiAgICAgICAgICAgICAgcmV0dXJuIGZldGNoQXBwbGljYXRpb25PcHRpb25zKCk7CiAgICAgICAgICAgIGNhc2UgNDoKICAgICAgICAgICAgICByZXNwID0gX2NvbnRleHQudjsKICAgICAgICAgICAgY2FzZSA1OgogICAgICAgICAgICAgIGlmIChyZXNwICYmIHJlc3AuY29kZSA9PT0gMjAwMDApIHsKICAgICAgICAgICAgICAgIGRhdGEgPSBBcnJheS5pc0FycmF5KHJlc3AuZGF0YSkgPyByZXNwLmRhdGEgOiByZXNwLmRhdGEgJiYgcmVzcC5kYXRhLmxpc3QgPyByZXNwLmRhdGEubGlzdCA6IFtdOwogICAgICAgICAgICAgICAgX3RoaXMzLmFwcE9wdGlvbnMgPSBkYXRhLm1hcChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgICAgICAgICBpZiAoaXRlbS5sYWJlbCAmJiBpdGVtLnZhbHVlICE9PSB1bmRlZmluZWQpIHJldHVybiBpdGVtOwogICAgICAgICAgICAgICAgICByZXR1cm4gewogICAgICAgICAgICAgICAgICAgIGxhYmVsOiBpdGVtLm5hbWUgfHwgaXRlbS5hcHBsaWNhdGlvbk5hbWUgfHwgaXRlbS5sYWJlbCwKICAgICAgICAgICAgICAgICAgICB2YWx1ZTogU3RyaW5nKGl0ZW0uaWQgfHwgaXRlbS52YWx1ZSkKICAgICAgICAgICAgICAgICAgfTsKICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICBfdGhpczMuYXBwT3B0aW9ucyA9IFtdOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBfY29udGV4dC5uID0gNzsKICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgY2FzZSA2OgogICAgICAgICAgICAgIF9jb250ZXh0LnAgPSA2OwogICAgICAgICAgICAgIF90ID0gX2NvbnRleHQudjsKICAgICAgICAgICAgICBfdGhpczMuYXBwT3B0aW9ucyA9IFtdOwogICAgICAgICAgICBjYXNlIDc6CiAgICAgICAgICAgICAgX2NvbnRleHQucCA9IDc7CiAgICAgICAgICAgICAgX3RoaXMzLmFwcE9wdGlvbnNMb2FkaW5nID0gZmFsc2U7CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0LmYoNyk7CiAgICAgICAgICAgIGNhc2UgODoKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQuYSgyKTsKICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlLCBudWxsLCBbWzEsIDYsIDcsIDhdXSk7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIGhhbmRsZVN0YXJ0Q29udGFpbmVyOiBmdW5jdGlvbiBoYW5kbGVTdGFydENvbnRhaW5lcihyb3cpIHsKICAgICAgdmFyIF90aGlzNCA9IHRoaXM7CiAgICAgIHN0YXJ0Q29udGFpbmVyKHJvdy5pZCkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgX3RoaXM0LiRtZXNzYWdlLnN1Y2Nlc3MoJ+WuueWZqOWQr+WKqOaIkOWKnycpOwogICAgICAgIF90aGlzNC5nZXRMaXN0KCk7CiAgICAgIH0pOwogICAgfSwKICAgIGhhbmRsZVN0b3BDb250YWluZXI6IGZ1bmN0aW9uIGhhbmRsZVN0b3BDb250YWluZXIocm93KSB7CiAgICAgIHZhciBfdGhpczUgPSB0aGlzOwogICAgICBzdG9wQ29udGFpbmVyKHJvdy5pZCkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgX3RoaXM1LiRtZXNzYWdlLnN1Y2Nlc3MoJ+WuueWZqOWBnOatouaIkOWKnycpOwogICAgICAgIF90aGlzNS5nZXRMaXN0KCk7CiAgICAgIH0pOwogICAgfSwKICAgIGhhbmRsZVJlc3RhcnRDb250YWluZXI6IGZ1bmN0aW9uIGhhbmRsZVJlc3RhcnRDb250YWluZXIocm93KSB7CiAgICAgIHZhciBfdGhpczYgPSB0aGlzOwogICAgICByZXN0YXJ0Q29udGFpbmVyKHJvdy5pZCkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgX3RoaXM2LiRtZXNzYWdlLnN1Y2Nlc3MoJ+WuueWZqOmHjeWQr+aIkOWKnycpOwogICAgICAgIF90aGlzNi5nZXRMaXN0KCk7CiAgICAgIH0pOwogICAgfSwKICAgIGhhbmRsZVJlbW92ZUNvbnRhaW5lcjogZnVuY3Rpb24gaGFuZGxlUmVtb3ZlQ29udGFpbmVyKHJvdykgewogICAgICB2YXIgX3RoaXM3ID0gdGhpczsKICAgICAgdGhpcy4kbXNnYm94LmNvbmZpcm0oIlx1Nzg2RVx1OEJBNFx1ODk4MVx1NTIyMFx1OTY2NFx1NUJCOVx1NTY2OCAiLmNvbmNhdChyb3cuY29udGFpbmVyTmFtZSwgIiBcdTU0MTdcdUZGMUYiKSwgJ+aPkOekuicsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsCiAgICAgICAgdHlwZTogJ3dhcm5pbmcnCiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgIHJlbW92ZUNvbnRhaW5lcihyb3cuaWQpLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgICAgX3RoaXM3LiRtZXNzYWdlLnN1Y2Nlc3MoJ+WIoOmZpOaIkOWKnycpOwogICAgICAgICAgX3RoaXM3LmdldExpc3QoKTsKICAgICAgICB9KTsKICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKCkgewogICAgICAgIC8vIOWPlua2iOWIoOmZpAogICAgICB9KTsKICAgIH0sCiAgICBoYW5kbGVTY2FsZUNvbnRhaW5lcjogZnVuY3Rpb24gaGFuZGxlU2NhbGVDb250YWluZXIocm93KSB7CiAgICAgIHZhciBfdGhpczggPSB0aGlzOwogICAgICB0aGlzLnNjYWxlRm9ybSA9IHsKICAgICAgICBpZDogcm93LmlkLAogICAgICAgIHJlcGxpY2FzOiByb3cucmVwbGljYXMgfHwgMSwKICAgICAgICAvLyDkvb/nlKjlvZPliY3lia/mnKzmlbDmiJbpu5jorqTlgLwKICAgICAgICBkaXN0cmlidXRpb25TdHJhdGVneTogcm93LmRpc3RyaWJ1dGlvblN0cmF0ZWd5IHx8ICdTUFJFQURfQUNST1NTX05PREVTJwogICAgICB9OwogICAgICB0aGlzLnNjYWxlRGlhbG9nVmlzaWJsZSA9IHRydWU7CiAgICAgIHRoaXMuJG5leHRUaWNrKGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpczguJHJlZnNbJ3NjYWxlRm9ybSddLmNsZWFyVmFsaWRhdGUoKTsKICAgICAgfSk7CiAgICB9LAogICAgY29uZmlybVNjYWxlQ29udGFpbmVyOiBmdW5jdGlvbiBjb25maXJtU2NhbGVDb250YWluZXIoKSB7CiAgICAgIHZhciBfdGhpczkgPSB0aGlzOwogICAgICB0aGlzLiRyZWZzWydzY2FsZUZvcm0nXS52YWxpZGF0ZShmdW5jdGlvbiAodmFsaWQpIHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIHNjYWxlQ29udGFpbmVyKF90aGlzOS5zY2FsZUZvcm0uaWQsIF90aGlzOS5zY2FsZUZvcm0ucmVwbGljYXMpLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgICAgICBfdGhpczkuc2NhbGVEaWFsb2dWaXNpYmxlID0gZmFsc2U7CiAgICAgICAgICAgIF90aGlzOS4kbWVzc2FnZS5zdWNjZXNzKCflrrnlmajmiannvKnlrrnku7vliqHlt7Lmj5DkuqQnKTsKICAgICAgICAgICAgX3RoaXM5LmdldExpc3QoKTsKICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgaGFuZGxlVmlld0xvZ3M6IGZ1bmN0aW9uIGhhbmRsZVZpZXdMb2dzKHJvdykgewogICAgICB2YXIgX3RoaXMwID0gdGhpczsKICAgICAgdGhpcy5jdXJyZW50Q29udGFpbmVyID0gcm93OwogICAgICBnZXRDb250YWluZXJMb2dzKHJvdy5pZCkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwMDApIHsKICAgICAgICAgIF90aGlzMC5jb250YWluZXJMb2dzID0gcmVzcG9uc2UuZGF0YSB8fCAnJzsKICAgICAgICAgIF90aGlzMC5sb2dzRGlhbG9nVmlzaWJsZSA9IHRydWU7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICBoYW5kbGVTeW5jU3RhdHVzOiBmdW5jdGlvbiBoYW5kbGVTeW5jU3RhdHVzKCkgewogICAgICB2YXIgX3RoaXMxID0gdGhpczsKICAgICAgc3luY0NvbnRhaW5lclN0YXR1cygpLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzMS4kbWVzc2FnZS5zdWNjZXNzKCflrrnlmajnirbmgIHlkIzmraXku7vliqHlt7Lmj5DkuqQnKTsKICAgICAgICBfdGhpczEuZ2V0TGlzdCgpOwogICAgICB9KTsKICAgIH0sCiAgICBnZXRMaXN0OiBmdW5jdGlvbiBnZXRMaXN0KCkgewogICAgICB2YXIgX3RoaXMxMCA9IHRoaXM7CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgIGdldENvbnRhaW5lckxpc3QodGhpcy5saXN0UXVlcnkpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMDAwKSB7CiAgICAgICAgICBfdGhpczEwLnRhYmxlTGlzdCA9IHJlc3BvbnNlLmRhdGEubGlzdDsKICAgICAgICAgIF90aGlzMTAudG90YWwgPSByZXNwb25zZS5kYXRhLnRvdGFsQ291bnQ7CiAgICAgICAgfQogICAgICAgIF90aGlzMTAubG9hZGluZyA9IGZhbHNlOwogICAgICB9KS5jYXRjaChmdW5jdGlvbiAoKSB7CiAgICAgICAgX3RoaXMxMC5sb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOagvOW8j+WMluerr+WPo+aYoOWwhOaYvuekugogICAgZm9ybWF0UG9ydHM6IGZ1bmN0aW9uIGZvcm1hdFBvcnRzKHBvcnRNYXBwaW5ncykgewogICAgICBpZiAoIXBvcnRNYXBwaW5ncykgcmV0dXJuICctJzsKICAgICAgdHJ5IHsKICAgICAgICB2YXIgcG9ydHMgPSBKU09OLnBhcnNlKHBvcnRNYXBwaW5ncyk7CiAgICAgICAgaWYgKEFycmF5LmlzQXJyYXkocG9ydHMpICYmIHBvcnRzLmxlbmd0aCA+IDApIHsKICAgICAgICAgIHJldHVybiBwb3J0cy5tYXAoZnVuY3Rpb24gKHApIHsKICAgICAgICAgICAgcmV0dXJuICIiLmNvbmNhdChwLmhvc3RQb3J0IHx8ICcnLCAiOiIpLmNvbmNhdChwLmNvbnRhaW5lclBvcnQsICIvIikuY29uY2F0KHAucHJvdG9jb2wgfHwgJ3RjcCcpOwogICAgICAgICAgfSkuam9pbignLCAnKTsKICAgICAgICB9CiAgICAgICAgcmV0dXJuICctJzsKICAgICAgfSBjYXRjaCAoZSkgewogICAgICAgIHJldHVybiBwb3J0TWFwcGluZ3M7CiAgICAgIH0KICAgIH0sCiAgICAvLyDmoLzlvI/ljJbml6XmnJ/mmL7npLoKICAgIGZvcm1hdERhdGU6IGZ1bmN0aW9uIGZvcm1hdERhdGUoZGF0ZVRpbWUpIHsKICAgICAgaWYgKCFkYXRlVGltZSkgcmV0dXJuICctJzsKICAgICAgcmV0dXJuIG5ldyBEYXRlKGRhdGVUaW1lKS50b0xvY2FsZVN0cmluZygnemgtQ04nKTsKICAgIH0sCiAgICAvLyDlpI3liLbliLDliarotLTmnb8KICAgIGNvcHlUb0NsaXBib2FyZDogZnVuY3Rpb24gY29weVRvQ2xpcGJvYXJkKHRleHQpIHsKICAgICAgdmFyIF90aGlzMTEgPSB0aGlzOwogICAgICBpZiAobmF2aWdhdG9yLmNsaXBib2FyZCkgewogICAgICAgIG5hdmlnYXRvci5jbGlwYm9hcmQud3JpdGVUZXh0KHRleHQpLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgICAgX3RoaXMxMS4kbWVzc2FnZS5zdWNjZXNzKCflnLDlnYDlt7LlpI3liLbliLDliarotLTmnb8nKTsKICAgICAgICB9KTsKICAgICAgfSBlbHNlIHsKICAgICAgICAvLyDpmY3nuqfmlrnmoYgKICAgICAgICB2YXIgdGV4dEFyZWEgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCd0ZXh0YXJlYScpOwogICAgICAgIHRleHRBcmVhLnZhbHVlID0gdGV4dDsKICAgICAgICBkb2N1bWVudC5ib2R5LmFwcGVuZENoaWxkKHRleHRBcmVhKTsKICAgICAgICB0ZXh0QXJlYS5zZWxlY3QoKTsKICAgICAgICBkb2N1bWVudC5leGVjQ29tbWFuZCgnY29weScpOwogICAgICAgIGRvY3VtZW50LmJvZHkucmVtb3ZlQ2hpbGQodGV4dEFyZWEpOwogICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5Zyw5Z2A5bey5aSN5Yi25Yiw5Ymq6LS05p2/Jyk7CiAgICAgIH0KICAgIH0sCiAgICAvLyDphY3nva7orr/pl67ot6/nlLEKICAgIGhhbmRsZUNvbmZpZ1JvdXRlOiBmdW5jdGlvbiBoYW5kbGVDb25maWdSb3V0ZShyb3cpIHsKICAgICAgdmFyIF90aGlzMTIgPSB0aGlzOwogICAgICB0aGlzLiRwcm9tcHQoJ+ivt+i+k+WFpeacjeWKoeerr+WPo++8iOWuueWZqOWGhemDqOerr+WPo++8iScsICfphY3nva7orr/pl67ot6/nlLEnLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLAogICAgICAgIGlucHV0VmFsdWU6ICc4MCcsCiAgICAgICAgaW5wdXRWYWxpZGF0b3I6IGZ1bmN0aW9uIGlucHV0VmFsaWRhdG9yKHZhbHVlKSB7CiAgICAgICAgICB2YXIgcG9ydCA9IHBhcnNlSW50KHZhbHVlKTsKICAgICAgICAgIGlmICghcG9ydCB8fCBwb3J0IDwgMSB8fCBwb3J0ID4gNjU1MzUpIHsKICAgICAgICAgICAgcmV0dXJuICfor7fovpPlhaXmnInmlYjnmoTnq6/lj6Plj7coMS02NTUzNSknOwogICAgICAgICAgfQogICAgICAgICAgcmV0dXJuIHRydWU7CiAgICAgICAgfQogICAgICB9KS50aGVuKGZ1bmN0aW9uIChfcmVmKSB7CiAgICAgICAgdmFyIHZhbHVlID0gX3JlZi52YWx1ZTsKICAgICAgICB2YXIgcm91dGVDb25maWcgPSB7CiAgICAgICAgICBjb250YWluZXJJZDogcm93LmlkLAogICAgICAgICAgc2VydmljZVBvcnQ6IHBhcnNlSW50KHZhbHVlKSwKICAgICAgICAgIGVuYWJsZVRyYWVmaWs6IHRydWUKICAgICAgICB9OwogICAgICAgIF90aGlzMTIuY29uZmlnQ29udGFpbmVyUm91dGUocm91dGVDb25maWcpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwMDAgJiYgcmVzcG9uc2UuZGF0YSAmJiByZXNwb25zZS5kYXRhLmFjY2Vzc1VybCkgewogICAgICAgICAgICBfdGhpczEyLiRtZXNzYWdlLnN1Y2Nlc3MoIlx1OERFRlx1NzUzMVx1OTE0RFx1N0Y2RVx1NjIxMFx1NTI5Rlx1RkYwMVx1OEJCRlx1OTVFRVx1NTczMFx1NTc0MFx1RkYxQSIuY29uY2F0KHJlc3BvbnNlLmRhdGEuYWNjZXNzVXJsKSk7CiAgICAgICAgICAgIF90aGlzMTIuZ2V0TGlzdCgpOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgX3RoaXMxMi4kbWVzc2FnZS5lcnJvcihyZXNwb25zZS5tZXNzYWdlIHx8ICfphY3nva7lpLHotKUnKTsKICAgICAgICAgIH0KICAgICAgICB9KS5jYXRjaChmdW5jdGlvbiAoKSB7CiAgICAgICAgICAvLyDplJnor6/lpITnkIblt7LlnKjmi6bmiKrlmajkuK3lpITnkIYKICAgICAgICB9KTsKICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKCkgewogICAgICAgIC8vIOWPlua2iOaTjeS9nAogICAgICB9KTsKICAgIH0sCiAgICAvLyDmm7TmlrDot6/nlLHphY3nva4KICAgIGhhbmRsZVVwZGF0ZVJvdXRlOiBmdW5jdGlvbiBoYW5kbGVVcGRhdGVSb3V0ZShyb3cpIHsKICAgICAgdmFyIF90aGlzMTMgPSB0aGlzOwogICAgICB0aGlzLiRwcm9tcHQoJ+ivt+i+k+WFpeaWsOeahOacjeWKoeerr+WPoycsICfmm7TmlrDot6/nlLEnLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLAogICAgICAgIGlucHV0VmFsaWRhdG9yOiBmdW5jdGlvbiBpbnB1dFZhbGlkYXRvcih2YWx1ZSkgewogICAgICAgICAgdmFyIHBvcnQgPSBwYXJzZUludCh2YWx1ZSk7CiAgICAgICAgICBpZiAoIXBvcnQgfHwgcG9ydCA8IDEgfHwgcG9ydCA+IDY1NTM1KSB7CiAgICAgICAgICAgIHJldHVybiAn6K+36L6T5YWl5pyJ5pWI55qE56uv5Y+j5Y+3KDEtNjU1MzUpJzsKICAgICAgICAgIH0KICAgICAgICAgIHJldHVybiB0cnVlOwogICAgICAgIH0KICAgICAgfSkudGhlbihmdW5jdGlvbiAoX3JlZjIpIHsKICAgICAgICB2YXIgdmFsdWUgPSBfcmVmMi52YWx1ZTsKICAgICAgICB2YXIgcm91dGVDb25maWcgPSB7CiAgICAgICAgICBjb250YWluZXJJZDogcm93LmlkLAogICAgICAgICAgc2VydmljZVBvcnQ6IHBhcnNlSW50KHZhbHVlKQogICAgICAgIH07CiAgICAgICAgX3RoaXMxMy51cGRhdGVDb250YWluZXJSb3V0ZShyb3V0ZUNvbmZpZykudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDAwMCAmJiByZXNwb25zZS5kYXRhICYmIHJlc3BvbnNlLmRhdGEuYWNjZXNzVXJsKSB7CiAgICAgICAgICAgIF90aGlzMTMuJG1lc3NhZ2Uuc3VjY2VzcygiXHU4REVGXHU3NTMxXHU2NkY0XHU2NUIwXHU2MjEwXHU1MjlGXHVGRjAxXHU2NUIwXHU1NzMwXHU1NzQwXHVGRjFBIi5jb25jYXQocmVzcG9uc2UuZGF0YS5hY2Nlc3NVcmwpKTsKICAgICAgICAgICAgX3RoaXMxMy5nZXRMaXN0KCk7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICBfdGhpczEzLiRtZXNzYWdlLmVycm9yKHJlc3BvbnNlLm1lc3NhZ2UgfHwgJ+abtOaWsOWksei0pScpOwogICAgICAgICAgfQogICAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uICgpIHsKICAgICAgICAgIC8vIOmUmeivr+WkhOeQhuW3suWcqOaLpuaIquWZqOS4reWkhOeQhgogICAgICAgIH0pOwogICAgICB9KS5jYXRjaChmdW5jdGlvbiAoKSB7CiAgICAgICAgLy8g5Y+W5raI5pON5L2cCiAgICAgIH0pOwogICAgfSwKICAgIC8vIOW4plRyYWVmaWvnmoTpg6jnvbLmlrnms5UKICAgIGRlcGxveVdpdGhUcmFlZmlrOiBmdW5jdGlvbiBkZXBsb3lXaXRoVHJhZWZpayhkZXBsb3lEYXRhKSB7CiAgICAgIHJldHVybiBfYXN5bmNUb0dlbmVyYXRvcigvKiNfX1BVUkVfXyovX3JlZ2VuZXJhdG9yKCkubShmdW5jdGlvbiBfY2FsbGVlMigpIHsKICAgICAgICByZXR1cm4gX3JlZ2VuZXJhdG9yKCkudyhmdW5jdGlvbiAoX2NvbnRleHQyKSB7CiAgICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dDIubikgewogICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0Mi5hKDIsIGRlcGxveUNvbnRhaW5lcldpdGhUcmFlZmlrKGRlcGxveURhdGEpKTsKICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlMik7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIC8vIOmFjee9ruWuueWZqOi3r+eUseaWueazlQogICAgY29uZmlnQ29udGFpbmVyUm91dGU6IGZ1bmN0aW9uIGNvbmZpZ0NvbnRhaW5lclJvdXRlKHJvdXRlQ29uZmlnKSB7CiAgICAgIHJldHVybiBfYXN5bmNUb0dlbmVyYXRvcigvKiNfX1BVUkVfXyovX3JlZ2VuZXJhdG9yKCkubShmdW5jdGlvbiBfY2FsbGVlMygpIHsKICAgICAgICByZXR1cm4gX3JlZ2VuZXJhdG9yKCkudyhmdW5jdGlvbiAoX2NvbnRleHQzKSB7CiAgICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dDMubikgewogICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0My5hKDIsIF9jb25maWdDb250YWluZXJSb3V0ZShyb3V0ZUNvbmZpZykpOwogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWUzKTsKICAgICAgfSkpKCk7CiAgICB9LAogICAgLy8g5pu05paw5a655Zmo6Lev55Sx5pa55rOVCiAgICB1cGRhdGVDb250YWluZXJSb3V0ZTogZnVuY3Rpb24gdXBkYXRlQ29udGFpbmVyUm91dGUocm91dGVDb25maWcpIHsKICAgICAgcmV0dXJuIF9hc3luY1RvR2VuZXJhdG9yKC8qI19fUFVSRV9fKi9fcmVnZW5lcmF0b3IoKS5tKGZ1bmN0aW9uIF9jYWxsZWU0KCkgewogICAgICAgIHJldHVybiBfcmVnZW5lcmF0b3IoKS53KGZ1bmN0aW9uIChfY29udGV4dDQpIHsKICAgICAgICAgIHdoaWxlICgxKSBzd2l0Y2ggKF9jb250ZXh0NC5uKSB7CiAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQ0LmEoMiwgX3VwZGF0ZUNvbnRhaW5lclJvdXRlKHJvdXRlQ29uZmlnKSk7CiAgICAgICAgICB9CiAgICAgICAgfSwgX2NhbGxlZTQpOwogICAgICB9KSkoKTsKICAgIH0sCiAgICAvLyDliqDovb3plZzlg4/liJfooagKICAgIGxvYWRJbWFnZUxpc3Q6IGZ1bmN0aW9uIGxvYWRJbWFnZUxpc3QoKSB7CiAgICAgIHZhciBfdGhpczE0ID0gdGhpczsKICAgICAgcmV0dXJuIF9hc3luY1RvR2VuZXJhdG9yKC8qI19fUFVSRV9fKi9fcmVnZW5lcmF0b3IoKS5tKGZ1bmN0aW9uIF9jYWxsZWU1KCkgewogICAgICAgIHZhciByZXNwb25zZSwgX3QyOwogICAgICAgIHJldHVybiBfcmVnZW5lcmF0b3IoKS53KGZ1bmN0aW9uIChfY29udGV4dDUpIHsKICAgICAgICAgIHdoaWxlICgxKSBzd2l0Y2ggKF9jb250ZXh0NS5uKSB7CiAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICBfdGhpczE0LmltYWdlTGlzdExvYWRpbmcgPSB0cnVlOwogICAgICAgICAgICAgIF9jb250ZXh0NS5wID0gMTsKICAgICAgICAgICAgICBfY29udGV4dDUubiA9IDI7CiAgICAgICAgICAgICAgcmV0dXJuIGdldEltYWdlTGlzdCgpOwogICAgICAgICAgICBjYXNlIDI6CiAgICAgICAgICAgICAgcmVzcG9uc2UgPSBfY29udGV4dDUudjsKICAgICAgICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwMDApIHsKICAgICAgICAgICAgICAgIC8vIOWkhOeQhuWIhumhteaVsOaNrue7k+aehAogICAgICAgICAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEgJiYgcmVzcG9uc2UuZGF0YS5saXN0KSB7CiAgICAgICAgICAgICAgICAgIC8vIOWmguaenOi/lOWbnueahOaYr+WIhumhtee7k+aehAogICAgICAgICAgICAgICAgICBfdGhpczE0LmltYWdlTGlzdCA9IHJlc3BvbnNlLmRhdGEubGlzdC5tYXAoZnVuY3Rpb24gKGltYWdlKSB7CiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIF9vYmplY3RTcHJlYWQoX29iamVjdFNwcmVhZCh7fSwgaW1hZ2UpLCB7fSwgewogICAgICAgICAgICAgICAgICAgICAgLy8g56Gu5L+dIHNpemVNYiDmmK/mlbDlrZfnsbvlnosKICAgICAgICAgICAgICAgICAgICAgIHNpemVNYjogdHlwZW9mIGltYWdlLnNpemVNYiA9PT0gJ3N0cmluZycgPyBwYXJzZUludChpbWFnZS5zaXplTWIpIDogaW1hZ2Uuc2l6ZU1iCiAgICAgICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgfSBlbHNlIGlmIChBcnJheS5pc0FycmF5KHJlc3BvbnNlLmRhdGEpKSB7CiAgICAgICAgICAgICAgICAgIC8vIOWmguaenOi/lOWbnueahOaYr+ebtOaOpeaVsOe7hAogICAgICAgICAgICAgICAgICBfdGhpczE0LmltYWdlTGlzdCA9IHJlc3BvbnNlLmRhdGEubWFwKGZ1bmN0aW9uIChpbWFnZSkgewogICAgICAgICAgICAgICAgICAgIHJldHVybiBfb2JqZWN0U3ByZWFkKF9vYmplY3RTcHJlYWQoe30sIGltYWdlKSwge30sIHsKICAgICAgICAgICAgICAgICAgICAgIHNpemVNYjogdHlwZW9mIGltYWdlLnNpemVNYiA9PT0gJ3N0cmluZycgPyBwYXJzZUludChpbWFnZS5zaXplTWIpIDogaW1hZ2Uuc2l6ZU1iCiAgICAgICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgICAgX3RoaXMxNC5pbWFnZUxpc3QgPSBbXTsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIGlmIChfdGhpczE0LmltYWdlTGlzdC5sZW5ndGggPT09IDApIHsKICAgICAgICAgICAgICAgICAgX3RoaXMxNC4kbWVzc2FnZS5pbmZvKCflvZPliY3msqHmnInlj6/nlKjnmoTplZzlg4/vvIzor7flhYjmnoTlu7rmiJblr7zlhaXplZzlg48nKTsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgX3RoaXMxNC4kbWVzc2FnZS5lcnJvcign6I635Y+W6ZWc5YOP5YiX6KGo5aSx6LSl77yaJyArIChyZXNwb25zZS5tZXNzYWdlIHx8ICfmnKrnn6XplJnor68nKSk7CiAgICAgICAgICAgICAgICBfdGhpczE0LmltYWdlTGlzdCA9IFtdOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBfY29udGV4dDUubiA9IDQ7CiAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgIGNhc2UgMzoKICAgICAgICAgICAgICBfY29udGV4dDUucCA9IDM7CiAgICAgICAgICAgICAgX3QyID0gX2NvbnRleHQ1LnY7CiAgICAgICAgICAgICAgY29uc29sZS5lcnJvcign5Yqg6L296ZWc5YOP5YiX6KGo5aSx6LSlOicsIF90Mik7CiAgICAgICAgICAgICAgX3RoaXMxNC4kbWVzc2FnZS5lcnJvcign5Yqg6L296ZWc5YOP5YiX6KGo5aSx6LSlJyk7CiAgICAgICAgICAgICAgX3RoaXMxNC5pbWFnZUxpc3QgPSBbXTsKICAgICAgICAgICAgY2FzZSA0OgogICAgICAgICAgICAgIF9jb250ZXh0NS5wID0gNDsKICAgICAgICAgICAgICBfdGhpczE0LmltYWdlTGlzdExvYWRpbmcgPSBmYWxzZTsKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQ1LmYoNCk7CiAgICAgICAgICAgIGNhc2UgNToKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQ1LmEoMik7CiAgICAgICAgICB9CiAgICAgICAgfSwgX2NhbGxlZTUsIG51bGwsIFtbMSwgMywgNCwgNV1dKTsKICAgICAgfSkpKCk7CiAgICB9LAogICAgLy8g5aSE55CG6ZWc5YOP6YCJ5oup5Y+Y5YyWCiAgICBoYW5kbGVJbWFnZUNoYW5nZTogZnVuY3Rpb24gaGFuZGxlSW1hZ2VDaGFuZ2UoaW1hZ2VJZCkgewogICAgICBpZiAoaW1hZ2VJZCkgewogICAgICAgIHRoaXMuc2VsZWN0ZWRJbWFnZSA9IHRoaXMuaW1hZ2VMaXN0LmZpbmQoZnVuY3Rpb24gKGltZykgewogICAgICAgICAgcmV0dXJuIGltZy5pZCA9PT0gaW1hZ2VJZDsKICAgICAgICB9KTsKICAgICAgICBpZiAodGhpcy5zZWxlY3RlZEltYWdlKSB7CiAgICAgICAgICAvLyDoh6rliqjloavlhYXplZzlg4/lkI3np7DlkozmoIfnrb4KICAgICAgICAgIHRoaXMuZGVwbG95Rm9ybS5pbWFnZU5hbWUgPSB0aGlzLnNlbGVjdGVkSW1hZ2UubmFtZTsKICAgICAgICAgIHRoaXMuZGVwbG95Rm9ybS5pbWFnZVRhZyA9IHRoaXMuc2VsZWN0ZWRJbWFnZS50YWc7CgogICAgICAgICAgLy8g5pm66IO955Sf5oiQ5a655Zmo5ZCN56ew77yI5aaC5p6c5b2T5YmN5Li656m677yJCiAgICAgICAgICBpZiAoIXRoaXMuZGVwbG95Rm9ybS5jb250YWluZXJOYW1lKSB7CiAgICAgICAgICAgIHRoaXMuc3VnZ2VzdENvbnRhaW5lck5hbWUodGhpcy5zZWxlY3RlZEltYWdlLm5hbWUsIHRoaXMuc2VsZWN0ZWRJbWFnZS50YWcpOwogICAgICAgICAgfQoKICAgICAgICAgIC8vIOagueaNrumVnOWDj+exu+Wei+aZuuiDveaOqOiNkOerr+WPowogICAgICAgICAgdGhpcy5zdWdnZXN0U2VydmljZVBvcnQodGhpcy5zZWxlY3RlZEltYWdlLm5hbWUpOwogICAgICAgIH0KICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLnNlbGVjdGVkSW1hZ2UgPSBudWxsOwogICAgICAgIHRoaXMuZGVwbG95Rm9ybS5pbWFnZU5hbWUgPSAnJzsKICAgICAgICB0aGlzLmRlcGxveUZvcm0uaW1hZ2VUYWcgPSAnbGF0ZXN0JzsKICAgICAgfQogICAgfSwKICAgIC8vIOaZuuiDveeUn+aIkOWuueWZqOWQjeensAogICAgc3VnZ2VzdENvbnRhaW5lck5hbWU6IGZ1bmN0aW9uIHN1Z2dlc3RDb250YWluZXJOYW1lKGltYWdlTmFtZSwgaW1hZ2VUYWcpIHsKICAgICAgLy8g56e76Zmk6ZWc5YOP5ZCN56ew5Lit55qE54m55q6K5a2X56ym77yM55Sf5oiQ566A5rSB55qE5ZCN56ewCiAgICAgIHZhciBiYXNlTmFtZSA9IGltYWdlTmFtZS5yZXBsYWNlKC9bXmEtekEtWjAtOV0vZywgJy0nKS50b0xvd2VyQ2FzZSgpOwoKICAgICAgLy8g5aaC5p6c5qCH562+5LiN5pivbGF0ZXN077yM5YiZ5Yqg5YWl5qCH562+CiAgICAgIGlmIChpbWFnZVRhZyAmJiBpbWFnZVRhZyAhPT0gJ2xhdGVzdCcpIHsKICAgICAgICBiYXNlTmFtZSArPSAnLScgKyBpbWFnZVRhZy5yZXBsYWNlKC9bXmEtekEtWjAtOV0vZywgJy0nKTsKICAgICAgfQoKICAgICAgLy8g5re75Yqg5pe26Ze05oiz5L+d6K+B5ZSv5LiA5oCnCiAgICAgIHZhciB0aW1lc3RhbXAgPSBEYXRlLm5vdygpLnRvU3RyaW5nKCkuc2xpY2UoLTYpOwogICAgICB0aGlzLmRlcGxveUZvcm0uY29udGFpbmVyTmFtZSA9ICIiLmNvbmNhdChiYXNlTmFtZSwgIi0iKS5jb25jYXQodGltZXN0YW1wKTsKICAgIH0sCiAgICAvLyDmoLnmja7plZzlg4/nsbvlnovmmbrog73mjqjojZDnq6/lj6MKICAgIHN1Z2dlc3RTZXJ2aWNlUG9ydDogZnVuY3Rpb24gc3VnZ2VzdFNlcnZpY2VQb3J0KGltYWdlTmFtZSkgewogICAgICB2YXIgcG9ydE1hcCA9IHsKICAgICAgICAnbmdpbngnOiA4MCwKICAgICAgICAnYXBhY2hlJzogODAsCiAgICAgICAgJ2h0dHBkJzogODAsCiAgICAgICAgJ215c3FsJzogMzMwNiwKICAgICAgICAnbWFyaWFkYic6IDMzMDYsCiAgICAgICAgJ3Bvc3RncmVzJzogNTQzMiwKICAgICAgICAncG9zdGdyZXNxbCc6IDU0MzIsCiAgICAgICAgJ3JlZGlzJzogNjM3OSwKICAgICAgICAnbW9uZ29kYic6IDI3MDE3LAogICAgICAgICdtb25nbyc6IDI3MDE3LAogICAgICAgICd0b21jYXQnOiA4MDgwLAogICAgICAgICdub2RlJzogMzAwMCwKICAgICAgICAnc3ByaW5nJzogODA4MCwKICAgICAgICAnamF2YSc6IDgwODAKICAgICAgfTsKCiAgICAgIC8vIOafpeaJvuWMuemFjeeahOmVnOWDj+exu+WeiwogICAgICBmb3IgKHZhciBfaSA9IDAsIF9PYmplY3QkZW50cmllcyA9IE9iamVjdC5lbnRyaWVzKHBvcnRNYXApOyBfaSA8IF9PYmplY3QkZW50cmllcy5sZW5ndGg7IF9pKyspIHsKICAgICAgICB2YXIgX09iamVjdCRlbnRyaWVzJF9pID0gX3NsaWNlZFRvQXJyYXkoX09iamVjdCRlbnRyaWVzW19pXSwgMiksCiAgICAgICAgICBrZXkgPSBfT2JqZWN0JGVudHJpZXMkX2lbMF0sCiAgICAgICAgICBwb3J0ID0gX09iamVjdCRlbnRyaWVzJF9pWzFdOwogICAgICAgIGlmIChpbWFnZU5hbWUudG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhrZXkpKSB7CiAgICAgICAgICB0aGlzLmRlcGxveUZvcm0uc2VydmljZVBvcnQgPSBwb3J0OwogICAgICAgICAgYnJlYWs7CiAgICAgICAgfQogICAgICB9CiAgICB9LAogICAgLy8g5qC85byP5YyW6ZWc5YOP5aSn5bCPCiAgICBmb3JtYXRJbWFnZVNpemU6IGZ1bmN0aW9uIGZvcm1hdEltYWdlU2l6ZShzaXplTWIpIHsKICAgICAgaWYgKCFzaXplTWIgfHwgc2l6ZU1iID09PSAwKSByZXR1cm4gJy0nOwoKICAgICAgLy8g56Gu5L+d5piv5pWw5a2X57G75Z6LCiAgICAgIHZhciBzaXplID0gdHlwZW9mIHNpemVNYiA9PT0gJ3N0cmluZycgPyBwYXJzZUludChzaXplTWIpIDogc2l6ZU1iOwogICAgICBpZiAoaXNOYU4oc2l6ZSkpIHJldHVybiAnLSc7CgogICAgICAvLyDlpoLmnpzlt7Lnu4/mmK9NQuWNleS9je+8jOebtOaOpeaYvuekugogICAgICBpZiAoc2l6ZSA8IDEwMjQpIHsKICAgICAgICByZXR1cm4gIiIuY29uY2F0KHNpemUsICIgTUIiKTsKICAgICAgfQoKICAgICAgLy8g6L2s5o2i5Li6R0IKICAgICAgdmFyIHNpemVHYiA9IHNpemUgLyAxMDI0OwogICAgICByZXR1cm4gIiIuY29uY2F0KHNpemVHYi50b0ZpeGVkKDEpLCAiIEdCIik7CiAgICB9LAogICAgLy8g6I635Y+W5YiG5biD562W55Wl5pi+56S65ZCN56ewCiAgICBnZXRTdHJhdGVneURpc3BsYXlOYW1lOiBmdW5jdGlvbiBnZXRTdHJhdGVneURpc3BsYXlOYW1lKHN0cmF0ZWd5KSB7CiAgICAgIHZhciBzdHJhdGVneU1hcCA9IHsKICAgICAgICAnU1BSRUFEX0FDUk9TU19OT0RFUyc6ICfot6joioLngrnliIbmlaMnLAogICAgICAgICdXT1JLRVJfTk9ERVNfT05MWSc6ICfku4VXb3JrZXLoioLngrknLAogICAgICAgICdNQU5BR0VSX05PREVTX09OTFknOiAn5LuFTWFuYWdlcuiKgueCuScsCiAgICAgICAgJ0JBTEFOQ0VEJzogJ+W5s+ihoeWIhuW4gycsCiAgICAgICAgJ1NQUkVBRF9BQ1JPU1NfWk9ORVMnOiAn6Leo5Y+v55So5Yy65YiG5pWjJwogICAgICB9OwogICAgICByZXR1cm4gc3RyYXRlZ3lNYXBbc3RyYXRlZ3ldIHx8IHN0cmF0ZWd5OwogICAgfSwKICAgIC8vIOiOt+WPluaOqOiNkOeahOWIhuW4g+etlueVpQogICAgZ2V0UmVjb21tZW5kZWREaXN0cmlidXRpb25TdHJhdGVneTogZnVuY3Rpb24gZ2V0UmVjb21tZW5kZWREaXN0cmlidXRpb25TdHJhdGVneShyZXBsaWNhcykgewogICAgICByZXR1cm4gX2FzeW5jVG9HZW5lcmF0b3IoLyojX19QVVJFX18qL19yZWdlbmVyYXRvcigpLm0oZnVuY3Rpb24gX2NhbGxlZTYoKSB7CiAgICAgICAgdmFyIHJlc3BvbnNlLCBfdDM7CiAgICAgICAgcmV0dXJuIF9yZWdlbmVyYXRvcigpLncoZnVuY3Rpb24gKF9jb250ZXh0NikgewogICAgICAgICAgd2hpbGUgKDEpIHN3aXRjaCAoX2NvbnRleHQ2Lm4pIHsKICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgIF9jb250ZXh0Ni5wID0gMDsKICAgICAgICAgICAgICBfY29udGV4dDYubiA9IDE7CiAgICAgICAgICAgICAgcmV0dXJuIGdldFJlY29tbWVuZGVkU3RyYXRlZ3kocmVwbGljYXMpOwogICAgICAgICAgICBjYXNlIDE6CiAgICAgICAgICAgICAgcmVzcG9uc2UgPSBfY29udGV4dDYudjsKICAgICAgICAgICAgICBpZiAoIShyZXNwb25zZS5jb2RlID09PSAyMDAwMCAmJiByZXNwb25zZS5kYXRhKSkgewogICAgICAgICAgICAgICAgX2NvbnRleHQ2Lm4gPSAyOwogICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDYuYSgyLCByZXNwb25zZS5kYXRhLnJlY29tbWVuZGVkU3RyYXRlZ3kpOwogICAgICAgICAgICBjYXNlIDI6CiAgICAgICAgICAgICAgX2NvbnRleHQ2Lm4gPSA0OwogICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICBjYXNlIDM6CiAgICAgICAgICAgICAgX2NvbnRleHQ2LnAgPSAzOwogICAgICAgICAgICAgIF90MyA9IF9jb250ZXh0Ni52OwogICAgICAgICAgICAgIGNvbnNvbGUud2Fybign6I635Y+W5o6o6I2Q5YiG5biD562W55Wl5aSx6LSlOicsIF90Myk7CiAgICAgICAgICAgIGNhc2UgNDoKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQ2LmEoMiwgJ1NQUkVBRF9BQ1JPU1NfTk9ERVMnKTsKICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlNiwgbnVsbCwgW1swLCAzXV0pOwogICAgICB9KSkoKTsKICAgIH0sCiAgICAvLyA9PT0gSFNN6K6+5aSH55u45YWz5pa55rOVID09PQogICAgLy8g5aSE55CGSFNN5byA5YWz5Y+Y5YyWCiAgICBoYW5kbGVIc21Ub2dnbGU6IGZ1bmN0aW9uIGhhbmRsZUhzbVRvZ2dsZShlbmFibGVkKSB7CiAgICAgIGlmIChlbmFibGVkKSB7CiAgICAgICAgLy8g5ZCv55SoSFNN5pe25Yqg6L296K6+5aSH5YiX6KGoCiAgICAgICAgdGhpcy5sb2FkSHNtRGV2aWNlTGlzdCgpOwogICAgICB9IGVsc2UgewogICAgICAgIC8vIOemgeeUqEhTTeaXtua4heepuumAieaLqQogICAgICAgIHRoaXMuZGVwbG95Rm9ybS5oc21EZXZpY2VJZCA9IG51bGw7CiAgICAgICAgdGhpcy5zZWxlY3RlZEhzbURldmljZSA9IG51bGw7CiAgICAgIH0KICAgIH0sCiAgICAvLyDliqDovb1IU03orr7lpIfliJfooagKICAgIGxvYWRIc21EZXZpY2VMaXN0OiBmdW5jdGlvbiBsb2FkSHNtRGV2aWNlTGlzdCgpIHsKICAgICAgdmFyIF90aGlzMTUgPSB0aGlzOwogICAgICByZXR1cm4gX2FzeW5jVG9HZW5lcmF0b3IoLyojX19QVVJFX18qL19yZWdlbmVyYXRvcigpLm0oZnVuY3Rpb24gX2NhbGxlZTcoKSB7CiAgICAgICAgdmFyIHJlc3BvbnNlLCBfdDQ7CiAgICAgICAgcmV0dXJuIF9yZWdlbmVyYXRvcigpLncoZnVuY3Rpb24gKF9jb250ZXh0NykgewogICAgICAgICAgd2hpbGUgKDEpIHN3aXRjaCAoX2NvbnRleHQ3Lm4pIHsKICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgIF90aGlzMTUuaHNtRGV2aWNlTGlzdExvYWRpbmcgPSB0cnVlOwogICAgICAgICAgICAgIF9jb250ZXh0Ny5wID0gMTsKICAgICAgICAgICAgICBfY29udGV4dDcubiA9IDI7CiAgICAgICAgICAgICAgcmV0dXJuIGdldEF2YWlsYWJsZUhzbURldmljZXMoewogICAgICAgICAgICAgICAgc3RhdHVzOiAncnVubmluZycKICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgY2FzZSAyOgogICAgICAgICAgICAgIHJlc3BvbnNlID0gX2NvbnRleHQ3LnY7CiAgICAgICAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMDAwKSB7CiAgICAgICAgICAgICAgICBpZiAocmVzcG9uc2UuZGF0YSAmJiByZXNwb25zZS5kYXRhLmxpc3QpIHsKICAgICAgICAgICAgICAgICAgX3RoaXMxNS5oc21EZXZpY2VMaXN0ID0gcmVzcG9uc2UuZGF0YS5saXN0OwogICAgICAgICAgICAgICAgfSBlbHNlIGlmIChBcnJheS5pc0FycmF5KHJlc3BvbnNlLmRhdGEpKSB7CiAgICAgICAgICAgICAgICAgIF90aGlzMTUuaHNtRGV2aWNlTGlzdCA9IHJlc3BvbnNlLmRhdGE7CiAgICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgICBfdGhpczE1LmhzbURldmljZUxpc3QgPSBbXTsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIGlmIChfdGhpczE1LmhzbURldmljZUxpc3QubGVuZ3RoID09PSAwKSB7CiAgICAgICAgICAgICAgICAgIF90aGlzMTUuJG1lc3NhZ2UuaW5mbygn5b2T5YmN5rKh5pyJ5Y+v55So55qESFNN6K6+5aSHJyk7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgIF90aGlzMTUuJG1lc3NhZ2UuZXJyb3IoJ+iOt+WPlkhTTeiuvuWkh+WIl+ihqOWksei0pe+8micgKyAocmVzcG9uc2UubWVzc2FnZSB8fCAn5pyq55+l6ZSZ6K+vJykpOwogICAgICAgICAgICAgICAgX3RoaXMxNS5oc21EZXZpY2VMaXN0ID0gW107CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIF9jb250ZXh0Ny5uID0gNDsKICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgY2FzZSAzOgogICAgICAgICAgICAgIF9jb250ZXh0Ny5wID0gMzsKICAgICAgICAgICAgICBfdDQgPSBfY29udGV4dDcudjsKICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCfliqDovb1IU03orr7lpIfliJfooajlpLHotKU6JywgX3Q0KTsKICAgICAgICAgICAgICBfdGhpczE1LiRtZXNzYWdlLmVycm9yKCfliqDovb1IU03orr7lpIfliJfooajlpLHotKUnKTsKICAgICAgICAgICAgICBfdGhpczE1LmhzbURldmljZUxpc3QgPSBbXTsKICAgICAgICAgICAgY2FzZSA0OgogICAgICAgICAgICAgIF9jb250ZXh0Ny5wID0gNDsKICAgICAgICAgICAgICBfdGhpczE1LmhzbURldmljZUxpc3RMb2FkaW5nID0gZmFsc2U7CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0Ny5mKDQpOwogICAgICAgICAgICBjYXNlIDU6CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0Ny5hKDIpOwogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWU3LCBudWxsLCBbWzEsIDMsIDQsIDVdXSk7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIC8vIOWkhOeQhkhTTeiuvuWkh+mAieaLqeWPmOWMlgogICAgaGFuZGxlSHNtRGV2aWNlQ2hhbmdlOiBmdW5jdGlvbiBoYW5kbGVIc21EZXZpY2VDaGFuZ2UoZGV2aWNlSWQpIHsKICAgICAgaWYgKGRldmljZUlkKSB7CiAgICAgICAgdGhpcy5zZWxlY3RlZEhzbURldmljZSA9IHRoaXMuaHNtRGV2aWNlTGlzdC5maW5kKGZ1bmN0aW9uIChkZXZpY2UpIHsKICAgICAgICAgIHJldHVybiBkZXZpY2UuZGV2aWNlSWQgPT09IGRldmljZUlkOwogICAgICAgIH0pOwogICAgICAgIGlmICh0aGlzLnNlbGVjdGVkSHNtRGV2aWNlKSB7CiAgICAgICAgICAvLyDlj6/ku6XlnKjov5nph4zliqDovb3mm7Tor6bnu4bnmoTorr7lpIfkv6Hmga8KICAgICAgICAgIHRoaXMubG9hZEhzbURldmljZURldGFpbChkZXZpY2VJZCk7CiAgICAgICAgfQogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuc2VsZWN0ZWRIc21EZXZpY2UgPSBudWxsOwogICAgICB9CiAgICB9LAogICAgLy8g5Yqg6L29SFNN6K6+5aSH6K+m57uG5L+h5oGvCiAgICBsb2FkSHNtRGV2aWNlRGV0YWlsOiBmdW5jdGlvbiBsb2FkSHNtRGV2aWNlRGV0YWlsKGRldmljZUlkKSB7CiAgICAgIHZhciBfdGhpczE2ID0gdGhpczsKICAgICAgcmV0dXJuIF9hc3luY1RvR2VuZXJhdG9yKC8qI19fUFVSRV9fKi9fcmVnZW5lcmF0b3IoKS5tKGZ1bmN0aW9uIF9jYWxsZWU4KCkgewogICAgICAgIHZhciByZXNwb25zZSwgX3Q1OwogICAgICAgIHJldHVybiBfcmVnZW5lcmF0b3IoKS53KGZ1bmN0aW9uIChfY29udGV4dDgpIHsKICAgICAgICAgIHdoaWxlICgxKSBzd2l0Y2ggKF9jb250ZXh0OC5uKSB7CiAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICBfY29udGV4dDgucCA9IDA7CiAgICAgICAgICAgICAgX2NvbnRleHQ4Lm4gPSAxOwogICAgICAgICAgICAgIHJldHVybiBnZXRIc21EZXZpY2VEZXRhaWwoZGV2aWNlSWQpOwogICAgICAgICAgICBjYXNlIDE6CiAgICAgICAgICAgICAgcmVzcG9uc2UgPSBfY29udGV4dDgudjsKICAgICAgICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwMDAgJiYgcmVzcG9uc2UuZGF0YSkgewogICAgICAgICAgICAgICAgX3RoaXMxNi5zZWxlY3RlZEhzbURldmljZSA9IHJlc3BvbnNlLmRhdGE7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIF9jb250ZXh0OC5uID0gMzsKICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgY2FzZSAyOgogICAgICAgICAgICAgIF9jb250ZXh0OC5wID0gMjsKICAgICAgICAgICAgICBfdDUgPSBfY29udGV4dDgudjsKICAgICAgICAgICAgICBjb25zb2xlLndhcm4oJ+iOt+WPlkhTTeiuvuWkh+ivpue7huS/oeaBr+Wksei0pTonLCBfdDUpOwogICAgICAgICAgICBjYXNlIDM6CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0OC5hKDIpOwogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWU4LCBudWxsLCBbWzAsIDJdXSk7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIC8vIOW4pkhTTemFjee9rueahOmDqOe9suaWueazlQogICAgZGVwbG95V2l0aEhzbTogZnVuY3Rpb24gZGVwbG95V2l0aEhzbShkZXBsb3lEYXRhKSB7CiAgICAgIHJldHVybiBfYXN5bmNUb0dlbmVyYXRvcigvKiNfX1BVUkVfXyovX3JlZ2VuZXJhdG9yKCkubShmdW5jdGlvbiBfY2FsbGVlOSgpIHsKICAgICAgICByZXR1cm4gX3JlZ2VuZXJhdG9yKCkudyhmdW5jdGlvbiAoX2NvbnRleHQ5KSB7CiAgICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dDkubikgewogICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0OS5hKDIsIGRlcGxveUNvbnRhaW5lcldpdGhIc20oZGVwbG95RGF0YSkpOwogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWU5KTsKICAgICAgfSkpKCk7CiAgICB9CiAgfSwKICBtb3VudGVkOiBmdW5jdGlvbiBtb3VudGVkKCkgewogICAgdGhpcy5nZXRMaXN0KCk7CiAgICAvLyDpooTliqDovb3plZzlg4/liJfooajvvIzmj5DljYfnlKjmiLfkvZPpqowKICAgIHRoaXMubG9hZEltYWdlTGlzdCgpOwogICAgLy8g6aKE5Yqg6L29SFNN6K6+5aSH5YiX6KGoCiAgICB0aGlzLmxvYWRIc21EZXZpY2VMaXN0KCk7CiAgfSwKICBjb21wdXRlZDogX29iamVjdFNwcmVhZCh7fSwgbWFwR2V0dGVycyhbJ3VzZXInXSkpLAogIHdhdGNoOiB7CiAgICAvLyDnm5HlkKzlia/mnKzmlbDlj5jljJbvvIzoh6rliqjmm7TmlrDmjqjojZDnmoTliIbluIPnrZbnlaUKICAgICdkZXBsb3lGb3JtLnJlcGxpY2FzJzogZnVuY3Rpb24gZGVwbG95Rm9ybVJlcGxpY2FzKG5ld1JlcGxpY2FzLCBvbGRSZXBsaWNhcykgewogICAgICB2YXIgX3RoaXMxNyA9IHRoaXM7CiAgICAgIGlmIChuZXdSZXBsaWNhcyA+IDEgJiYgbmV3UmVwbGljYXMgIT09IG9sZFJlcGxpY2FzKSB7CiAgICAgICAgdGhpcy5nZXRSZWNvbW1lbmRlZERpc3RyaWJ1dGlvblN0cmF0ZWd5KG5ld1JlcGxpY2FzKS50aGVuKGZ1bmN0aW9uIChzdHJhdGVneSkgewogICAgICAgICAgX3RoaXMxNy5kZXBsb3lGb3JtLmRpc3RyaWJ1dGlvblN0cmF0ZWd5ID0gc3RyYXRlZ3k7CiAgICAgICAgfSk7CiAgICAgIH0gZWxzZSBpZiAobmV3UmVwbGljYXMgPT09IDEpIHsKICAgICAgICAvLyDljZXlia/mnKzml7bkuI3pnIDopoHliIbluIPnrZbnlaUKICAgICAgICB0aGlzLmRlcGxveUZvcm0uZGlzdHJpYnV0aW9uU3RyYXRlZ3kgPSAnQkFMQU5DRUQnOwogICAgICB9CiAgICB9LAogICAgLy8g55uR5ZCs5omp57yp5a655Ymv5pys5pWw5Y+Y5YyWCiAgICAnc2NhbGVGb3JtLnJlcGxpY2FzJzogZnVuY3Rpb24gc2NhbGVGb3JtUmVwbGljYXMobmV3UmVwbGljYXMsIG9sZFJlcGxpY2FzKSB7CiAgICAgIHZhciBfdGhpczE4ID0gdGhpczsKICAgICAgaWYgKG5ld1JlcGxpY2FzID4gMSAmJiBuZXdSZXBsaWNhcyAhPT0gb2xkUmVwbGljYXMpIHsKICAgICAgICB0aGlzLmdldFJlY29tbWVuZGVkRGlzdHJpYnV0aW9uU3RyYXRlZ3kobmV3UmVwbGljYXMpLnRoZW4oZnVuY3Rpb24gKHN0cmF0ZWd5KSB7CiAgICAgICAgICBfdGhpczE4LnNjYWxlRm9ybS5kaXN0cmlidXRpb25TdHJhdGVneSA9IHN0cmF0ZWd5OwogICAgICAgIH0pOwogICAgICB9IGVsc2UgaWYgKG5ld1JlcGxpY2FzID09PSAxKSB7CiAgICAgICAgdGhpcy5zY2FsZUZvcm0uZGlzdHJpYnV0aW9uU3RyYXRlZ3kgPSAnQkFMQU5DRUQnOwogICAgICB9CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["mapGetters", "Pagination", "getContainerList", "deployContainer", "deployContainerWithTraefik", "deployContainerWithStrategy", "getRecommendedStrategy", "startContainer", "stopContainer", "restartContainer", "remove<PERSON><PERSON><PERSON>", "getContainerLogs", "syncContainerStatus", "scaleContainer", "updateContainerImage", "configContainerRoute", "updateContainerRoute", "getImageList", "fetchApplicationOptions", "fetchUserApps", "getAvailableHsmDevices", "getHsmDeviceDetail", "deployContainerWithHsm", "name", "components", "data", "tableList", "loading", "total", "list<PERSON>uery", "page", "pageSize", "containerName", "undefined", "status", "logsDialogVisible", "containerLogs", "currentC<PERSON><PERSON>", "deployDialogVisible", "deployForm", "imageId", "imageName", "imageTag", "servicePort", "replicas", "distributionStrategy", "enableTraefik", "enableHsm", "hsmDeviceId", "deployedBy", "applicationId", "applicationName", "deployRules", "required", "message", "trigger", "scaleDialogVisible", "scaleForm", "id", "scaleRules", "imageList", "imageListLoading", "selectedImage", "hsmDeviceList", "hsmDeviceListLoading", "selectedHsmDevice", "appOptions", "appOptionsLoading", "methods", "handleSearch", "getList", "handleResetSearch", "handleDeployContainer", "_this", "Promise", "all", "loadImageList", "loadAppOptions", "then", "user", "$nextTick", "$refs", "clearValidate", "confirmDeployContainer", "_this2", "validate", "valid", "$message", "error", "deployData", "_objectSpread", "hsmDeviceConfig", "encryptorGroupId", "encryptorId", "deviceId", "encryptorName", "deviceName", "serverIpAddr", "ip<PERSON><PERSON><PERSON>", "serverPort", "tcpConnNum", "msgHeadLen", "msgTailLen", "asciiOrEbcdic", "encoding", "dynamicLibPath", "deployFn", "console", "log", "deployWithHsm", "deployWithTraefik", "response", "code", "accessUrl", "concat", "container", "getStrategyDisplayName", "hsmConfigured", "success", "catch", "_this3", "_asyncToGenerator", "_regenerator", "m", "_callee", "resp", "_t", "w", "_context", "n", "p", "Array", "isArray", "roles", "includes", "v", "list", "map", "item", "label", "value", "String", "f", "a", "handleStartContainer", "row", "_this4", "handleStopContainer", "_this5", "handleRestartContainer", "_this6", "handleRemoveContainer", "_this7", "$msgbox", "confirm", "confirmButtonText", "cancelButtonText", "type", "handleScaleContainer", "_this8", "confirmScaleContainer", "_this9", "handleViewLogs", "_this0", "handleSyncStatus", "_this1", "_this10", "totalCount", "formatPorts", "portMappings", "ports", "JSON", "parse", "length", "hostPort", "containerPort", "protocol", "join", "e", "formatDate", "dateTime", "Date", "toLocaleString", "copyToClipboard", "text", "_this11", "navigator", "clipboard", "writeText", "textArea", "document", "createElement", "body", "append<PERSON><PERSON><PERSON>", "select", "execCommand", "<PERSON><PERSON><PERSON><PERSON>", "handleConfigRoute", "_this12", "$prompt", "inputValue", "inputValidator", "port", "parseInt", "_ref", "routeConfig", "containerId", "handleUpdateRoute", "_this13", "_ref2", "_callee2", "_context2", "_callee3", "_context3", "_callee4", "_context4", "_this14", "_callee5", "_t2", "_context5", "image", "sizeMb", "info", "handleImageChange", "find", "img", "tag", "suggestContainerName", "suggestServicePort", "baseName", "replace", "toLowerCase", "timestamp", "now", "toString", "slice", "portMap", "_i", "_Object$entries", "Object", "entries", "_Object$entries$_i", "_slicedToArray", "key", "formatImageSize", "size", "isNaN", "sizeGb", "toFixed", "strategy", "strategyMap", "getRecommendedDistributionStrategy", "_callee6", "_t3", "_context6", "recommendedStrategy", "warn", "handleHsmToggle", "enabled", "loadHsmDeviceList", "_this15", "_callee7", "_t4", "_context7", "handleHsmDeviceChange", "device", "loadHsmDeviceDetail", "_this16", "_callee8", "_t5", "_context8", "_callee9", "_context9", "mounted", "computed", "watch", "deployFormReplicas", "newReplicas", "oldReplicas", "_this17", "scaleFormReplicas", "_this18"], "sources": ["src/views/docker/container/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-card shadow=\"never\">\n      <div class=\"filter-container clearfix\">\n        <el-form ref=\"searchForm\" :model=\"listQuery\" inline @submit.native.prevent=\"getList\">\n          <div class=\"filter-inner\">\n            <el-form-item label=\"容器名称\">\n              <el-input v-model.trim=\"listQuery.containerName\" clearable placeholder=\"容器名称\" />\n            </el-form-item>\n            <el-form-item label=\"状态\">\n              <el-select v-model=\"listQuery.status\" clearable placeholder=\"请选择状态\">\n                <el-option label=\"运行中\" value=\"running\" />\n                <el-option label=\"已停止\" value=\"stopped\" />\n                <el-option label=\"已暂停\" value=\"paused\" />\n              </el-select>\n            </el-form-item>\n          </div>\n          <div class=\"pull-right\">\n            <el-button-group>\n              <el-form-item>\n                <el-button type=\"primary\" @click=\"handleSearch\"><i class=\"el-icon-search\" /> 查询</el-button>\n                <el-button type=\"info\" @click=\"handleResetSearch\"><i class=\"el-icon-refresh\" /> 重置</el-button>\n              </el-form-item>\n            </el-button-group>\n          </div>\n        </el-form>\n      </div>\n    </el-card>\n    \n    <el-card shadow=\"never\">\n      <div class=\"filter-container clearfix\">\n        <div class=\"filter-inner\">\n          <el-button type=\"primary\" @click=\"handleDeployContainer\">部署容器</el-button>\n          <el-button type=\"success\" @click=\"handleSyncStatus\">同步状态</el-button>\n        </div>\n      </div>\n      \n      <el-table\n        ref=\"dataTable\"\n        v-loading=\"loading\"\n        :data=\"tableList\"\n        style=\"width: 100%;\"\n      >\n        <el-table-column align=\"center\" label=\"序号\" type=\"index\" width=\"50\" />\n        <el-table-column align=\"center\" label=\"容器ID\" min-width=\"120\" prop=\"id\" />\n        <el-table-column align=\"center\" label=\"容器名称\" min-width=\"150\" prop=\"containerName\" />\n        <el-table-column align=\"center\" label=\"所属应用\" min-width=\"140\">\n          <template v-slot=\"{row}\">\n            <span>{{ row.applicationName || '-' }}</span>\n          </template>\n        </el-table-column>\n        <el-table-column align=\"center\" label=\"镜像\" min-width=\"150\">\n          <template v-slot=\"{row}\">\n            <span>{{ row.imageName }}:{{ row.imageTag || 'latest' }}</span>\n          </template>\n        </el-table-column>\n        <el-table-column align=\"center\" label=\"状态\" min-width=\"100\" prop=\"status\">\n          <template v-slot=\"{row}\">\n            <el-tag v-if=\"row.status === 'running'\" type=\"success\">运行中</el-tag>\n            <el-tag v-else-if=\"row.status === 'stopped'\" type=\"danger\">已停止</el-tag>\n            <el-tag v-else-if=\"row.status === 'paused'\" type=\"warning\">已暂停</el-tag>\n            <el-tag v-else>{{ row.status }}</el-tag>\n          </template>\n        </el-table-column>\n        <el-table-column align=\"center\" label=\"端口\" min-width=\"120\">\n          <template v-slot=\"{row}\">\n            <span>{{ formatPorts(row.portMappings) }}</span>\n          </template>\n        </el-table-column>\n        <el-table-column align=\"center\" label=\"副本信息\" min-width=\"120\">\n          <template v-slot=\"{row}\">\n            <div v-if=\"row.replicas\">\n              <div>副本数：{{ row.replicas || '-' }}</div>\n              <div v-if=\"row.distributionStrategy\" class=\"strategy-info\">\n                分布：{{ getStrategyDisplayName(row.distributionStrategy) }}\n              </div>\n            </div>\n            <span v-else>-</span>\n          </template>\n        </el-table-column>\n        <el-table-column align=\"center\" label=\"访问地址\" min-width=\"180\">\n          <template v-slot=\"{row}\">\n            <div v-if=\"row.accessUrl\">\n              <el-link :href=\"row.accessUrl\" target=\"_blank\" type=\"primary\">\n                {{ row.accessUrl }}\n              </el-link>\n              <el-button \n                type=\"text\" \n                size=\"mini\" \n                @click=\"copyToClipboard(row.accessUrl)\"\n                style=\"margin-left: 5px;\"\n              >\n                <i class=\"el-icon-copy-document\"></i>\n              </el-button>\n            </div>\n            <el-button \n              v-else-if=\"row.status === 'running'\" \n              type=\"text\" \n              size=\"mini\" \n              @click=\"handleConfigRoute(row)\"\n            >\n              配置访问\n            </el-button>\n            <span v-else>-</span>\n          </template>\n        </el-table-column>\n        <el-table-column align=\"center\" label=\"HSM设备\" min-width=\"120\">\n          <template v-slot=\"{row}\">\n            <div v-if=\"row.hsmDeviceId || row.hsmConfigured\">\n              <el-tag type=\"success\" size=\"mini\">已配置</el-tag>\n              <div v-if=\"row.hsmDeviceName\" class=\"hsm-info\">\n                {{ row.hsmDeviceName }}\n              </div>\n            </div>\n            <span v-else>-</span>\n          </template>\n        </el-table-column>\n        <el-table-column align=\"center\" label=\"节点\" min-width=\"100\" prop=\"nodeName\" />\n        <el-table-column align=\"center\" label=\"创建时间\" min-width=\"150\">\n          <template v-slot=\"{row}\">\n            <span>{{ formatDate(row.createTime) }}</span>\n          </template>\n        </el-table-column>\n        <el-table-column align=\"center\" fixed=\"right\" label=\"操作\" width=\"250\">\n          <template v-slot=\"{row}\">\n            <el-button v-if=\"row.status === 'running'\" type=\"text\" @click=\"handleStopContainer(row)\">停止</el-button>\n            <el-button v-else type=\"text\" @click=\"handleStartContainer(row)\">启动</el-button>\n            <el-button type=\"text\" @click=\"handleRestartContainer(row)\">重启</el-button>\n            <el-button type=\"text\" @click=\"handleScaleContainer(row)\">扩缩容</el-button>\n            <el-button type=\"text\" @click=\"handleViewLogs(row)\">日志</el-button>\n            <el-button v-if=\"row.accessUrl\" type=\"text\" @click=\"handleUpdateRoute(row)\">更新路由</el-button>\n            <el-button type=\"text\" @click=\"handleRemoveContainer(row)\">删除</el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n      \n      <pagination\n        v-show=\"total > listQuery.pageSize\"\n        :limit.sync=\"listQuery.pageSize\"\n        :page.sync=\"listQuery.page\"\n        :total=\"total\"\n        layout=\"prev, pager, next\"\n        @pagination=\"getList\"\n      />\n    </el-card>\n    \n    <!-- 部署容器对话框 -->\n    <el-dialog :visible.sync=\"deployDialogVisible\" title=\"部署容器\" width=\"600px\">\n      <el-form ref=\"deployForm\" :model=\"deployForm\" :rules=\"deployRules\" label-width=\"120px\">\n        <el-form-item label=\"关联应用\" prop=\"applicationId\">\n          <el-select v-model=\"deployForm.applicationId\" filterable placeholder=\"请选择应用\" style=\"width: 100%;\" :loading=\"appOptionsLoading\">\n            <el-option v-for=\"opt in appOptions\" :key=\"opt.value\" :label=\"opt.label\" :value=\"String(opt.value)\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"容器名称\" prop=\"containerName\">\n          <el-input v-model.trim=\"deployForm.containerName\" placeholder=\"例如: my-nginx\" />\n        </el-form-item>\n        <el-form-item label=\"镜像选择\" prop=\"imageId\">\n          <el-select \n            v-model=\"deployForm.imageId\" \n            filterable \n            placeholder=\"请选择镜像\"\n            @change=\"handleImageChange\"\n            style=\"width: 100%;\"\n            :loading=\"imageListLoading\"\n            :disabled=\"imageList.length === 0\"\n          >\n            <el-option\n              v-for=\"image in imageList\"\n              :key=\"image.id\"\n              :label=\"`${image.name}:${image.tag}`\"\n              :value=\"image.id\"\n            >\n              <span style=\"float: left\">{{ image.name }}:{{ image.tag }}</span>\n              <span style=\"float: right; color: #8492a6; font-size: 13px\">{{ formatImageSize(image.sizeMb) }}</span>\n            </el-option>\n            <div v-if=\"imageList.length === 0\" slot=\"empty\">\n              <span v-if=\"imageListLoading\">加载中...</span>\n              <span v-else>暂无可用镜像，请先构建或导入镜像</span>\n            </div>\n          </el-select>\n          <div class=\"form-help\">\n            选择已有的Docker镜像进行部署\n            <el-button \n              type=\"text\" \n              size=\"mini\" \n              @click=\"loadImageList\"\n              style=\"margin-left: 8px;\"\n            >\n              <i class=\"el-icon-refresh\"></i> 刷新\n            </el-button>\n          </div>\n        </el-form-item>\n        <el-form-item label=\"镜像信息\" v-if=\"selectedImage\">\n          <div class=\"image-info\">\n            <p><strong>名称：</strong>{{ selectedImage.name }}</p>\n            <p><strong>标签：</strong>{{ selectedImage.tag }}</p>\n            <p><strong>大小：</strong>{{ formatImageSize(selectedImage.sizeMb) }}</p>\n            <p v-if=\"selectedImage.description\"><strong>描述：</strong>{{ selectedImage.description }}</p>\n          </div>\n        </el-form-item>\n        <el-form-item label=\"服务端口\" prop=\"servicePort\">\n          <el-input-number v-model=\"deployForm.servicePort\" placeholder=\"容器内部端口，如: 80\" :min=\"1\" :max=\"65535\" />\n          <div class=\"form-help\">容器内应用监听的端口号</div>\n        </el-form-item>\n        <el-form-item label=\"副本数\" prop=\"replicas\">\n          <el-input-number v-model=\"deployForm.replicas\" :min=\"1\" />\n        </el-form-item>\n        <el-form-item label=\"分布策略\" v-if=\"deployForm.replicas > 1\">\n          <el-select v-model=\"deployForm.distributionStrategy\" placeholder=\"请选择分布策略\" style=\"width: 100%;\">\n            <el-option label=\"跨节点分散（推荐）\" value=\"SPREAD_ACROSS_NODES\" />\n            <el-option label=\"仅Worker节点\" value=\"WORKER_NODES_ONLY\" />\n            <el-option label=\"仅Manager节点\" value=\"MANAGER_NODES_ONLY\" />\n            <el-option label=\"平衡分布\" value=\"BALANCED\" />\n            <el-option label=\"跨可用区分散\" value=\"SPREAD_ACROSS_ZONES\" />\n          </el-select>\n          <div class=\"form-help\">多副本时的部署分布策略，推荐选择跨节点分散</div>\n        </el-form-item>\n        <el-form-item label=\"启用路由\">\n          <el-switch v-model=\"deployForm.enableTraefik\" active-text=\"启用\" inactive-text=\"禁用\" />\n          <div class=\"form-help\">启用后可通过内网地址访问服务</div>\n        </el-form-item>\n        <el-form-item label=\"HSM设备资源\">\n          <el-switch v-model=\"deployForm.enableHsm\" active-text=\"启用\" inactive-text=\"禁用\" @change=\"handleHsmToggle\" />\n          <div class=\"form-help\">启用后可配置HSM加密设备资源</div>\n        </el-form-item>\n        <el-form-item v-if=\"deployForm.enableHsm\" label=\"选择设备\" prop=\"hsmDeviceId\">\n          <el-select \n            v-model=\"deployForm.hsmDeviceId\" \n            filterable \n            placeholder=\"请选择HSM设备\"\n            @change=\"handleHsmDeviceChange\"\n            style=\"width: 100%;\"\n            :loading=\"hsmDeviceListLoading\"\n            :disabled=\"hsmDeviceList.length === 0\"\n          >\n            <el-option\n              v-for=\"device in hsmDeviceList\"\n              :key=\"device.deviceId\"\n              :label=\"device.deviceName\"\n              :value=\"device.deviceId\"\n            >\n              <span style=\"float: left\">{{ device.deviceName }}</span>\n              <span style=\"float: right; color: #8492a6; font-size: 13px\">{{ device.ipAddress }}:{{ device.servicePort }}</span>\n            </el-option>\n            <div v-if=\"hsmDeviceList.length === 0\" slot=\"empty\">\n              <span v-if=\"hsmDeviceListLoading\">加载中...</span>\n              <span v-else>暂无可用设备</span>\n            </div>\n          </el-select>\n          <div class=\"form-help\">\n            选择可用的HSM加密设备\n            <el-button \n              type=\"text\" \n              size=\"mini\" \n              @click=\"loadHsmDeviceList\"\n              style=\"margin-left: 8px;\"\n            >\n              <i class=\"el-icon-refresh\"></i> 刷新\n            </el-button>\n          </div>\n        </el-form-item>\n        <el-form-item v-if=\"deployForm.enableHsm && selectedHsmDevice\" label=\"设备信息\">\n          <div class=\"device-info\">\n            <p><strong>设备名称：</strong>{{ selectedHsmDevice.deviceName }}</p>\n            <p><strong>IP地址：</strong>{{ selectedHsmDevice.ipAddress }}</p>\n            <p><strong>服务端口：</strong>{{ selectedHsmDevice.servicePort }}</p>\n            <p><strong>管理端口：</strong>{{ selectedHsmDevice.managementPort }}</p>\n            <p><strong>状态：</strong>\n              <el-tag v-if=\"selectedHsmDevice.status === 'available'\" type=\"success\" size=\"mini\">可用</el-tag>\n              <el-tag v-else-if=\"selectedHsmDevice.status === 'running'\" type=\"success\" size=\"mini\">运行中</el-tag>\n              <el-tag v-else-if=\"selectedHsmDevice.status === 'active'\" type=\"success\" size=\"mini\">活跃</el-tag>\n              <el-tag v-else type=\"warning\" size=\"mini\">{{ selectedHsmDevice.status }}</el-tag>\n            </p>\n            <p v-if=\"selectedHsmDevice.description\"><strong>描述：</strong>{{ selectedHsmDevice.description }}</p>\n            <p v-if=\"selectedHsmDevice.deviceGroupName\"><strong>设备组：</strong>{{ selectedHsmDevice.deviceGroupName }}</p>\n          </div>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"deployDialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"confirmDeployContainer\">部署</el-button>\n      </div>\n    </el-dialog>\n    \n    <!-- 容器日志对话框 -->\n    <el-dialog :visible.sync=\"logsDialogVisible\" title=\"容器日志\" width=\"800px\">\n      <el-input\n        v-model=\"containerLogs\"\n        :rows=\"20\"\n        readonly\n        type=\"textarea\"\n      />\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"logsDialogVisible = false\">关闭</el-button>\n      </div>\n    </el-dialog>\n    \n    <!-- 扩缩容对话框 -->\n    <el-dialog :visible.sync=\"scaleDialogVisible\" title=\"容器扩缩容\" width=\"500px\">\n      <el-form ref=\"scaleForm\" :model=\"scaleForm\" :rules=\"scaleRules\" label-width=\"100px\">\n        <el-form-item label=\"副本数\" prop=\"replicas\">\n          <el-input-number v-model=\"scaleForm.replicas\" :min=\"1\" />\n          <div class=\"form-help\">当前运行的容器实例数量</div>\n        </el-form-item>\n        <el-form-item label=\"分布策略\" v-if=\"scaleForm.replicas > 1\">\n          <el-select v-model=\"scaleForm.distributionStrategy\" placeholder=\"请选择分布策略\" style=\"width: 100%;\">\n            <el-option label=\"跨节点分散（推荐）\" value=\"SPREAD_ACROSS_NODES\" />\n            <el-option label=\"仅Worker节点\" value=\"WORKER_NODES_ONLY\" />\n            <el-option label=\"仅Manager节点\" value=\"MANAGER_NODES_ONLY\" />\n            <el-option label=\"平衡分布\" value=\"BALANCED\" />\n          </el-select>\n          <div class=\"form-help\">多副本时的部署分布策略</div>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"scaleDialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"confirmScaleContainer\">确认</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { mapGetters } from 'vuex'\nimport Pagination from '@/components/Pagination'\nimport { \n  getContainerList, \n  deployContainer, \n  deployContainerWithTraefik,\n  deployContainerWithStrategy,\n  getRecommendedStrategy,\n  startContainer, \n  stopContainer, \n  restartContainer, \n  removeContainer, \n  getContainerLogs, \n  syncContainerStatus, \n  scaleContainer, \n  updateContainerImage,\n  configContainerRoute,\n  updateContainerRoute\n} from '@/api/docker/container'\nimport { getImageList } from '@/api/docker/image'\nimport { fetchApplicationOptions, fetchUserApps } from '@/api/application'\nimport { getAvailableHsmDevices, getHsmDeviceDetail, deployContainerWithHsm } from '@/api/docker/hsm'\n\nexport default {\n  name: 'DockerContainerIndex',\n  components: {\n    Pagination\n  },\n  data() {\n    return {\n      tableList: [],\n      loading: false,\n      total: 0,\n      listQuery: {\n        page: 1,\n        pageSize: 20,\n        containerName: undefined,\n        status: undefined\n      },\n      logsDialogVisible: false,\n      containerLogs: '',\n      currentContainer: null,\n      deployDialogVisible: false,\n      deployForm: {\n        containerName: '',\n        imageId: null,\n        imageName: '',\n        imageTag: 'latest',\n        servicePort: 80,\n        replicas: 1,\n        distributionStrategy: 'SPREAD_ACROSS_NODES',\n        enableTraefik: true,\n        enableHsm: false,\n        hsmDeviceId: null,\n        deployedBy: null,\n        applicationId: null,\n        applicationName: ''\n      },\n      deployRules: {\n        applicationId: [{ required: true, message: '请选择关联应用', trigger: 'change' }],\n        containerName: [{ required: true, message: '请输入容器名称', trigger: 'blur' }],\n        imageId: [{ required: true, message: '请选择镜像', trigger: 'change' }],\n        servicePort: [{ required: true, message: '请输入服务端口', trigger: 'blur' }]\n      },\n      scaleDialogVisible: false,\n      scaleForm: {\n        id: '',\n        replicas: 1,\n        distributionStrategy: 'SPREAD_ACROSS_NODES'\n      },\n      scaleRules: {\n        replicas: [{ required: true, message: '请输入副本数量', trigger: 'blur' }]\n      },\n      // 镜像相关数据\n      imageList: [],\n      imageListLoading: false,\n      selectedImage: null,\n      // HSM设备相关数据\n      hsmDeviceList: [],\n      hsmDeviceListLoading: false,\n      selectedHsmDevice: null,\n      // 应用下拉\n      appOptions: [],\n      appOptionsLoading: false\n    }\n  },\n  methods: {\n    handleSearch() {\n      this.getList()\n    },\n    handleResetSearch() {\n      this.listQuery = {\n        page: 1,\n        pageSize: 20,\n        containerName: undefined,\n        status: undefined\n      }\n      this.getList()\n    },\n    handleDeployContainer() {\n      // 首先加载镜像列表\n      Promise.all([this.loadImageList(), this.loadAppOptions()]).then(() => {\n        this.deployForm = {\n          containerName: '',\n          imageId: null,\n          imageName: '',\n          imageTag: 'latest',\n          servicePort: 80,\n          replicas: 1,\n          distributionStrategy: 'SPREAD_ACROSS_NODES',\n          enableTraefik: true,\n          enableHsm: false,\n          hsmDeviceId: null,\n          deployedBy: this.user ? this.user.id : null,\n          applicationId: null,\n          applicationName: ''\n        }\n        this.selectedImage = null\n        this.selectedHsmDevice = null\n        this.deployDialogVisible = true\n        this.$nextTick(() => {\n          this.$refs['deployForm'].clearValidate()\n        })\n      })\n    },\n    confirmDeployContainer() {\n      this.$refs['deployForm'].validate((valid) => {\n        if (valid) {\n          // 检查HSM设备配置\n          if (this.deployForm.enableHsm && !this.deployForm.hsmDeviceId) {\n            this.$message.error('请选择HSM设备')\n            return\n          }\n          \n          const deployData = { ...this.deployForm }\n          \n          // 确保镜像信息完整\n          if (this.selectedImage) {\n            deployData.imageName = this.selectedImage.imageName\n            deployData.imageTag = this.selectedImage.imageTag\n          }\n          \n          // 构建HSM设备配置\n          if (this.deployForm.enableHsm && this.selectedHsmDevice) {\n            deployData.hsmDeviceConfig = {\n              encryptorGroupId: 1, // 默认组ID\n              encryptorId: this.selectedHsmDevice.deviceId,\n              encryptorName: this.selectedHsmDevice.deviceName,\n              serverIpAddr: this.selectedHsmDevice.ipAddress,\n              serverPort: this.selectedHsmDevice.servicePort,\n              tcpConnNum: this.selectedHsmDevice.tcpConnNum || 5,\n              msgHeadLen: this.selectedHsmDevice.msgHeadLen || 4,\n              msgTailLen: this.selectedHsmDevice.msgTailLen || 0,\n              asciiOrEbcdic: this.selectedHsmDevice.encoding || 0,\n              dynamicLibPath: './libdeviceapi.so'\n            }\n          }\n          \n          // 根据配置选择部署接口\n          let deployFn\n          if (this.deployForm.enableHsm) {\n            // 使用带HSM配置的部署接口\n            console.log('使用带HSM配置的部署接口')\n            deployFn = this.deployWithHsm\n          } else if (deployData.replicas > 1 && deployData.distributionStrategy) {\n            // 使用带分布策略的部署接口\n            console.log('使用带分布策略的部署接口')\n            deployFn = deployContainerWithStrategy\n          } else if (deployData.enableTraefik) {\n            // 使用带Traefik的部署接口\n            console.log('使用带Traefik的部署接口')\n            deployFn = this.deployWithTraefik\n          } else {\n            // 使用基本部署接口\n            console.log('使用基本部署接口')\n            deployFn = deployContainer\n          }\n          \n          deployFn(deployData).then((response) => {\n            this.deployDialogVisible = false\n            if (response.code === 20000) {\n              if (response.data) {\n                // 处理不同的响应结构\n                let message = '容器部署成功！'\n                \n                if (response.data.accessUrl) {\n                  message += `访问地址：${response.data.accessUrl}`\n                } else if (response.data.container && response.data.container.accessUrl) {\n                  message += `访问地址：${response.data.container.accessUrl}`\n                }\n                \n                if (response.data.distributionStrategy) {\n                  message += `，分布策略：${this.getStrategyDisplayName(response.data.distributionStrategy)}`\n                }\n                \n                if (response.data.hsmConfigured) {\n                  message += `，HSM设备：${this.selectedHsmDevice.deviceName}`\n                }\n                \n                this.$message.success(message)\n              } else {\n                this.$message.success('容器部署任务已提交')\n              }\n              this.getList()\n            } else {\n              this.$message.error(response.message || '部署失败')\n            }\n          }).catch(() => {\n            // 错误处理已在拦截器中处理\n          })\n        }\n      })\n    },\n\n    // 加载应用选项\n    async loadAppOptions() {\n      this.appOptionsLoading = true\n      try {\n        let resp\n        // 租户角色使用 user/list-options，其它使用 fetch-options\n        if (this.user && Array.isArray(this.user.roles) && this.user.roles.includes('ROLE_TENANT')) {\n          resp = await fetchUserApps()\n        } else {\n          resp = await fetchApplicationOptions()\n        }\n        if (resp && resp.code === 20000) {\n          const data = Array.isArray(resp.data) ? resp.data : (resp.data && resp.data.list) ? resp.data.list : []\n          this.appOptions = data.map(item => {\n            if (item.label && (item.value !== undefined)) return item\n            return { label: item.name || item.applicationName || item.label, value: String(item.id || item.value) }\n          })\n        } else {\n          this.appOptions = []\n        }\n      } catch (e) {\n        this.appOptions = []\n      } finally {\n        this.appOptionsLoading = false\n      }\n    },\n    handleStartContainer(row) {\n      startContainer(row.id).then(() => {\n        this.$message.success('容器启动成功')\n        this.getList()\n      })\n    },\n    handleStopContainer(row) {\n      stopContainer(row.id).then(() => {\n        this.$message.success('容器停止成功')\n        this.getList()\n      })\n    },\n    handleRestartContainer(row) {\n      restartContainer(row.id).then(() => {\n        this.$message.success('容器重启成功')\n        this.getList()\n      })\n    },\n    handleRemoveContainer(row) {\n      this.$msgbox.confirm(`确认要删除容器 ${row.containerName} 吗？`, '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        removeContainer(row.id).then(() => {\n          this.$message.success('删除成功')\n          this.getList()\n        })\n      }).catch(() => {\n        // 取消删除\n      })\n    },\n    handleScaleContainer(row) {\n      this.scaleForm = {\n        id: row.id,\n        replicas: row.replicas || 1, // 使用当前副本数或默认值\n        distributionStrategy: row.distributionStrategy || 'SPREAD_ACROSS_NODES'\n      }\n      this.scaleDialogVisible = true\n      this.$nextTick(() => {\n        this.$refs['scaleForm'].clearValidate()\n      })\n    },\n    confirmScaleContainer() {\n      this.$refs['scaleForm'].validate((valid) => {\n        if (valid) {\n          scaleContainer(this.scaleForm.id, this.scaleForm.replicas).then(() => {\n            this.scaleDialogVisible = false\n            this.$message.success('容器扩缩容任务已提交')\n            this.getList()\n          })\n        }\n      })\n    },\n    handleViewLogs(row) {\n      this.currentContainer = row\n      getContainerLogs(row.id).then(response => {\n        if (response.code === 20000) {\n          this.containerLogs = response.data || ''\n          this.logsDialogVisible = true\n        }\n      })\n    },\n    handleSyncStatus() {\n      syncContainerStatus().then(() => {\n        this.$message.success('容器状态同步任务已提交')\n        this.getList()\n      })\n    },\n    getList() {\n      this.loading = true\n      getContainerList(this.listQuery).then(response => {\n        if (response.code === 20000) {\n          this.tableList = response.data.list\n          this.total = response.data.totalCount\n        }\n        this.loading = false\n      }).catch(() => {\n        this.loading = false\n      })\n    },\n    // 格式化端口映射显示\n    formatPorts(portMappings) {\n      if (!portMappings) return '-'\n      try {\n        const ports = JSON.parse(portMappings)\n        if (Array.isArray(ports) && ports.length > 0) {\n          return ports.map(p => `${p.hostPort || ''}:${p.containerPort}/${p.protocol || 'tcp'}`).join(', ')\n        }\n        return '-'\n      } catch (e) {\n        return portMappings\n      }\n    },\n    // 格式化日期显示\n    formatDate(dateTime) {\n      if (!dateTime) return '-'\n      return new Date(dateTime).toLocaleString('zh-CN')\n    },\n    \n    // 复制到剪贴板\n    copyToClipboard(text) {\n      if (navigator.clipboard) {\n        navigator.clipboard.writeText(text).then(() => {\n          this.$message.success('地址已复制到剪贴板')\n        })\n      } else {\n        // 降级方案\n        const textArea = document.createElement('textarea')\n        textArea.value = text\n        document.body.appendChild(textArea)\n        textArea.select()\n        document.execCommand('copy')\n        document.body.removeChild(textArea)\n        this.$message.success('地址已复制到剪贴板')\n      }\n    },\n    \n    // 配置访问路由\n    handleConfigRoute(row) {\n      this.$prompt('请输入服务端口（容器内部端口）', '配置访问路由', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        inputValue: '80',\n        inputValidator: (value) => {\n          const port = parseInt(value)\n          if (!port || port < 1 || port > 65535) {\n            return '请输入有效的端口号(1-65535)'\n          }\n          return true\n        }\n      }).then(({ value }) => {\n        const routeConfig = {\n          containerId: row.id,\n          servicePort: parseInt(value),\n          enableTraefik: true\n        }\n        \n        this.configContainerRoute(routeConfig).then((response) => {\n          if (response.code === 20000 && response.data && response.data.accessUrl) {\n            this.$message.success(`路由配置成功！访问地址：${response.data.accessUrl}`)\n            this.getList()\n          } else {\n            this.$message.error(response.message || '配置失败')\n          }\n        }).catch(() => {\n          // 错误处理已在拦截器中处理\n        })\n      }).catch(() => {\n        // 取消操作\n      })\n    },\n    \n    // 更新路由配置\n    handleUpdateRoute(row) {\n      this.$prompt('请输入新的服务端口', '更新路由', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        inputValidator: (value) => {\n          const port = parseInt(value)\n          if (!port || port < 1 || port > 65535) {\n            return '请输入有效的端口号(1-65535)'\n          }\n          return true\n        }\n      }).then(({ value }) => {\n        const routeConfig = {\n          containerId: row.id,\n          servicePort: parseInt(value)\n        }\n        \n        this.updateContainerRoute(routeConfig).then((response) => {\n          if (response.code === 20000 && response.data && response.data.accessUrl) {\n            this.$message.success(`路由更新成功！新地址：${response.data.accessUrl}`)\n            this.getList()\n          } else {\n            this.$message.error(response.message || '更新失败')\n          }\n        }).catch(() => {\n          // 错误处理已在拦截器中处理\n        })\n      }).catch(() => {\n        // 取消操作\n      })\n    },\n    \n    // 带Traefik的部署方法\n    async deployWithTraefik(deployData) {\n      return deployContainerWithTraefik(deployData)\n    },\n    \n    // 配置容器路由方法\n    async configContainerRoute(routeConfig) {\n      return configContainerRoute(routeConfig)\n    },\n    \n    // 更新容器路由方法\n    async updateContainerRoute(routeConfig) {\n      return updateContainerRoute(routeConfig)\n    },\n    \n    // 加载镜像列表\n    async loadImageList() {\n      this.imageListLoading = true\n      try {\n        const response = await getImageList()\n        if (response.code === 20000) {\n          // 处理分页数据结构\n          if (response.data && response.data.list) {\n            // 如果返回的是分页结构\n            this.imageList = response.data.list.map(image => {\n              return {\n                ...image,\n                // 确保 sizeMb 是数字类型\n                sizeMb: typeof image.sizeMb === 'string' ? parseInt(image.sizeMb) : image.sizeMb\n              }\n            })\n          } else if (Array.isArray(response.data)) {\n            // 如果返回的是直接数组\n            this.imageList = response.data.map(image => {\n              return {\n                ...image,\n                sizeMb: typeof image.sizeMb === 'string' ? parseInt(image.sizeMb) : image.sizeMb\n              }\n            })\n          } else {\n            this.imageList = []\n          }\n          \n          if (this.imageList.length === 0) {\n            this.$message.info('当前没有可用的镜像，请先构建或导入镜像')\n          }\n        } else {\n          this.$message.error('获取镜像列表失败：' + (response.message || '未知错误'))\n          this.imageList = []\n        }\n      } catch (error) {\n        console.error('加载镜像列表失败:', error)\n        this.$message.error('加载镜像列表失败')\n        this.imageList = []\n      } finally {\n        this.imageListLoading = false\n      }\n    },\n    \n    // 处理镜像选择变化\n    handleImageChange(imageId) {\n      if (imageId) {\n        this.selectedImage = this.imageList.find(img => img.id === imageId)\n        if (this.selectedImage) {\n          // 自动填充镜像名称和标签\n          this.deployForm.imageName = this.selectedImage.name\n          this.deployForm.imageTag = this.selectedImage.tag\n          \n          // 智能生成容器名称（如果当前为空）\n          if (!this.deployForm.containerName) {\n            this.suggestContainerName(this.selectedImage.name, this.selectedImage.tag)\n          }\n          \n          // 根据镜像类型智能推荐端口\n          this.suggestServicePort(this.selectedImage.name)\n        }\n      } else {\n        this.selectedImage = null\n        this.deployForm.imageName = ''\n        this.deployForm.imageTag = 'latest'\n      }\n    },\n    \n    // 智能生成容器名称\n    suggestContainerName(imageName, imageTag) {\n      // 移除镜像名称中的特殊字符，生成简洁的名称\n      let baseName = imageName.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase()\n      \n      // 如果标签不是latest，则加入标签\n      if (imageTag && imageTag !== 'latest') {\n        baseName += '-' + imageTag.replace(/[^a-zA-Z0-9]/g, '-')\n      }\n      \n      // 添加时间戳保证唯一性\n      const timestamp = Date.now().toString().slice(-6)\n      this.deployForm.containerName = `${baseName}-${timestamp}`\n    },\n    \n    // 根据镜像类型智能推荐端口\n    suggestServicePort(imageName) {\n      const portMap = {\n        'nginx': 80,\n        'apache': 80,\n        'httpd': 80,\n        'mysql': 3306,\n        'mariadb': 3306,\n        'postgres': 5432,\n        'postgresql': 5432,\n        'redis': 6379,\n        'mongodb': 27017,\n        'mongo': 27017,\n        'tomcat': 8080,\n        'node': 3000,\n        'spring': 8080,\n        'java': 8080\n      }\n      \n      // 查找匹配的镜像类型\n      for (const [key, port] of Object.entries(portMap)) {\n        if (imageName.toLowerCase().includes(key)) {\n          this.deployForm.servicePort = port\n          break\n        }\n      }\n    },\n    \n    // 格式化镜像大小\n    formatImageSize(sizeMb) {\n      if (!sizeMb || sizeMb === 0) return '-'\n      \n      // 确保是数字类型\n      const size = typeof sizeMb === 'string' ? parseInt(sizeMb) : sizeMb\n      if (isNaN(size)) return '-'\n      \n      // 如果已经是MB单位，直接显示\n      if (size < 1024) {\n        return `${size} MB`\n      }\n      \n      // 转换为GB\n      const sizeGb = size / 1024\n      return `${sizeGb.toFixed(1)} GB`\n    },\n    \n    // 获取分布策略显示名称\n    getStrategyDisplayName(strategy) {\n      const strategyMap = {\n        'SPREAD_ACROSS_NODES': '跨节点分散',\n        'WORKER_NODES_ONLY': '仅Worker节点',\n        'MANAGER_NODES_ONLY': '仅Manager节点',\n        'BALANCED': '平衡分布',\n        'SPREAD_ACROSS_ZONES': '跨可用区分散'\n      }\n      return strategyMap[strategy] || strategy\n    },\n    \n    // 获取推荐的分布策略\n    async getRecommendedDistributionStrategy(replicas) {\n      try {\n        const response = await getRecommendedStrategy(replicas)\n        if (response.code === 20000 && response.data) {\n          return response.data.recommendedStrategy\n        }\n      } catch (error) {\n        console.warn('获取推荐分布策略失败:', error)\n      }\n      return 'SPREAD_ACROSS_NODES' // 默认值\n    },\n    \n    // === HSM设备相关方法 ===\n    \n    // 处理HSM开关变化\n    handleHsmToggle(enabled) {\n      if (enabled) {\n        // 启用HSM时加载设备列表\n        this.loadHsmDeviceList()\n      } else {\n        // 禁用HSM时清空选择\n        this.deployForm.hsmDeviceId = null\n        this.selectedHsmDevice = null\n      }\n    },\n    \n    // 加载HSM设备列表\n    async loadHsmDeviceList() {\n      this.hsmDeviceListLoading = true\n      try {\n        const response = await getAvailableHsmDevices({\n          status: 'running'\n        })\n        \n        if (response.code === 20000) {\n          if (response.data && response.data.list) {\n            this.hsmDeviceList = response.data.list\n          } else if (Array.isArray(response.data)) {\n            this.hsmDeviceList = response.data\n          } else {\n            this.hsmDeviceList = []\n          }\n          \n          if (this.hsmDeviceList.length === 0) {\n            this.$message.info('当前没有可用的HSM设备')\n          }\n        } else {\n          this.$message.error('获取HSM设备列表失败：' + (response.message || '未知错误'))\n          this.hsmDeviceList = []\n        }\n      } catch (error) {\n        console.error('加载HSM设备列表失败:', error)\n        this.$message.error('加载HSM设备列表失败')\n        this.hsmDeviceList = []\n      } finally {\n        this.hsmDeviceListLoading = false\n      }\n    },\n    \n    // 处理HSM设备选择变化\n    handleHsmDeviceChange(deviceId) {\n      if (deviceId) {\n        this.selectedHsmDevice = this.hsmDeviceList.find(device => device.deviceId === deviceId)\n        if (this.selectedHsmDevice) {\n          // 可以在这里加载更详细的设备信息\n          this.loadHsmDeviceDetail(deviceId)\n        }\n      } else {\n        this.selectedHsmDevice = null\n      }\n    },\n    \n    // 加载HSM设备详细信息\n    async loadHsmDeviceDetail(deviceId) {\n      try {\n        const response = await getHsmDeviceDetail(deviceId)\n        if (response.code === 20000 && response.data) {\n          this.selectedHsmDevice = response.data\n        }\n      } catch (error) {\n        console.warn('获取HSM设备详细信息失败:', error)\n      }\n    },\n    \n    // 带HSM配置的部署方法\n    async deployWithHsm(deployData) {\n      return deployContainerWithHsm(deployData)\n    }\n  },\n  mounted() {\n    this.getList()\n    // 预加载镜像列表，提升用户体验\n    this.loadImageList()\n    // 预加载HSM设备列表\n    this.loadHsmDeviceList()\n  },\n  computed: {\n    ...mapGetters([\n      'user'\n    ])\n  },\n  watch: {\n    // 监听副本数变化，自动更新推荐的分布策略\n    'deployForm.replicas'(newReplicas, oldReplicas) {\n      if (newReplicas > 1 && newReplicas !== oldReplicas) {\n        this.getRecommendedDistributionStrategy(newReplicas).then(strategy => {\n          this.deployForm.distributionStrategy = strategy\n        })\n      } else if (newReplicas === 1) {\n        // 单副本时不需要分布策略\n        this.deployForm.distributionStrategy = 'BALANCED'\n      }\n    },\n    // 监听扩缩容副本数变化\n    'scaleForm.replicas'(newReplicas, oldReplicas) {\n      if (newReplicas > 1 && newReplicas !== oldReplicas) {\n        this.getRecommendedDistributionStrategy(newReplicas).then(strategy => {\n          this.scaleForm.distributionStrategy = strategy\n        })\n      } else if (newReplicas === 1) {\n        this.scaleForm.distributionStrategy = 'BALANCED'\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.filter-inner {\n  display: flex;\n  align-items: center;\n  flex-wrap: wrap;\n  \n  .el-form-item {\n    margin-bottom: 10px;\n    margin-right: 15px;\n  }\n}\n\n.pull-right {\n  float: right;\n}\n\n.clearfix::after {\n  content: \"\";\n  display: table;\n  clear: both;\n}\n\n.form-help {\n  font-size: 12px;\n  color: #909399;\n  margin-top: 4px;\n}\n\n.el-link {\n  font-size: 12px;\n  max-width: 150px;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  display: inline-block;\n}\n\n.image-info {\n  background-color: #f5f7fa;\n  padding: 12px;\n  border-radius: 4px;\n  border: 1px solid #e4e7ed;\n  \n  p {\n    margin: 4px 0;\n    font-size: 13px;\n    \n    strong {\n      color: #303133;\n      font-weight: 500;\n    }\n  }\n}\n\n.device-info {\n  background-color: #f0f9ff;\n  padding: 12px;\n  border-radius: 4px;\n  border: 1px solid #a7f3d0;\n  \n  p {\n    margin: 4px 0;\n    font-size: 13px;\n    \n    strong {\n      color: #303133;\n      font-weight: 500;\n    }\n  }\n}\n\n.strategy-info {\n  font-size: 11px;\n  color: #909399;\n  margin-top: 2px;\n}\n\n.hsm-info {\n  font-size: 11px;\n  color: #67c23a;\n  margin-top: 2px;\n}\n</style>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoUA,SAAAA,UAAA;AACA,OAAAC,UAAA;AACA,SACAC,gBAAA,EACAC,eAAA,EACAC,0BAAA,EACAC,2BAAA,EACAC,sBAAA,EACAC,cAAA,EACAC,aAAA,EACAC,gBAAA,EACAC,eAAA,EACAC,gBAAA,EACAC,mBAAA,EACAC,cAAA,EACAC,oBAAA,EACAC,oBAAA,IAAAA,qBAAA,EACAC,oBAAA,IAAAA,qBAAA,QACA;AACA,SAAAC,YAAA;AACA,SAAAC,uBAAA,EAAAC,aAAA;AACA,SAAAC,sBAAA,EAAAC,kBAAA,EAAAC,sBAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAvB,UAAA,EAAAA;EACA;EACAwB,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA;MACAC,OAAA;MACAC,KAAA;MACAC,SAAA;QACAC,IAAA;QACAC,QAAA;QACAC,aAAA,EAAAC,SAAA;QACAC,MAAA,EAAAD;MACA;MACAE,iBAAA;MACAC,aAAA;MACAC,gBAAA;MACAC,mBAAA;MACAC,UAAA;QACAP,aAAA;QACAQ,OAAA;QACAC,SAAA;QACAC,QAAA;QACAC,WAAA;QACAC,QAAA;QACAC,oBAAA;QACAC,aAAA;QACAC,SAAA;QACAC,WAAA;QACAC,UAAA;QACAC,aAAA;QACAC,eAAA;MACA;MACAC,WAAA;QACAF,aAAA;UAAAG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAvB,aAAA;UAAAqB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAf,OAAA;UAAAa,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAZ,WAAA;UAAAU,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MACA;MACAC,kBAAA;MACAC,SAAA;QACAC,EAAA;QACAd,QAAA;QACAC,oBAAA;MACA;MACAc,UAAA;QACAf,QAAA;UAAAS,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MACA;MACA;MACAK,SAAA;MACAC,gBAAA;MACAC,aAAA;MACA;MACAC,aAAA;MACAC,oBAAA;MACAC,iBAAA;MACA;MACAC,UAAA;MACAC,iBAAA;IACA;EACA;EACAC,OAAA;IACAC,YAAA,WAAAA,aAAA;MACA,KAAAC,OAAA;IACA;IACAC,iBAAA,WAAAA,kBAAA;MACA,KAAA1C,SAAA;QACAC,IAAA;QACAC,QAAA;QACAC,aAAA,EAAAC,SAAA;QACAC,MAAA,EAAAD;MACA;MACA,KAAAqC,OAAA;IACA;IACAE,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,KAAA;MACA;MACAC,OAAA,CAAAC,GAAA,OAAAC,aAAA,SAAAC,cAAA,KAAAC,IAAA;QACAL,KAAA,CAAAlC,UAAA;UACAP,aAAA;UACAQ,OAAA;UACAC,SAAA;UACAC,QAAA;UACAC,WAAA;UACAC,QAAA;UACAC,oBAAA;UACAC,aAAA;UACAC,SAAA;UACAC,WAAA;UACAC,UAAA,EAAAwB,KAAA,CAAAM,IAAA,GAAAN,KAAA,CAAAM,IAAA,CAAArB,EAAA;UACAR,aAAA;UACAC,eAAA;QACA;QACAsB,KAAA,CAAAX,aAAA;QACAW,KAAA,CAAAR,iBAAA;QACAQ,KAAA,CAAAnC,mBAAA;QACAmC,KAAA,CAAAO,SAAA;UACAP,KAAA,CAAAQ,KAAA,eAAAC,aAAA;QACA;MACA;IACA;IACAC,sBAAA,WAAAA,uBAAA;MAAA,IAAAC,MAAA;MACA,KAAAH,KAAA,eAAAI,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA;UACA,IAAAF,MAAA,CAAA7C,UAAA,CAAAQ,SAAA,KAAAqC,MAAA,CAAA7C,UAAA,CAAAS,WAAA;YACAoC,MAAA,CAAAG,QAAA,CAAAC,KAAA;YACA;UACA;UAEA,IAAAC,UAAA,GAAAC,aAAA,KAAAN,MAAA,CAAA7C,UAAA;;UAEA;UACA,IAAA6C,MAAA,CAAAtB,aAAA;YACA2B,UAAA,CAAAhD,SAAA,GAAA2C,MAAA,CAAAtB,aAAA,CAAArB,SAAA;YACAgD,UAAA,CAAA/C,QAAA,GAAA0C,MAAA,CAAAtB,aAAA,CAAApB,QAAA;UACA;;UAEA;UACA,IAAA0C,MAAA,CAAA7C,UAAA,CAAAQ,SAAA,IAAAqC,MAAA,CAAAnB,iBAAA;YACAwB,UAAA,CAAAE,eAAA;cACAC,gBAAA;cAAA;cACAC,WAAA,EAAAT,MAAA,CAAAnB,iBAAA,CAAA6B,QAAA;cACAC,aAAA,EAAAX,MAAA,CAAAnB,iBAAA,CAAA+B,UAAA;cACAC,YAAA,EAAAb,MAAA,CAAAnB,iBAAA,CAAAiC,SAAA;cACAC,UAAA,EAAAf,MAAA,CAAAnB,iBAAA,CAAAtB,WAAA;cACAyD,UAAA,EAAAhB,MAAA,CAAAnB,iBAAA,CAAAmC,UAAA;cACAC,UAAA,EAAAjB,MAAA,CAAAnB,iBAAA,CAAAoC,UAAA;cACAC,UAAA,EAAAlB,MAAA,CAAAnB,iBAAA,CAAAqC,UAAA;cACAC,aAAA,EAAAnB,MAAA,CAAAnB,iBAAA,CAAAuC,QAAA;cACAC,cAAA;YACA;UACA;;UAEA;UACA,IAAAC,QAAA;UACA,IAAAtB,MAAA,CAAA7C,UAAA,CAAAQ,SAAA;YACA;YACA4D,OAAA,CAAAC,GAAA;YACAF,QAAA,GAAAtB,MAAA,CAAAyB,aAAA;UACA,WAAApB,UAAA,CAAA7C,QAAA,QAAA6C,UAAA,CAAA5C,oBAAA;YACA;YACA8D,OAAA,CAAAC,GAAA;YACAF,QAAA,GAAArG,2BAAA;UACA,WAAAoF,UAAA,CAAA3C,aAAA;YACA;YACA6D,OAAA,CAAAC,GAAA;YACAF,QAAA,GAAAtB,MAAA,CAAA0B,iBAAA;UACA;YACA;YACAH,OAAA,CAAAC,GAAA;YACAF,QAAA,GAAAvG,eAAA;UACA;UAEAuG,QAAA,CAAAjB,UAAA,EAAAX,IAAA,WAAAiC,QAAA;YACA3B,MAAA,CAAA9C,mBAAA;YACA,IAAAyE,QAAA,CAAAC,IAAA;cACA,IAAAD,QAAA,CAAAtF,IAAA;gBACA;gBACA,IAAA6B,OAAA;gBAEA,IAAAyD,QAAA,CAAAtF,IAAA,CAAAwF,SAAA;kBACA3D,OAAA,qCAAA4D,MAAA,CAAAH,QAAA,CAAAtF,IAAA,CAAAwF,SAAA;gBACA,WAAAF,QAAA,CAAAtF,IAAA,CAAA0F,SAAA,IAAAJ,QAAA,CAAAtF,IAAA,CAAA0F,SAAA,CAAAF,SAAA;kBACA3D,OAAA,qCAAA4D,MAAA,CAAAH,QAAA,CAAAtF,IAAA,CAAA0F,SAAA,CAAAF,SAAA;gBACA;gBAEA,IAAAF,QAAA,CAAAtF,IAAA,CAAAoB,oBAAA;kBACAS,OAAA,2CAAA4D,MAAA,CAAA9B,MAAA,CAAAgC,sBAAA,CAAAL,QAAA,CAAAtF,IAAA,CAAAoB,oBAAA;gBACA;gBAEA,IAAAkE,QAAA,CAAAtF,IAAA,CAAA4F,aAAA;kBACA/D,OAAA,kCAAA4D,MAAA,CAAA9B,MAAA,CAAAnB,iBAAA,CAAA+B,UAAA;gBACA;gBAEAZ,MAAA,CAAAG,QAAA,CAAA+B,OAAA,CAAAhE,OAAA;cACA;gBACA8B,MAAA,CAAAG,QAAA,CAAA+B,OAAA;cACA;cACAlC,MAAA,CAAAd,OAAA;YACA;cACAc,MAAA,CAAAG,QAAA,CAAAC,KAAA,CAAAuB,QAAA,CAAAzD,OAAA;YACA;UACA,GAAAiE,KAAA;YACA;UAAA,CACA;QACA;MACA;IACA;IAEA;IACA1C,cAAA,WAAAA,eAAA;MAAA,IAAA2C,MAAA;MAAA,OAAAC,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAAC,QAAA;QAAA,IAAAC,IAAA,EAAApG,IAAA,EAAAqG,EAAA;QAAA,OAAAJ,YAAA,GAAAK,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA;YAAA;cACAT,MAAA,CAAArD,iBAAA;cAAA6D,QAAA,CAAAE,CAAA;cAAA,MAIAV,MAAA,CAAAzC,IAAA,IAAAoD,KAAA,CAAAC,OAAA,CAAAZ,MAAA,CAAAzC,IAAA,CAAAsD,KAAA,KAAAb,MAAA,CAAAzC,IAAA,CAAAsD,KAAA,CAAAC,QAAA;gBAAAN,QAAA,CAAAC,CAAA;gBAAA;cAAA;cAAAD,QAAA,CAAAC,CAAA;cAAA,OACA9G,aAAA;YAAA;cAAA0G,IAAA,GAAAG,QAAA,CAAAO,CAAA;cAAAP,QAAA,CAAAC,CAAA;cAAA;YAAA;cAAAD,QAAA,CAAAC,CAAA;cAAA,OAEA/G,uBAAA;YAAA;cAAA2G,IAAA,GAAAG,QAAA,CAAAO,CAAA;YAAA;cAEA,IAAAV,IAAA,IAAAA,IAAA,CAAAb,IAAA;gBACAvF,IAAA,GAAA0G,KAAA,CAAAC,OAAA,CAAAP,IAAA,CAAApG,IAAA,IAAAoG,IAAA,CAAApG,IAAA,GAAAoG,IAAA,CAAApG,IAAA,IAAAoG,IAAA,CAAApG,IAAA,CAAA+G,IAAA,GAAAX,IAAA,CAAApG,IAAA,CAAA+G,IAAA;gBACAhB,MAAA,CAAAtD,UAAA,GAAAzC,IAAA,CAAAgH,GAAA,WAAAC,IAAA;kBACA,IAAAA,IAAA,CAAAC,KAAA,IAAAD,IAAA,CAAAE,KAAA,KAAA3G,SAAA,SAAAyG,IAAA;kBACA;oBAAAC,KAAA,EAAAD,IAAA,CAAAnH,IAAA,IAAAmH,IAAA,CAAAvF,eAAA,IAAAuF,IAAA,CAAAC,KAAA;oBAAAC,KAAA,EAAAC,MAAA,CAAAH,IAAA,CAAAhF,EAAA,IAAAgF,IAAA,CAAAE,KAAA;kBAAA;gBACA;cACA;gBACApB,MAAA,CAAAtD,UAAA;cACA;cAAA8D,QAAA,CAAAC,CAAA;cAAA;YAAA;cAAAD,QAAA,CAAAE,CAAA;cAAAJ,EAAA,GAAAE,QAAA,CAAAO,CAAA;cAEAf,MAAA,CAAAtD,UAAA;YAAA;cAAA8D,QAAA,CAAAE,CAAA;cAEAV,MAAA,CAAArD,iBAAA;cAAA,OAAA6D,QAAA,CAAAc,CAAA;YAAA;cAAA,OAAAd,QAAA,CAAAe,CAAA;UAAA;QAAA,GAAAnB,OAAA;MAAA;IAEA;IACAoB,oBAAA,WAAAA,qBAAAC,GAAA;MAAA,IAAAC,MAAA;MACA3I,cAAA,CAAA0I,GAAA,CAAAvF,EAAA,EAAAoB,IAAA;QACAoE,MAAA,CAAA3D,QAAA,CAAA+B,OAAA;QACA4B,MAAA,CAAA5E,OAAA;MACA;IACA;IACA6E,mBAAA,WAAAA,oBAAAF,GAAA;MAAA,IAAAG,MAAA;MACA5I,aAAA,CAAAyI,GAAA,CAAAvF,EAAA,EAAAoB,IAAA;QACAsE,MAAA,CAAA7D,QAAA,CAAA+B,OAAA;QACA8B,MAAA,CAAA9E,OAAA;MACA;IACA;IACA+E,sBAAA,WAAAA,uBAAAJ,GAAA;MAAA,IAAAK,MAAA;MACA7I,gBAAA,CAAAwI,GAAA,CAAAvF,EAAA,EAAAoB,IAAA;QACAwE,MAAA,CAAA/D,QAAA,CAAA+B,OAAA;QACAgC,MAAA,CAAAhF,OAAA;MACA;IACA;IACAiF,qBAAA,WAAAA,sBAAAN,GAAA;MAAA,IAAAO,MAAA;MACA,KAAAC,OAAA,CAAAC,OAAA,+CAAAxC,MAAA,CAAA+B,GAAA,CAAAjH,aAAA;QACA2H,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAA/E,IAAA;QACApE,eAAA,CAAAuI,GAAA,CAAAvF,EAAA,EAAAoB,IAAA;UACA0E,MAAA,CAAAjE,QAAA,CAAA+B,OAAA;UACAkC,MAAA,CAAAlF,OAAA;QACA;MACA,GAAAiD,KAAA;QACA;MAAA,CACA;IACA;IACAuC,oBAAA,WAAAA,qBAAAb,GAAA;MAAA,IAAAc,MAAA;MACA,KAAAtG,SAAA;QACAC,EAAA,EAAAuF,GAAA,CAAAvF,EAAA;QACAd,QAAA,EAAAqG,GAAA,CAAArG,QAAA;QAAA;QACAC,oBAAA,EAAAoG,GAAA,CAAApG,oBAAA;MACA;MACA,KAAAW,kBAAA;MACA,KAAAwB,SAAA;QACA+E,MAAA,CAAA9E,KAAA,cAAAC,aAAA;MACA;IACA;IACA8E,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,MAAA;MACA,KAAAhF,KAAA,cAAAI,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAzE,cAAA,CAAAoJ,MAAA,CAAAxG,SAAA,CAAAC,EAAA,EAAAuG,MAAA,CAAAxG,SAAA,CAAAb,QAAA,EAAAkC,IAAA;YACAmF,MAAA,CAAAzG,kBAAA;YACAyG,MAAA,CAAA1E,QAAA,CAAA+B,OAAA;YACA2C,MAAA,CAAA3F,OAAA;UACA;QACA;MACA;IACA;IACA4F,cAAA,WAAAA,eAAAjB,GAAA;MAAA,IAAAkB,MAAA;MACA,KAAA9H,gBAAA,GAAA4G,GAAA;MACAtI,gBAAA,CAAAsI,GAAA,CAAAvF,EAAA,EAAAoB,IAAA,WAAAiC,QAAA;QACA,IAAAA,QAAA,CAAAC,IAAA;UACAmD,MAAA,CAAA/H,aAAA,GAAA2E,QAAA,CAAAtF,IAAA;UACA0I,MAAA,CAAAhI,iBAAA;QACA;MACA;IACA;IACAiI,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,MAAA;MACAzJ,mBAAA,GAAAkE,IAAA;QACAuF,MAAA,CAAA9E,QAAA,CAAA+B,OAAA;QACA+C,MAAA,CAAA/F,OAAA;MACA;IACA;IACAA,OAAA,WAAAA,QAAA;MAAA,IAAAgG,OAAA;MACA,KAAA3I,OAAA;MACAzB,gBAAA,MAAA2B,SAAA,EAAAiD,IAAA,WAAAiC,QAAA;QACA,IAAAA,QAAA,CAAAC,IAAA;UACAsD,OAAA,CAAA5I,SAAA,GAAAqF,QAAA,CAAAtF,IAAA,CAAA+G,IAAA;UACA8B,OAAA,CAAA1I,KAAA,GAAAmF,QAAA,CAAAtF,IAAA,CAAA8I,UAAA;QACA;QACAD,OAAA,CAAA3I,OAAA;MACA,GAAA4F,KAAA;QACA+C,OAAA,CAAA3I,OAAA;MACA;IACA;IACA;IACA6I,WAAA,WAAAA,YAAAC,YAAA;MACA,KAAAA,YAAA;MACA;QACA,IAAAC,KAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAH,YAAA;QACA,IAAAtC,KAAA,CAAAC,OAAA,CAAAsC,KAAA,KAAAA,KAAA,CAAAG,MAAA;UACA,OAAAH,KAAA,CAAAjC,GAAA,WAAAP,CAAA;YAAA,UAAAhB,MAAA,CAAAgB,CAAA,CAAA4C,QAAA,aAAA5D,MAAA,CAAAgB,CAAA,CAAA6C,aAAA,OAAA7D,MAAA,CAAAgB,CAAA,CAAA8C,QAAA;UAAA,GAAAC,IAAA;QACA;QACA;MACA,SAAAC,CAAA;QACA,OAAAT,YAAA;MACA;IACA;IACA;IACAU,UAAA,WAAAA,WAAAC,QAAA;MACA,KAAAA,QAAA;MACA,WAAAC,IAAA,CAAAD,QAAA,EAAAE,cAAA;IACA;IAEA;IACAC,eAAA,WAAAA,gBAAAC,IAAA;MAAA,IAAAC,OAAA;MACA,IAAAC,SAAA,CAAAC,SAAA;QACAD,SAAA,CAAAC,SAAA,CAAAC,SAAA,CAAAJ,IAAA,EAAA1G,IAAA;UACA2G,OAAA,CAAAlG,QAAA,CAAA+B,OAAA;QACA;MACA;QACA;QACA,IAAAuE,QAAA,GAAAC,QAAA,CAAAC,aAAA;QACAF,QAAA,CAAAjD,KAAA,GAAA4C,IAAA;QACAM,QAAA,CAAAE,IAAA,CAAAC,WAAA,CAAAJ,QAAA;QACAA,QAAA,CAAAK,MAAA;QACAJ,QAAA,CAAAK,WAAA;QACAL,QAAA,CAAAE,IAAA,CAAAI,WAAA,CAAAP,QAAA;QACA,KAAAtG,QAAA,CAAA+B,OAAA;MACA;IACA;IAEA;IACA+E,iBAAA,WAAAA,kBAAApD,GAAA;MAAA,IAAAqD,OAAA;MACA,KAAAC,OAAA;QACA5C,iBAAA;QACAC,gBAAA;QACA4C,UAAA;QACAC,cAAA,WAAAA,eAAA7D,KAAA;UACA,IAAA8D,IAAA,GAAAC,QAAA,CAAA/D,KAAA;UACA,KAAA8D,IAAA,IAAAA,IAAA,QAAAA,IAAA;YACA;UACA;UACA;QACA;MACA,GAAA5H,IAAA,WAAA8H,IAAA;QAAA,IAAAhE,KAAA,GAAAgE,IAAA,CAAAhE,KAAA;QACA,IAAAiE,WAAA;UACAC,WAAA,EAAA7D,GAAA,CAAAvF,EAAA;UACAf,WAAA,EAAAgK,QAAA,CAAA/D,KAAA;UACA9F,aAAA;QACA;QAEAwJ,OAAA,CAAAvL,oBAAA,CAAA8L,WAAA,EAAA/H,IAAA,WAAAiC,QAAA;UACA,IAAAA,QAAA,CAAAC,IAAA,cAAAD,QAAA,CAAAtF,IAAA,IAAAsF,QAAA,CAAAtF,IAAA,CAAAwF,SAAA;YACAqF,OAAA,CAAA/G,QAAA,CAAA+B,OAAA,4EAAAJ,MAAA,CAAAH,QAAA,CAAAtF,IAAA,CAAAwF,SAAA;YACAqF,OAAA,CAAAhI,OAAA;UACA;YACAgI,OAAA,CAAA/G,QAAA,CAAAC,KAAA,CAAAuB,QAAA,CAAAzD,OAAA;UACA;QACA,GAAAiE,KAAA;UACA;QAAA,CACA;MACA,GAAAA,KAAA;QACA;MAAA,CACA;IACA;IAEA;IACAwF,iBAAA,WAAAA,kBAAA9D,GAAA;MAAA,IAAA+D,OAAA;MACA,KAAAT,OAAA;QACA5C,iBAAA;QACAC,gBAAA;QACA6C,cAAA,WAAAA,eAAA7D,KAAA;UACA,IAAA8D,IAAA,GAAAC,QAAA,CAAA/D,KAAA;UACA,KAAA8D,IAAA,IAAAA,IAAA,QAAAA,IAAA;YACA;UACA;UACA;QACA;MACA,GAAA5H,IAAA,WAAAmI,KAAA;QAAA,IAAArE,KAAA,GAAAqE,KAAA,CAAArE,KAAA;QACA,IAAAiE,WAAA;UACAC,WAAA,EAAA7D,GAAA,CAAAvF,EAAA;UACAf,WAAA,EAAAgK,QAAA,CAAA/D,KAAA;QACA;QAEAoE,OAAA,CAAAhM,oBAAA,CAAA6L,WAAA,EAAA/H,IAAA,WAAAiC,QAAA;UACA,IAAAA,QAAA,CAAAC,IAAA,cAAAD,QAAA,CAAAtF,IAAA,IAAAsF,QAAA,CAAAtF,IAAA,CAAAwF,SAAA;YACA+F,OAAA,CAAAzH,QAAA,CAAA+B,OAAA,sEAAAJ,MAAA,CAAAH,QAAA,CAAAtF,IAAA,CAAAwF,SAAA;YACA+F,OAAA,CAAA1I,OAAA;UACA;YACA0I,OAAA,CAAAzH,QAAA,CAAAC,KAAA,CAAAuB,QAAA,CAAAzD,OAAA;UACA;QACA,GAAAiE,KAAA;UACA;QAAA,CACA;MACA,GAAAA,KAAA;QACA;MAAA,CACA;IACA;IAEA;IACAT,iBAAA,WAAAA,kBAAArB,UAAA;MAAA,OAAAgC,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAAuF,SAAA;QAAA,OAAAxF,YAAA,GAAAK,CAAA,WAAAoF,SAAA;UAAA,kBAAAA,SAAA,CAAAlF,CAAA;YAAA;cAAA,OAAAkF,SAAA,CAAApE,CAAA,IACA3I,0BAAA,CAAAqF,UAAA;UAAA;QAAA,GAAAyH,QAAA;MAAA;IACA;IAEA;IACAnM,oBAAA,WAAAA,qBAAA8L,WAAA;MAAA,OAAApF,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAAyF,SAAA;QAAA,OAAA1F,YAAA,GAAAK,CAAA,WAAAsF,SAAA;UAAA,kBAAAA,SAAA,CAAApF,CAAA;YAAA;cAAA,OAAAoF,SAAA,CAAAtE,CAAA,IACAhI,qBAAA,CAAA8L,WAAA;UAAA;QAAA,GAAAO,QAAA;MAAA;IACA;IAEA;IACApM,oBAAA,WAAAA,qBAAA6L,WAAA;MAAA,OAAApF,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAA2F,SAAA;QAAA,OAAA5F,YAAA,GAAAK,CAAA,WAAAwF,SAAA;UAAA,kBAAAA,SAAA,CAAAtF,CAAA;YAAA;cAAA,OAAAsF,SAAA,CAAAxE,CAAA,IACA/H,qBAAA,CAAA6L,WAAA;UAAA;QAAA,GAAAS,QAAA;MAAA;IACA;IAEA;IACA1I,aAAA,WAAAA,cAAA;MAAA,IAAA4I,OAAA;MAAA,OAAA/F,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAA8F,SAAA;QAAA,IAAA1G,QAAA,EAAA2G,GAAA;QAAA,OAAAhG,YAAA,GAAAK,CAAA,WAAA4F,SAAA;UAAA,kBAAAA,SAAA,CAAA1F,CAAA;YAAA;cACAuF,OAAA,CAAA3J,gBAAA;cAAA8J,SAAA,CAAAzF,CAAA;cAAAyF,SAAA,CAAA1F,CAAA;cAAA,OAEAhH,YAAA;YAAA;cAAA8F,QAAA,GAAA4G,SAAA,CAAApF,CAAA;cACA,IAAAxB,QAAA,CAAAC,IAAA;gBACA;gBACA,IAAAD,QAAA,CAAAtF,IAAA,IAAAsF,QAAA,CAAAtF,IAAA,CAAA+G,IAAA;kBACA;kBACAgF,OAAA,CAAA5J,SAAA,GAAAmD,QAAA,CAAAtF,IAAA,CAAA+G,IAAA,CAAAC,GAAA,WAAAmF,KAAA;oBACA,OAAAlI,aAAA,CAAAA,aAAA,KACAkI,KAAA;sBACA;sBACAC,MAAA,SAAAD,KAAA,CAAAC,MAAA,gBAAAlB,QAAA,CAAAiB,KAAA,CAAAC,MAAA,IAAAD,KAAA,CAAAC;oBAAA;kBAEA;gBACA,WAAA1F,KAAA,CAAAC,OAAA,CAAArB,QAAA,CAAAtF,IAAA;kBACA;kBACA+L,OAAA,CAAA5J,SAAA,GAAAmD,QAAA,CAAAtF,IAAA,CAAAgH,GAAA,WAAAmF,KAAA;oBACA,OAAAlI,aAAA,CAAAA,aAAA,KACAkI,KAAA;sBACAC,MAAA,SAAAD,KAAA,CAAAC,MAAA,gBAAAlB,QAAA,CAAAiB,KAAA,CAAAC,MAAA,IAAAD,KAAA,CAAAC;oBAAA;kBAEA;gBACA;kBACAL,OAAA,CAAA5J,SAAA;gBACA;gBAEA,IAAA4J,OAAA,CAAA5J,SAAA,CAAAiH,MAAA;kBACA2C,OAAA,CAAAjI,QAAA,CAAAuI,IAAA;gBACA;cACA;gBACAN,OAAA,CAAAjI,QAAA,CAAAC,KAAA,gBAAAuB,QAAA,CAAAzD,OAAA;gBACAkK,OAAA,CAAA5J,SAAA;cACA;cAAA+J,SAAA,CAAA1F,CAAA;cAAA;YAAA;cAAA0F,SAAA,CAAAzF,CAAA;cAAAwF,GAAA,GAAAC,SAAA,CAAApF,CAAA;cAEA5B,OAAA,CAAAnB,KAAA,cAAAkI,GAAA;cACAF,OAAA,CAAAjI,QAAA,CAAAC,KAAA;cACAgI,OAAA,CAAA5J,SAAA;YAAA;cAAA+J,SAAA,CAAAzF,CAAA;cAEAsF,OAAA,CAAA3J,gBAAA;cAAA,OAAA8J,SAAA,CAAA7E,CAAA;YAAA;cAAA,OAAA6E,SAAA,CAAA5E,CAAA;UAAA;QAAA,GAAA0E,QAAA;MAAA;IAEA;IAEA;IACAM,iBAAA,WAAAA,kBAAAvL,OAAA;MACA,IAAAA,OAAA;QACA,KAAAsB,aAAA,QAAAF,SAAA,CAAAoK,IAAA,WAAAC,GAAA;UAAA,OAAAA,GAAA,CAAAvK,EAAA,KAAAlB,OAAA;QAAA;QACA,SAAAsB,aAAA;UACA;UACA,KAAAvB,UAAA,CAAAE,SAAA,QAAAqB,aAAA,CAAAvC,IAAA;UACA,KAAAgB,UAAA,CAAAG,QAAA,QAAAoB,aAAA,CAAAoK,GAAA;;UAEA;UACA,UAAA3L,UAAA,CAAAP,aAAA;YACA,KAAAmM,oBAAA,MAAArK,aAAA,CAAAvC,IAAA,OAAAuC,aAAA,CAAAoK,GAAA;UACA;;UAEA;UACA,KAAAE,kBAAA,MAAAtK,aAAA,CAAAvC,IAAA;QACA;MACA;QACA,KAAAuC,aAAA;QACA,KAAAvB,UAAA,CAAAE,SAAA;QACA,KAAAF,UAAA,CAAAG,QAAA;MACA;IACA;IAEA;IACAyL,oBAAA,WAAAA,qBAAA1L,SAAA,EAAAC,QAAA;MACA;MACA,IAAA2L,QAAA,GAAA5L,SAAA,CAAA6L,OAAA,uBAAAC,WAAA;;MAEA;MACA,IAAA7L,QAAA,IAAAA,QAAA;QACA2L,QAAA,UAAA3L,QAAA,CAAA4L,OAAA;MACA;;MAEA;MACA,IAAAE,SAAA,GAAAnD,IAAA,CAAAoD,GAAA,GAAAC,QAAA,GAAAC,KAAA;MACA,KAAApM,UAAA,CAAAP,aAAA,MAAAkF,MAAA,CAAAmH,QAAA,OAAAnH,MAAA,CAAAsH,SAAA;IACA;IAEA;IACAJ,kBAAA,WAAAA,mBAAA3L,SAAA;MACA,IAAAmM,OAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;;MAEA;MACA,SAAAC,EAAA,MAAAC,eAAA,GAAAC,MAAA,CAAAC,OAAA,CAAAJ,OAAA,GAAAC,EAAA,GAAAC,eAAA,CAAAjE,MAAA,EAAAgE,EAAA;QAAA,IAAAI,kBAAA,GAAAC,cAAA,CAAAJ,eAAA,CAAAD,EAAA;UAAAM,GAAA,GAAAF,kBAAA;UAAAvC,IAAA,GAAAuC,kBAAA;QACA,IAAAxM,SAAA,CAAA8L,WAAA,GAAAjG,QAAA,CAAA6G,GAAA;UACA,KAAA5M,UAAA,CAAAI,WAAA,GAAA+J,IAAA;UACA;QACA;MACA;IACA;IAEA;IACA0C,eAAA,WAAAA,gBAAAvB,MAAA;MACA,KAAAA,MAAA,IAAAA,MAAA;;MAEA;MACA,IAAAwB,IAAA,UAAAxB,MAAA,gBAAAlB,QAAA,CAAAkB,MAAA,IAAAA,MAAA;MACA,IAAAyB,KAAA,CAAAD,IAAA;;MAEA;MACA,IAAAA,IAAA;QACA,UAAAnI,MAAA,CAAAmI,IAAA;MACA;;MAEA;MACA,IAAAE,MAAA,GAAAF,IAAA;MACA,UAAAnI,MAAA,CAAAqI,MAAA,CAAAC,OAAA;IACA;IAEA;IACApI,sBAAA,WAAAA,uBAAAqI,QAAA;MACA,IAAAC,WAAA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,WAAA,CAAAD,QAAA,KAAAA,QAAA;IACA;IAEA;IACAE,kCAAA,WAAAA,mCAAA/M,QAAA;MAAA,OAAA6E,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAAiI,SAAA;QAAA,IAAA7I,QAAA,EAAA8I,GAAA;QAAA,OAAAnI,YAAA,GAAAK,CAAA,WAAA+H,SAAA;UAAA,kBAAAA,SAAA,CAAA7H,CAAA;YAAA;cAAA6H,SAAA,CAAA5H,CAAA;cAAA4H,SAAA,CAAA7H,CAAA;cAAA,OAEA3H,sBAAA,CAAAsC,QAAA;YAAA;cAAAmE,QAAA,GAAA+I,SAAA,CAAAvH,CAAA;cAAA,MACAxB,QAAA,CAAAC,IAAA,cAAAD,QAAA,CAAAtF,IAAA;gBAAAqO,SAAA,CAAA7H,CAAA;gBAAA;cAAA;cAAA,OAAA6H,SAAA,CAAA/G,CAAA,IACAhC,QAAA,CAAAtF,IAAA,CAAAsO,mBAAA;YAAA;cAAAD,SAAA,CAAA7H,CAAA;cAAA;YAAA;cAAA6H,SAAA,CAAA5H,CAAA;cAAA2H,GAAA,GAAAC,SAAA,CAAAvH,CAAA;cAGA5B,OAAA,CAAAqJ,IAAA,gBAAAH,GAAA;YAAA;cAAA,OAAAC,SAAA,CAAA/G,CAAA,IAEA;UAAA;QAAA,GAAA6G,QAAA;MAAA;IACA;IAEA;IAEA;IACAK,eAAA,WAAAA,gBAAAC,OAAA;MACA,IAAAA,OAAA;QACA;QACA,KAAAC,iBAAA;MACA;QACA;QACA,KAAA5N,UAAA,CAAAS,WAAA;QACA,KAAAiB,iBAAA;MACA;IACA;IAEA;IACAkM,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,OAAA;MAAA,OAAA3I,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAA0I,SAAA;QAAA,IAAAtJ,QAAA,EAAAuJ,GAAA;QAAA,OAAA5I,YAAA,GAAAK,CAAA,WAAAwI,SAAA;UAAA,kBAAAA,SAAA,CAAAtI,CAAA;YAAA;cACAmI,OAAA,CAAApM,oBAAA;cAAAuM,SAAA,CAAArI,CAAA;cAAAqI,SAAA,CAAAtI,CAAA;cAAA,OAEA7G,sBAAA;gBACAc,MAAA;cACA;YAAA;cAFA6E,QAAA,GAAAwJ,SAAA,CAAAhI,CAAA;cAIA,IAAAxB,QAAA,CAAAC,IAAA;gBACA,IAAAD,QAAA,CAAAtF,IAAA,IAAAsF,QAAA,CAAAtF,IAAA,CAAA+G,IAAA;kBACA4H,OAAA,CAAArM,aAAA,GAAAgD,QAAA,CAAAtF,IAAA,CAAA+G,IAAA;gBACA,WAAAL,KAAA,CAAAC,OAAA,CAAArB,QAAA,CAAAtF,IAAA;kBACA2O,OAAA,CAAArM,aAAA,GAAAgD,QAAA,CAAAtF,IAAA;gBACA;kBACA2O,OAAA,CAAArM,aAAA;gBACA;gBAEA,IAAAqM,OAAA,CAAArM,aAAA,CAAA8G,MAAA;kBACAuF,OAAA,CAAA7K,QAAA,CAAAuI,IAAA;gBACA;cACA;gBACAsC,OAAA,CAAA7K,QAAA,CAAAC,KAAA,mBAAAuB,QAAA,CAAAzD,OAAA;gBACA8M,OAAA,CAAArM,aAAA;cACA;cAAAwM,SAAA,CAAAtI,CAAA;cAAA;YAAA;cAAAsI,SAAA,CAAArI,CAAA;cAAAoI,GAAA,GAAAC,SAAA,CAAAhI,CAAA;cAEA5B,OAAA,CAAAnB,KAAA,iBAAA8K,GAAA;cACAF,OAAA,CAAA7K,QAAA,CAAAC,KAAA;cACA4K,OAAA,CAAArM,aAAA;YAAA;cAAAwM,SAAA,CAAArI,CAAA;cAEAkI,OAAA,CAAApM,oBAAA;cAAA,OAAAuM,SAAA,CAAAzH,CAAA;YAAA;cAAA,OAAAyH,SAAA,CAAAxH,CAAA;UAAA;QAAA,GAAAsH,QAAA;MAAA;IAEA;IAEA;IACAG,qBAAA,WAAAA,sBAAA1K,QAAA;MACA,IAAAA,QAAA;QACA,KAAA7B,iBAAA,QAAAF,aAAA,CAAAiK,IAAA,WAAAyC,MAAA;UAAA,OAAAA,MAAA,CAAA3K,QAAA,KAAAA,QAAA;QAAA;QACA,SAAA7B,iBAAA;UACA;UACA,KAAAyM,mBAAA,CAAA5K,QAAA;QACA;MACA;QACA,KAAA7B,iBAAA;MACA;IACA;IAEA;IACAyM,mBAAA,WAAAA,oBAAA5K,QAAA;MAAA,IAAA6K,OAAA;MAAA,OAAAlJ,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAAiJ,SAAA;QAAA,IAAA7J,QAAA,EAAA8J,GAAA;QAAA,OAAAnJ,YAAA,GAAAK,CAAA,WAAA+I,SAAA;UAAA,kBAAAA,SAAA,CAAA7I,CAAA;YAAA;cAAA6I,SAAA,CAAA5I,CAAA;cAAA4I,SAAA,CAAA7I,CAAA;cAAA,OAEA5G,kBAAA,CAAAyE,QAAA;YAAA;cAAAiB,QAAA,GAAA+J,SAAA,CAAAvI,CAAA;cACA,IAAAxB,QAAA,CAAAC,IAAA,cAAAD,QAAA,CAAAtF,IAAA;gBACAkP,OAAA,CAAA1M,iBAAA,GAAA8C,QAAA,CAAAtF,IAAA;cACA;cAAAqP,SAAA,CAAA7I,CAAA;cAAA;YAAA;cAAA6I,SAAA,CAAA5I,CAAA;cAAA2I,GAAA,GAAAC,SAAA,CAAAvI,CAAA;cAEA5B,OAAA,CAAAqJ,IAAA,mBAAAa,GAAA;YAAA;cAAA,OAAAC,SAAA,CAAA/H,CAAA;UAAA;QAAA,GAAA6H,QAAA;MAAA;IAEA;IAEA;IACA/J,aAAA,WAAAA,cAAApB,UAAA;MAAA,OAAAgC,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAAoJ,SAAA;QAAA,OAAArJ,YAAA,GAAAK,CAAA,WAAAiJ,SAAA;UAAA,kBAAAA,SAAA,CAAA/I,CAAA;YAAA;cAAA,OAAA+I,SAAA,CAAAjI,CAAA,IACAzH,sBAAA,CAAAmE,UAAA;UAAA;QAAA,GAAAsL,QAAA;MAAA;IACA;EACA;EACAE,OAAA,WAAAA,QAAA;IACA,KAAA3M,OAAA;IACA;IACA,KAAAM,aAAA;IACA;IACA,KAAAuL,iBAAA;EACA;EACAe,QAAA,EAAAxL,aAAA,KACA1F,UAAA,EACA,OACA,EACA;EACAmR,KAAA;IACA;IACA,gCAAAC,mBAAAC,WAAA,EAAAC,WAAA;MAAA,IAAAC,OAAA;MACA,IAAAF,WAAA,QAAAA,WAAA,KAAAC,WAAA;QACA,KAAA3B,kCAAA,CAAA0B,WAAA,EAAAvM,IAAA,WAAA2K,QAAA;UACA8B,OAAA,CAAAhP,UAAA,CAAAM,oBAAA,GAAA4M,QAAA;QACA;MACA,WAAA4B,WAAA;QACA;QACA,KAAA9O,UAAA,CAAAM,oBAAA;MACA;IACA;IACA;IACA,+BAAA2O,kBAAAH,WAAA,EAAAC,WAAA;MAAA,IAAAG,OAAA;MACA,IAAAJ,WAAA,QAAAA,WAAA,KAAAC,WAAA;QACA,KAAA3B,kCAAA,CAAA0B,WAAA,EAAAvM,IAAA,WAAA2K,QAAA;UACAgC,OAAA,CAAAhO,SAAA,CAAAZ,oBAAA,GAAA4M,QAAA;QACA;MACA,WAAA4B,WAAA;QACA,KAAA5N,SAAA,CAAAZ,oBAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}