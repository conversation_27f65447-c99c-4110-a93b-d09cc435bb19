{"remainingRequest": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\src\\views\\docker\\container\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\src\\views\\docker\\container\\index.vue", "mtime": 1756805140667}, {"path": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\babel.config.js", "mtime": 1699511114284}, {"path": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1729062151209}, {"path": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1729062151198}, {"path": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1729062151209}, {"path": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1729062152627}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["mapGetters", "Pagination", "getContainerList", "deployContainer", "getRecommendedStrategy", "startContainer", "stopContainer", "restartContainer", "remove<PERSON><PERSON><PERSON>", "getContainerLogs", "syncContainerStatus", "scaleContainer", "updateContainerImage", "configContainerRoute", "updateContainerRoute", "getImageList", "fetchApplicationOptions", "fetchUserApps", "getAvailableHsmDevices", "getHsmDeviceDetail", "name", "components", "data", "tableList", "loading", "total", "list<PERSON>uery", "page", "pageSize", "containerName", "undefined", "status", "logsDialogVisible", "containerLogs", "currentC<PERSON><PERSON>", "deployDialogVisible", "deployForm", "imageId", "imageName", "imageTag", "servicePort", "replicas", "distributionStrategy", "enableTraefik", "enableHsm", "hsmDeviceId", "deployedBy", "applicationId", "applicationName", "deployRules", "required", "message", "trigger", "scaleDialogVisible", "scaleForm", "id", "scaleRules", "imageList", "imageListLoading", "selectedImage", "hsmDeviceList", "hsmDeviceListLoading", "selectedHsmDevice", "appOptions", "appOptionsLoading", "methods", "handleSearch", "getList", "handleResetSearch", "handleDeployContainer", "_this", "Promise", "all", "loadImageList", "loadAppOptions", "then", "user", "$nextTick", "$refs", "clearValidate", "confirmDeployContainer", "_this2", "validate", "valid", "$message", "error", "deployData", "_objectSpread", "hsmDeviceConfig", "encryptorGroupId", "encryptorId", "deviceId", "encryptorName", "deviceName", "serverIpAddr", "ip<PERSON><PERSON><PERSON>", "serverPort", "tcpConnNum", "msgHeadLen", "msgTailLen", "asciiOrEbcdic", "encoding", "dynamicLibPath", "traefikConfig", "enabled", "domain", "concat", "toLowerCase", "replace", "pathPrefix", "httpsEnabled", "deviceResourceConfig", "deviceResourceType", "deviceResourceIds", "allocationType", "priority", "configData", "JSON", "stringify", "console", "log", "hsm", "traefik", "strategy", "response", "code", "accessUrl", "container", "getStrategyDisplayName", "hsmConfigured", "success", "catch", "_this3", "_asyncToGenerator", "_regenerator", "m", "_callee", "resp", "_t", "w", "_context", "n", "p", "Array", "isArray", "roles", "includes", "v", "list", "map", "item", "label", "value", "String", "f", "a", "handleStartContainer", "row", "_this4", "handleStopContainer", "_this5", "handleRestartContainer", "_this6", "handleRemoveContainer", "_this7", "$msgbox", "confirm", "confirmButtonText", "cancelButtonText", "type", "handleScaleContainer", "_this8", "confirmScaleContainer", "_this9", "handleViewLogs", "_this0", "handleSyncStatus", "_this1", "_this10", "totalCount", "formatPorts", "portMappings", "ports", "parse", "length", "hostPort", "containerPort", "protocol", "join", "e", "formatDate", "dateTime", "Date", "toLocaleString", "copyToClipboard", "text", "_this11", "navigator", "clipboard", "writeText", "textArea", "document", "createElement", "body", "append<PERSON><PERSON><PERSON>", "select", "execCommand", "<PERSON><PERSON><PERSON><PERSON>", "handleConfigRoute", "_this12", "$prompt", "inputValue", "inputValidator", "port", "parseInt", "_ref", "routeConfig", "containerId", "handleUpdateRoute", "_this13", "_ref2", "_callee2", "_context2", "_callee3", "_context3", "_this14", "_callee4", "_t2", "_context4", "image", "sizeMb", "info", "handleImageChange", "find", "img", "tag", "suggestContainerName", "suggestServicePort", "baseName", "timestamp", "now", "toString", "slice", "portMap", "_i", "_Object$entries", "Object", "entries", "_Object$entries$_i", "_slicedToArray", "key", "formatImageSize", "size", "isNaN", "sizeGb", "toFixed", "strategyMap", "getRecommendedDistributionStrategy", "_callee5", "_t3", "_context5", "recommendedStrategy", "warn", "handleHsmToggle", "loadHsmDeviceList", "_this15", "_callee6", "_t4", "_context6", "handleHsmDeviceChange", "device", "loadHsmDeviceDetail", "_this16", "_callee7", "_t5", "_context7", "mounted", "computed", "watch", "deployFormReplicas", "newReplicas", "oldReplicas", "_this17", "scaleFormReplicas", "_this18"], "sources": ["src/views/docker/container/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-card shadow=\"never\">\n      <div class=\"filter-container clearfix\">\n        <el-form ref=\"searchForm\" :model=\"listQuery\" inline @submit.native.prevent=\"getList\">\n          <div class=\"filter-inner\">\n            <el-form-item label=\"容器名称\">\n              <el-input v-model.trim=\"listQuery.containerName\" clearable placeholder=\"容器名称\" />\n            </el-form-item>\n            <el-form-item label=\"状态\">\n              <el-select v-model=\"listQuery.status\" clearable placeholder=\"请选择状态\">\n                <el-option label=\"运行中\" value=\"running\" />\n                <el-option label=\"已停止\" value=\"stopped\" />\n                <el-option label=\"已暂停\" value=\"paused\" />\n              </el-select>\n            </el-form-item>\n          </div>\n          <div class=\"pull-right\">\n            <el-button-group>\n              <el-form-item>\n                <el-button type=\"primary\" @click=\"handleSearch\"><i class=\"el-icon-search\" /> 查询</el-button>\n                <el-button type=\"info\" @click=\"handleResetSearch\"><i class=\"el-icon-refresh\" /> 重置</el-button>\n              </el-form-item>\n            </el-button-group>\n          </div>\n        </el-form>\n      </div>\n    </el-card>\n    \n    <el-card shadow=\"never\">\n      <div class=\"filter-container clearfix\">\n        <div class=\"filter-inner\">\n          <el-button type=\"primary\" @click=\"handleDeployContainer\">部署容器</el-button>\n          <el-button type=\"success\" @click=\"handleSyncStatus\">同步状态</el-button>\n        </div>\n      </div>\n      \n      <el-table\n        ref=\"dataTable\"\n        v-loading=\"loading\"\n        :data=\"tableList\"\n        style=\"width: 100%;\"\n      >\n        <el-table-column align=\"center\" label=\"序号\" type=\"index\" width=\"50\" />\n        <el-table-column align=\"center\" label=\"容器ID\" min-width=\"120\" prop=\"id\" />\n        <el-table-column align=\"center\" label=\"容器名称\" min-width=\"150\" prop=\"containerName\" />\n        <el-table-column align=\"center\" label=\"所属应用\" min-width=\"140\">\n          <template v-slot=\"{row}\">\n            <span>{{ row.applicationName || '-' }}</span>\n          </template>\n        </el-table-column>\n        <el-table-column align=\"center\" label=\"镜像\" min-width=\"150\">\n          <template v-slot=\"{row}\">\n            <span>{{ row.imageName }}:{{ row.imageTag || 'latest' }}</span>\n          </template>\n        </el-table-column>\n        <el-table-column align=\"center\" label=\"状态\" min-width=\"100\" prop=\"status\">\n          <template v-slot=\"{row}\">\n            <el-tag v-if=\"row.status === 'running'\" type=\"success\">运行中</el-tag>\n            <el-tag v-else-if=\"row.status === 'stopped'\" type=\"danger\">已停止</el-tag>\n            <el-tag v-else-if=\"row.status === 'paused'\" type=\"warning\">已暂停</el-tag>\n            <el-tag v-else>{{ row.status }}</el-tag>\n          </template>\n        </el-table-column>\n        <el-table-column align=\"center\" label=\"端口\" min-width=\"120\">\n          <template v-slot=\"{row}\">\n            <span>{{ formatPorts(row.portMappings) }}</span>\n          </template>\n        </el-table-column>\n        <el-table-column align=\"center\" label=\"副本信息\" min-width=\"120\">\n          <template v-slot=\"{row}\">\n            <div v-if=\"row.replicas\">\n              <div>副本数：{{ row.replicas || '-' }}</div>\n              <div v-if=\"row.distributionStrategy\" class=\"strategy-info\">\n                分布：{{ getStrategyDisplayName(row.distributionStrategy) }}\n              </div>\n            </div>\n            <span v-else>-</span>\n          </template>\n        </el-table-column>\n        <el-table-column align=\"center\" label=\"访问地址\" min-width=\"180\">\n          <template v-slot=\"{row}\">\n            <div v-if=\"row.accessUrl\">\n              <el-link :href=\"row.accessUrl\" target=\"_blank\" type=\"primary\">\n                {{ row.accessUrl }}\n              </el-link>\n              <el-button \n                type=\"text\" \n                size=\"mini\" \n                @click=\"copyToClipboard(row.accessUrl)\"\n                style=\"margin-left: 5px;\"\n              >\n                <i class=\"el-icon-copy-document\"></i>\n              </el-button>\n            </div>\n            <el-button \n              v-else-if=\"row.status === 'running'\" \n              type=\"text\" \n              size=\"mini\" \n              @click=\"handleConfigRoute(row)\"\n            >\n              配置访问\n            </el-button>\n            <span v-else>-</span>\n          </template>\n        </el-table-column>\n        <el-table-column align=\"center\" label=\"HSM设备\" min-width=\"120\">\n          <template v-slot=\"{row}\">\n            <div v-if=\"row.hsmDeviceId || row.hsmConfigured\">\n              <el-tag type=\"success\" size=\"mini\">已配置</el-tag>\n              <div v-if=\"row.hsmDeviceName\" class=\"hsm-info\">\n                {{ row.hsmDeviceName }}\n              </div>\n            </div>\n            <span v-else>-</span>\n          </template>\n        </el-table-column>\n        <el-table-column align=\"center\" label=\"节点\" min-width=\"100\" prop=\"nodeName\" />\n        <el-table-column align=\"center\" label=\"创建时间\" min-width=\"150\">\n          <template v-slot=\"{row}\">\n            <span>{{ formatDate(row.createTime) }}</span>\n          </template>\n        </el-table-column>\n        <el-table-column align=\"center\" fixed=\"right\" label=\"操作\" width=\"250\">\n          <template v-slot=\"{row}\">\n            <el-button v-if=\"row.status === 'running'\" type=\"text\" @click=\"handleStopContainer(row)\">停止</el-button>\n            <el-button v-else type=\"text\" @click=\"handleStartContainer(row)\">启动</el-button>\n            <el-button type=\"text\" @click=\"handleRestartContainer(row)\">重启</el-button>\n            <el-button type=\"text\" @click=\"handleScaleContainer(row)\">扩缩容</el-button>\n            <el-button type=\"text\" @click=\"handleViewLogs(row)\">日志</el-button>\n            <el-button v-if=\"row.accessUrl\" type=\"text\" @click=\"handleUpdateRoute(row)\">更新路由</el-button>\n            <el-button type=\"text\" @click=\"handleRemoveContainer(row)\">删除</el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n      \n      <pagination\n        v-show=\"total > listQuery.pageSize\"\n        :limit.sync=\"listQuery.pageSize\"\n        :page.sync=\"listQuery.page\"\n        :total=\"total\"\n        layout=\"prev, pager, next\"\n        @pagination=\"getList\"\n      />\n    </el-card>\n    \n    <!-- 部署容器对话框 -->\n    <el-dialog :visible.sync=\"deployDialogVisible\" title=\"部署容器\" width=\"600px\">\n      <el-form ref=\"deployForm\" :model=\"deployForm\" :rules=\"deployRules\" label-width=\"120px\">\n        <el-form-item label=\"关联应用\" prop=\"applicationId\">\n          <el-select v-model=\"deployForm.applicationId\" filterable placeholder=\"请选择应用\" style=\"width: 100%;\" :loading=\"appOptionsLoading\">\n            <el-option v-for=\"opt in appOptions\" :key=\"opt.value\" :label=\"opt.label\" :value=\"String(opt.value)\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"容器名称\" prop=\"containerName\">\n          <el-input v-model.trim=\"deployForm.containerName\" placeholder=\"例如: my-nginx\" />\n        </el-form-item>\n        <el-form-item label=\"镜像选择\" prop=\"imageId\">\n          <el-select \n            v-model=\"deployForm.imageId\" \n            filterable \n            placeholder=\"请选择镜像\"\n            @change=\"handleImageChange\"\n            style=\"width: 100%;\"\n            :loading=\"imageListLoading\"\n            :disabled=\"imageList.length === 0\"\n          >\n            <el-option\n              v-for=\"image in imageList\"\n              :key=\"image.id\"\n              :label=\"`${image.name}:${image.tag}`\"\n              :value=\"image.id\"\n            >\n              <span style=\"float: left\">{{ image.name }}:{{ image.tag }}</span>\n              <span style=\"float: right; color: #8492a6; font-size: 13px\">{{ formatImageSize(image.sizeMb) }}</span>\n            </el-option>\n            <div v-if=\"imageList.length === 0\" slot=\"empty\">\n              <span v-if=\"imageListLoading\">加载中...</span>\n              <span v-else>暂无可用镜像，请先构建或导入镜像</span>\n            </div>\n          </el-select>\n          <div class=\"form-help\">\n            选择已有的Docker镜像进行部署\n            <el-button \n              type=\"text\" \n              size=\"mini\" \n              @click=\"loadImageList\"\n              style=\"margin-left: 8px;\"\n            >\n              <i class=\"el-icon-refresh\"></i> 刷新\n            </el-button>\n          </div>\n        </el-form-item>\n        <el-form-item label=\"镜像信息\" v-if=\"selectedImage\">\n          <div class=\"image-info\">\n            <p><strong>名称：</strong>{{ selectedImage.name }}</p>\n            <p><strong>标签：</strong>{{ selectedImage.tag }}</p>\n            <p><strong>大小：</strong>{{ formatImageSize(selectedImage.sizeMb) }}</p>\n            <p v-if=\"selectedImage.description\"><strong>描述：</strong>{{ selectedImage.description }}</p>\n          </div>\n        </el-form-item>\n        <el-form-item label=\"服务端口\" prop=\"servicePort\">\n          <el-input-number v-model=\"deployForm.servicePort\" placeholder=\"容器内部端口，如: 80\" :min=\"1\" :max=\"65535\" />\n          <div class=\"form-help\">容器内应用监听的端口号</div>\n        </el-form-item>\n        <el-form-item label=\"副本数\" prop=\"replicas\">\n          <el-input-number v-model=\"deployForm.replicas\" :min=\"1\" />\n        </el-form-item>\n        <el-form-item label=\"分布策略\" v-if=\"deployForm.replicas > 1\">\n          <el-select v-model=\"deployForm.distributionStrategy\" placeholder=\"请选择分布策略\" style=\"width: 100%;\">\n            <el-option label=\"跨节点分散（推荐）\" value=\"SPREAD_ACROSS_NODES\" />\n            <el-option label=\"仅Worker节点\" value=\"WORKER_NODES_ONLY\" />\n            <el-option label=\"仅Manager节点\" value=\"MANAGER_NODES_ONLY\" />\n            <el-option label=\"平衡分布\" value=\"BALANCED\" />\n            <el-option label=\"跨可用区分散\" value=\"SPREAD_ACROSS_ZONES\" />\n          </el-select>\n          <div class=\"form-help\">多副本时的部署分布策略，推荐选择跨节点分散</div>\n        </el-form-item>\n        <el-form-item label=\"启用路由\">\n          <el-switch v-model=\"deployForm.enableTraefik\" active-text=\"启用\" inactive-text=\"禁用\" />\n          <div class=\"form-help\">启用后可通过内网地址访问服务</div>\n        </el-form-item>\n        <el-form-item label=\"HSM设备资源\">\n          <el-switch v-model=\"deployForm.enableHsm\" active-text=\"启用\" inactive-text=\"禁用\" @change=\"handleHsmToggle\" />\n          <div class=\"form-help\">启用后可配置HSM加密设备资源</div>\n        </el-form-item>\n        <el-form-item v-if=\"deployForm.enableHsm\" label=\"选择设备\" prop=\"hsmDeviceId\">\n          <el-select \n            v-model=\"deployForm.hsmDeviceId\" \n            filterable \n            placeholder=\"请选择HSM设备\"\n            @change=\"handleHsmDeviceChange\"\n            style=\"width: 100%;\"\n            :loading=\"hsmDeviceListLoading\"\n            :disabled=\"hsmDeviceList.length === 0\"\n          >\n            <el-option\n              v-for=\"device in hsmDeviceList\"\n              :key=\"device.deviceId\"\n              :label=\"device.deviceName\"\n              :value=\"device.deviceId\"\n            >\n              <span style=\"float: left\">{{ device.deviceName }}</span>\n              <span style=\"float: right; color: #8492a6; font-size: 13px\">{{ device.ipAddress }}:{{ device.servicePort }}</span>\n            </el-option>\n            <div v-if=\"hsmDeviceList.length === 0\" slot=\"empty\">\n              <span v-if=\"hsmDeviceListLoading\">加载中...</span>\n              <span v-else>暂无可用设备</span>\n            </div>\n          </el-select>\n          <div class=\"form-help\">\n            选择可用的HSM加密设备\n            <el-button \n              type=\"text\" \n              size=\"mini\" \n              @click=\"loadHsmDeviceList\"\n              style=\"margin-left: 8px;\"\n            >\n              <i class=\"el-icon-refresh\"></i> 刷新\n            </el-button>\n          </div>\n        </el-form-item>\n        <el-form-item v-if=\"deployForm.enableHsm && selectedHsmDevice\" label=\"设备信息\">\n          <div class=\"device-info\">\n            <p><strong>设备名称：</strong>{{ selectedHsmDevice.deviceName }}</p>\n            <p><strong>IP地址：</strong>{{ selectedHsmDevice.ipAddress }}</p>\n            <p><strong>服务端口：</strong>{{ selectedHsmDevice.servicePort }}</p>\n            <p><strong>管理端口：</strong>{{ selectedHsmDevice.managementPort }}</p>\n            <p><strong>状态：</strong>\n              <el-tag v-if=\"selectedHsmDevice.status === 'available'\" type=\"success\" size=\"mini\">可用</el-tag>\n              <el-tag v-else-if=\"selectedHsmDevice.status === 'running'\" type=\"success\" size=\"mini\">运行中</el-tag>\n              <el-tag v-else-if=\"selectedHsmDevice.status === 'active'\" type=\"success\" size=\"mini\">活跃</el-tag>\n              <el-tag v-else type=\"warning\" size=\"mini\">{{ selectedHsmDevice.status }}</el-tag>\n            </p>\n            <p v-if=\"selectedHsmDevice.description\"><strong>描述：</strong>{{ selectedHsmDevice.description }}</p>\n            <p v-if=\"selectedHsmDevice.deviceGroupName\"><strong>设备组：</strong>{{ selectedHsmDevice.deviceGroupName }}</p>\n          </div>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"deployDialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"confirmDeployContainer\">部署</el-button>\n      </div>\n    </el-dialog>\n    \n    <!-- 容器日志对话框 -->\n    <el-dialog :visible.sync=\"logsDialogVisible\" title=\"容器日志\" width=\"800px\">\n      <el-input\n        v-model=\"containerLogs\"\n        :rows=\"20\"\n        readonly\n        type=\"textarea\"\n      />\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"logsDialogVisible = false\">关闭</el-button>\n      </div>\n    </el-dialog>\n    \n    <!-- 扩缩容对话框 -->\n    <el-dialog :visible.sync=\"scaleDialogVisible\" title=\"容器扩缩容\" width=\"500px\">\n      <el-form ref=\"scaleForm\" :model=\"scaleForm\" :rules=\"scaleRules\" label-width=\"100px\">\n        <el-form-item label=\"副本数\" prop=\"replicas\">\n          <el-input-number v-model=\"scaleForm.replicas\" :min=\"1\" />\n          <div class=\"form-help\">当前运行的容器实例数量</div>\n        </el-form-item>\n        <el-form-item label=\"分布策略\" v-if=\"scaleForm.replicas > 1\">\n          <el-select v-model=\"scaleForm.distributionStrategy\" placeholder=\"请选择分布策略\" style=\"width: 100%;\">\n            <el-option label=\"跨节点分散（推荐）\" value=\"SPREAD_ACROSS_NODES\" />\n            <el-option label=\"仅Worker节点\" value=\"WORKER_NODES_ONLY\" />\n            <el-option label=\"仅Manager节点\" value=\"MANAGER_NODES_ONLY\" />\n            <el-option label=\"平衡分布\" value=\"BALANCED\" />\n          </el-select>\n          <div class=\"form-help\">多副本时的部署分布策略</div>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"scaleDialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"confirmScaleContainer\">确认</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { mapGetters } from 'vuex'\nimport Pagination from '@/components/Pagination'\nimport {\n  getContainerList,\n  deployContainer,\n  getRecommendedStrategy,\n  startContainer,\n  stopContainer,\n  restartContainer,\n  removeContainer,\n  getContainerLogs,\n  syncContainerStatus,\n  scaleContainer,\n  updateContainerImage,\n  configContainerRoute,\n  updateContainerRoute\n} from '@/api/docker/container'\nimport { getImageList } from '@/api/docker/image'\nimport { fetchApplicationOptions, fetchUserApps } from '@/api/application'\nimport { getAvailableHsmDevices, getHsmDeviceDetail } from '@/api/docker/hsm'\n\nexport default {\n  name: 'DockerContainerIndex',\n  components: {\n    Pagination\n  },\n  data() {\n    return {\n      tableList: [],\n      loading: false,\n      total: 0,\n      listQuery: {\n        page: 1,\n        pageSize: 20,\n        containerName: undefined,\n        status: undefined\n      },\n      logsDialogVisible: false,\n      containerLogs: '',\n      currentContainer: null,\n      deployDialogVisible: false,\n      deployForm: {\n        containerName: '',\n        imageId: null,\n        imageName: '',\n        imageTag: 'latest',\n        servicePort: 80,\n        replicas: 1,\n        distributionStrategy: 'SPREAD_ACROSS_NODES',\n        enableTraefik: true,\n        enableHsm: false,\n        hsmDeviceId: null,\n        deployedBy: null,\n        applicationId: null,\n        applicationName: ''\n      },\n      deployRules: {\n        applicationId: [{ required: true, message: '请选择关联应用', trigger: 'change' }],\n        containerName: [{ required: true, message: '请输入容器名称', trigger: 'blur' }],\n        imageId: [{ required: true, message: '请选择镜像', trigger: 'change' }],\n        servicePort: [{ required: true, message: '请输入服务端口', trigger: 'blur' }]\n      },\n      scaleDialogVisible: false,\n      scaleForm: {\n        id: '',\n        replicas: 1,\n        distributionStrategy: 'SPREAD_ACROSS_NODES'\n      },\n      scaleRules: {\n        replicas: [{ required: true, message: '请输入副本数量', trigger: 'blur' }]\n      },\n      // 镜像相关数据\n      imageList: [],\n      imageListLoading: false,\n      selectedImage: null,\n      // HSM设备相关数据\n      hsmDeviceList: [],\n      hsmDeviceListLoading: false,\n      selectedHsmDevice: null,\n      // 应用下拉\n      appOptions: [],\n      appOptionsLoading: false\n    }\n  },\n  methods: {\n    handleSearch() {\n      this.getList()\n    },\n    handleResetSearch() {\n      this.listQuery = {\n        page: 1,\n        pageSize: 20,\n        containerName: undefined,\n        status: undefined\n      }\n      this.getList()\n    },\n    handleDeployContainer() {\n      // 首先加载镜像列表\n      Promise.all([this.loadImageList(), this.loadAppOptions()]).then(() => {\n        this.deployForm = {\n          containerName: '',\n          imageId: null,\n          imageName: '',\n          imageTag: 'latest',\n          servicePort: 80,\n          replicas: 1,\n          distributionStrategy: 'SPREAD_ACROSS_NODES',\n          enableTraefik: true,\n          enableHsm: false,\n          hsmDeviceId: null,\n          deployedBy: this.user ? this.user.id : null,\n          applicationId: null,\n          applicationName: ''\n        }\n        this.selectedImage = null\n        this.selectedHsmDevice = null\n        this.deployDialogVisible = true\n        this.$nextTick(() => {\n          this.$refs['deployForm'].clearValidate()\n        })\n      })\n    },\n    confirmDeployContainer() {\n      this.$refs['deployForm'].validate((valid) => {\n        if (valid) {\n          // 检查HSM设备配置\n          if (this.deployForm.enableHsm && !this.deployForm.hsmDeviceId) {\n            this.$message.error('请选择HSM设备')\n            return\n          }\n          \n          const deployData = { ...this.deployForm }\n          \n          // 确保镜像信息完整\n          if (this.selectedImage) {\n            deployData.imageName = this.selectedImage.imageName\n            deployData.imageTag = this.selectedImage.imageTag\n          }\n          \n          // 构建HSM设备配置\n          if (this.deployForm.enableHsm && this.selectedHsmDevice) {\n            deployData.hsmDeviceConfig = {\n              encryptorGroupId: 1, // 默认组ID\n              encryptorId: this.selectedHsmDevice.deviceId,\n              encryptorName: this.selectedHsmDevice.deviceName,\n              serverIpAddr: this.selectedHsmDevice.ipAddress,\n              serverPort: this.selectedHsmDevice.servicePort,\n              tcpConnNum: this.selectedHsmDevice.tcpConnNum || 5,\n              msgHeadLen: this.selectedHsmDevice.msgHeadLen || 4,\n              msgTailLen: this.selectedHsmDevice.msgTailLen || 0,\n              asciiOrEbcdic: this.selectedHsmDevice.encoding || 0,\n              dynamicLibPath: './libdeviceapi.so'\n            }\n          }\n          \n          // 构建Traefik配置\n          if (this.deployForm.enableTraefik) {\n            deployData.traefikConfig = {\n              enabled: true,\n              domain: `${deployData.containerName.toLowerCase().replace(/[^a-z0-9-]/g, '-')}.ccsp.local`,\n              servicePort: deployData.servicePort || 80,\n              pathPrefix: '/',\n              httpsEnabled: false\n            }\n          }\n\n          // 设置分布策略\n          if (deployData.replicas > 1 && deployData.distributionStrategy) {\n            deployData.distributionStrategy = deployData.distributionStrategy\n          }\n\n          // 设备资源配置\n          if (this.deployForm.enableHsm && this.selectedHsmDevice && deployData.hsmDeviceConfig) {\n            deployData.deviceResourceConfig = {\n              deviceResourceType: 'VSM',\n              deviceResourceIds: [this.selectedHsmDevice.deviceId],\n              allocationType: 'exclusive',\n              priority: 1,\n              configData: JSON.stringify(deployData.hsmDeviceConfig)\n            }\n          }\n\n          // 使用统一的部署接口\n          console.log('使用统一的部署接口，配置：', {\n            hsm: this.deployForm.enableHsm,\n            traefik: this.deployForm.enableTraefik,\n            strategy: deployData.distributionStrategy,\n            replicas: deployData.replicas\n          })\n\n          deployContainer(deployData).then((response) => {\n            this.deployDialogVisible = false\n            if (response.code === 20000) {\n              if (response.data) {\n                // 处理不同的响应结构\n                let message = '容器部署成功！'\n                \n                if (response.data.accessUrl) {\n                  message += `访问地址：${response.data.accessUrl}`\n                } else if (response.data.container && response.data.container.accessUrl) {\n                  message += `访问地址：${response.data.container.accessUrl}`\n                }\n                \n                if (response.data.distributionStrategy) {\n                  message += `，分布策略：${this.getStrategyDisplayName(response.data.distributionStrategy)}`\n                }\n                \n                if (response.data.hsmConfigured) {\n                  message += `，HSM设备：${this.selectedHsmDevice.deviceName}`\n                }\n                \n                this.$message.success(message)\n              } else {\n                this.$message.success('容器部署任务已提交')\n              }\n              this.getList()\n            } else {\n              this.$message.error(response.message || '部署失败')\n            }\n          }).catch(() => {\n            // 错误处理已在拦截器中处理\n          })\n        }\n      })\n    },\n\n    // 加载应用选项\n    async loadAppOptions() {\n      this.appOptionsLoading = true\n      try {\n        let resp\n        // 租户角色使用 user/list-options，其它使用 fetch-options\n        if (this.user && Array.isArray(this.user.roles) && this.user.roles.includes('ROLE_TENANT')) {\n          resp = await fetchUserApps()\n        } else {\n          resp = await fetchApplicationOptions()\n        }\n        if (resp && resp.code === 20000) {\n          const data = Array.isArray(resp.data) ? resp.data : (resp.data && resp.data.list) ? resp.data.list : []\n          this.appOptions = data.map(item => {\n            if (item.label && (item.value !== undefined)) return item\n            return { label: item.name || item.applicationName || item.label, value: String(item.id || item.value) }\n          })\n        } else {\n          this.appOptions = []\n        }\n      } catch (e) {\n        this.appOptions = []\n      } finally {\n        this.appOptionsLoading = false\n      }\n    },\n    handleStartContainer(row) {\n      startContainer(row.id).then(() => {\n        this.$message.success('容器启动成功')\n        this.getList()\n      })\n    },\n    handleStopContainer(row) {\n      stopContainer(row.id).then(() => {\n        this.$message.success('容器停止成功')\n        this.getList()\n      })\n    },\n    handleRestartContainer(row) {\n      restartContainer(row.id).then(() => {\n        this.$message.success('容器重启成功')\n        this.getList()\n      })\n    },\n    handleRemoveContainer(row) {\n      this.$msgbox.confirm(`确认要删除容器 ${row.containerName} 吗？`, '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        removeContainer(row.id).then(() => {\n          this.$message.success('删除成功')\n          this.getList()\n        })\n      }).catch(() => {\n        // 取消删除\n      })\n    },\n    handleScaleContainer(row) {\n      this.scaleForm = {\n        id: row.id,\n        replicas: row.replicas || 1, // 使用当前副本数或默认值\n        distributionStrategy: row.distributionStrategy || 'SPREAD_ACROSS_NODES'\n      }\n      this.scaleDialogVisible = true\n      this.$nextTick(() => {\n        this.$refs['scaleForm'].clearValidate()\n      })\n    },\n    confirmScaleContainer() {\n      this.$refs['scaleForm'].validate((valid) => {\n        if (valid) {\n          scaleContainer(this.scaleForm.id, this.scaleForm.replicas).then(() => {\n            this.scaleDialogVisible = false\n            this.$message.success('容器扩缩容任务已提交')\n            this.getList()\n          })\n        }\n      })\n    },\n    handleViewLogs(row) {\n      this.currentContainer = row\n      getContainerLogs(row.id).then(response => {\n        if (response.code === 20000) {\n          this.containerLogs = response.data || ''\n          this.logsDialogVisible = true\n        }\n      })\n    },\n    handleSyncStatus() {\n      syncContainerStatus().then(() => {\n        this.$message.success('容器状态同步任务已提交')\n        this.getList()\n      })\n    },\n    getList() {\n      this.loading = true\n      getContainerList(this.listQuery).then(response => {\n        if (response.code === 20000) {\n          this.tableList = response.data.list\n          this.total = response.data.totalCount\n        }\n        this.loading = false\n      }).catch(() => {\n        this.loading = false\n      })\n    },\n    // 格式化端口映射显示\n    formatPorts(portMappings) {\n      if (!portMappings) return '-'\n      try {\n        const ports = JSON.parse(portMappings)\n        if (Array.isArray(ports) && ports.length > 0) {\n          return ports.map(p => `${p.hostPort || ''}:${p.containerPort}/${p.protocol || 'tcp'}`).join(', ')\n        }\n        return '-'\n      } catch (e) {\n        return portMappings\n      }\n    },\n    // 格式化日期显示\n    formatDate(dateTime) {\n      if (!dateTime) return '-'\n      return new Date(dateTime).toLocaleString('zh-CN')\n    },\n    \n    // 复制到剪贴板\n    copyToClipboard(text) {\n      if (navigator.clipboard) {\n        navigator.clipboard.writeText(text).then(() => {\n          this.$message.success('地址已复制到剪贴板')\n        })\n      } else {\n        // 降级方案\n        const textArea = document.createElement('textarea')\n        textArea.value = text\n        document.body.appendChild(textArea)\n        textArea.select()\n        document.execCommand('copy')\n        document.body.removeChild(textArea)\n        this.$message.success('地址已复制到剪贴板')\n      }\n    },\n    \n    // 配置访问路由\n    handleConfigRoute(row) {\n      this.$prompt('请输入服务端口（容器内部端口）', '配置访问路由', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        inputValue: '80',\n        inputValidator: (value) => {\n          const port = parseInt(value)\n          if (!port || port < 1 || port > 65535) {\n            return '请输入有效的端口号(1-65535)'\n          }\n          return true\n        }\n      }).then(({ value }) => {\n        const routeConfig = {\n          containerId: row.id,\n          servicePort: parseInt(value),\n          enableTraefik: true\n        }\n        \n        this.configContainerRoute(routeConfig).then((response) => {\n          if (response.code === 20000 && response.data && response.data.accessUrl) {\n            this.$message.success(`路由配置成功！访问地址：${response.data.accessUrl}`)\n            this.getList()\n          } else {\n            this.$message.error(response.message || '配置失败')\n          }\n        }).catch(() => {\n          // 错误处理已在拦截器中处理\n        })\n      }).catch(() => {\n        // 取消操作\n      })\n    },\n    \n    // 更新路由配置\n    handleUpdateRoute(row) {\n      this.$prompt('请输入新的服务端口', '更新路由', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        inputValidator: (value) => {\n          const port = parseInt(value)\n          if (!port || port < 1 || port > 65535) {\n            return '请输入有效的端口号(1-65535)'\n          }\n          return true\n        }\n      }).then(({ value }) => {\n        const routeConfig = {\n          containerId: row.id,\n          servicePort: parseInt(value)\n        }\n        \n        this.updateContainerRoute(routeConfig).then((response) => {\n          if (response.code === 20000 && response.data && response.data.accessUrl) {\n            this.$message.success(`路由更新成功！新地址：${response.data.accessUrl}`)\n            this.getList()\n          } else {\n            this.$message.error(response.message || '更新失败')\n          }\n        }).catch(() => {\n          // 错误处理已在拦截器中处理\n        })\n      }).catch(() => {\n        // 取消操作\n      })\n    },\n    \n\n    \n    // 配置容器路由方法\n    async configContainerRoute(routeConfig) {\n      return configContainerRoute(routeConfig)\n    },\n    \n    // 更新容器路由方法\n    async updateContainerRoute(routeConfig) {\n      return updateContainerRoute(routeConfig)\n    },\n    \n    // 加载镜像列表\n    async loadImageList() {\n      this.imageListLoading = true\n      try {\n        const response = await getImageList()\n        if (response.code === 20000) {\n          // 处理分页数据结构\n          if (response.data && response.data.list) {\n            // 如果返回的是分页结构\n            this.imageList = response.data.list.map(image => {\n              return {\n                ...image,\n                // 确保 sizeMb 是数字类型\n                sizeMb: typeof image.sizeMb === 'string' ? parseInt(image.sizeMb) : image.sizeMb\n              }\n            })\n          } else if (Array.isArray(response.data)) {\n            // 如果返回的是直接数组\n            this.imageList = response.data.map(image => {\n              return {\n                ...image,\n                sizeMb: typeof image.sizeMb === 'string' ? parseInt(image.sizeMb) : image.sizeMb\n              }\n            })\n          } else {\n            this.imageList = []\n          }\n          \n          if (this.imageList.length === 0) {\n            this.$message.info('当前没有可用的镜像，请先构建或导入镜像')\n          }\n        } else {\n          this.$message.error('获取镜像列表失败：' + (response.message || '未知错误'))\n          this.imageList = []\n        }\n      } catch (error) {\n        console.error('加载镜像列表失败:', error)\n        this.$message.error('加载镜像列表失败')\n        this.imageList = []\n      } finally {\n        this.imageListLoading = false\n      }\n    },\n    \n    // 处理镜像选择变化\n    handleImageChange(imageId) {\n      if (imageId) {\n        this.selectedImage = this.imageList.find(img => img.id === imageId)\n        if (this.selectedImage) {\n          // 自动填充镜像名称和标签\n          this.deployForm.imageName = this.selectedImage.name\n          this.deployForm.imageTag = this.selectedImage.tag\n          \n          // 智能生成容器名称（如果当前为空）\n          if (!this.deployForm.containerName) {\n            this.suggestContainerName(this.selectedImage.name, this.selectedImage.tag)\n          }\n          \n          // 根据镜像类型智能推荐端口\n          this.suggestServicePort(this.selectedImage.name)\n        }\n      } else {\n        this.selectedImage = null\n        this.deployForm.imageName = ''\n        this.deployForm.imageTag = 'latest'\n      }\n    },\n    \n    // 智能生成容器名称\n    suggestContainerName(imageName, imageTag) {\n      // 移除镜像名称中的特殊字符，生成简洁的名称\n      let baseName = imageName.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase()\n      \n      // 如果标签不是latest，则加入标签\n      if (imageTag && imageTag !== 'latest') {\n        baseName += '-' + imageTag.replace(/[^a-zA-Z0-9]/g, '-')\n      }\n      \n      // 添加时间戳保证唯一性\n      const timestamp = Date.now().toString().slice(-6)\n      this.deployForm.containerName = `${baseName}-${timestamp}`\n    },\n    \n    // 根据镜像类型智能推荐端口\n    suggestServicePort(imageName) {\n      const portMap = {\n        'nginx': 80,\n        'apache': 80,\n        'httpd': 80,\n        'mysql': 3306,\n        'mariadb': 3306,\n        'postgres': 5432,\n        'postgresql': 5432,\n        'redis': 6379,\n        'mongodb': 27017,\n        'mongo': 27017,\n        'tomcat': 8080,\n        'node': 3000,\n        'spring': 8080,\n        'java': 8080\n      }\n      \n      // 查找匹配的镜像类型\n      for (const [key, port] of Object.entries(portMap)) {\n        if (imageName.toLowerCase().includes(key)) {\n          this.deployForm.servicePort = port\n          break\n        }\n      }\n    },\n    \n    // 格式化镜像大小\n    formatImageSize(sizeMb) {\n      if (!sizeMb || sizeMb === 0) return '-'\n      \n      // 确保是数字类型\n      const size = typeof sizeMb === 'string' ? parseInt(sizeMb) : sizeMb\n      if (isNaN(size)) return '-'\n      \n      // 如果已经是MB单位，直接显示\n      if (size < 1024) {\n        return `${size} MB`\n      }\n      \n      // 转换为GB\n      const sizeGb = size / 1024\n      return `${sizeGb.toFixed(1)} GB`\n    },\n    \n    // 获取分布策略显示名称\n    getStrategyDisplayName(strategy) {\n      const strategyMap = {\n        'SPREAD_ACROSS_NODES': '跨节点分散',\n        'WORKER_NODES_ONLY': '仅Worker节点',\n        'MANAGER_NODES_ONLY': '仅Manager节点',\n        'BALANCED': '平衡分布',\n        'SPREAD_ACROSS_ZONES': '跨可用区分散'\n      }\n      return strategyMap[strategy] || strategy\n    },\n    \n    // 获取推荐的分布策略\n    async getRecommendedDistributionStrategy(replicas) {\n      try {\n        const response = await getRecommendedStrategy(replicas)\n        if (response.code === 20000 && response.data) {\n          return response.data.recommendedStrategy\n        }\n      } catch (error) {\n        console.warn('获取推荐分布策略失败:', error)\n      }\n      return 'SPREAD_ACROSS_NODES' // 默认值\n    },\n    \n    // === HSM设备相关方法 ===\n    \n    // 处理HSM开关变化\n    handleHsmToggle(enabled) {\n      if (enabled) {\n        // 启用HSM时加载设备列表\n        this.loadHsmDeviceList()\n      } else {\n        // 禁用HSM时清空选择\n        this.deployForm.hsmDeviceId = null\n        this.selectedHsmDevice = null\n      }\n    },\n    \n    // 加载HSM设备列表\n    async loadHsmDeviceList() {\n      this.hsmDeviceListLoading = true\n      try {\n        const response = await getAvailableHsmDevices({\n          status: 'running'\n        })\n        \n        if (response.code === 20000) {\n          if (response.data && response.data.list) {\n            this.hsmDeviceList = response.data.list\n          } else if (Array.isArray(response.data)) {\n            this.hsmDeviceList = response.data\n          } else {\n            this.hsmDeviceList = []\n          }\n          \n          if (this.hsmDeviceList.length === 0) {\n            this.$message.info('当前没有可用的HSM设备')\n          }\n        } else {\n          this.$message.error('获取HSM设备列表失败：' + (response.message || '未知错误'))\n          this.hsmDeviceList = []\n        }\n      } catch (error) {\n        console.error('加载HSM设备列表失败:', error)\n        this.$message.error('加载HSM设备列表失败')\n        this.hsmDeviceList = []\n      } finally {\n        this.hsmDeviceListLoading = false\n      }\n    },\n    \n    // 处理HSM设备选择变化\n    handleHsmDeviceChange(deviceId) {\n      if (deviceId) {\n        this.selectedHsmDevice = this.hsmDeviceList.find(device => device.deviceId === deviceId)\n        if (this.selectedHsmDevice) {\n          // 可以在这里加载更详细的设备信息\n          this.loadHsmDeviceDetail(deviceId)\n        }\n      } else {\n        this.selectedHsmDevice = null\n      }\n    },\n    \n    // 加载HSM设备详细信息\n    async loadHsmDeviceDetail(deviceId) {\n      try {\n        const response = await getHsmDeviceDetail(deviceId)\n        if (response.code === 20000 && response.data) {\n          this.selectedHsmDevice = response.data\n        }\n      } catch (error) {\n        console.warn('获取HSM设备详细信息失败:', error)\n      }\n    },\n    \n\n  },\n  mounted() {\n    this.getList()\n    // 预加载镜像列表，提升用户体验\n    this.loadImageList()\n    // 预加载HSM设备列表\n    this.loadHsmDeviceList()\n  },\n  computed: {\n    ...mapGetters([\n      'user'\n    ])\n  },\n  watch: {\n    // 监听副本数变化，自动更新推荐的分布策略\n    'deployForm.replicas'(newReplicas, oldReplicas) {\n      if (newReplicas > 1 && newReplicas !== oldReplicas) {\n        this.getRecommendedDistributionStrategy(newReplicas).then(strategy => {\n          this.deployForm.distributionStrategy = strategy\n        })\n      } else if (newReplicas === 1) {\n        // 单副本时不需要分布策略\n        this.deployForm.distributionStrategy = 'BALANCED'\n      }\n    },\n    // 监听扩缩容副本数变化\n    'scaleForm.replicas'(newReplicas, oldReplicas) {\n      if (newReplicas > 1 && newReplicas !== oldReplicas) {\n        this.getRecommendedDistributionStrategy(newReplicas).then(strategy => {\n          this.scaleForm.distributionStrategy = strategy\n        })\n      } else if (newReplicas === 1) {\n        this.scaleForm.distributionStrategy = 'BALANCED'\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.filter-inner {\n  display: flex;\n  align-items: center;\n  flex-wrap: wrap;\n  \n  .el-form-item {\n    margin-bottom: 10px;\n    margin-right: 15px;\n  }\n}\n\n.pull-right {\n  float: right;\n}\n\n.clearfix::after {\n  content: \"\";\n  display: table;\n  clear: both;\n}\n\n.form-help {\n  font-size: 12px;\n  color: #909399;\n  margin-top: 4px;\n}\n\n.el-link {\n  font-size: 12px;\n  max-width: 150px;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  display: inline-block;\n}\n\n.image-info {\n  background-color: #f5f7fa;\n  padding: 12px;\n  border-radius: 4px;\n  border: 1px solid #e4e7ed;\n  \n  p {\n    margin: 4px 0;\n    font-size: 13px;\n    \n    strong {\n      color: #303133;\n      font-weight: 500;\n    }\n  }\n}\n\n.device-info {\n  background-color: #f0f9ff;\n  padding: 12px;\n  border-radius: 4px;\n  border: 1px solid #a7f3d0;\n  \n  p {\n    margin: 4px 0;\n    font-size: 13px;\n    \n    strong {\n      color: #303133;\n      font-weight: 500;\n    }\n  }\n}\n\n.strategy-info {\n  font-size: 11px;\n  color: #909399;\n  margin-top: 2px;\n}\n\n.hsm-info {\n  font-size: 11px;\n  color: #67c23a;\n  margin-top: 2px;\n}\n</style>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoUA,SAAAA,UAAA;AACA,OAAAC,UAAA;AACA,SACAC,gBAAA,EACAC,eAAA,EACAC,sBAAA,EACAC,cAAA,EACAC,aAAA,EACAC,gBAAA,EACAC,eAAA,EACAC,gBAAA,EACAC,mBAAA,EACAC,cAAA,EACAC,oBAAA,EACAC,oBAAA,IAAAA,qBAAA,EACAC,oBAAA,IAAAA,qBAAA,QACA;AACA,SAAAC,YAAA;AACA,SAAAC,uBAAA,EAAAC,aAAA;AACA,SAAAC,sBAAA,EAAAC,kBAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACApB,UAAA,EAAAA;EACA;EACAqB,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA;MACAC,OAAA;MACAC,KAAA;MACAC,SAAA;QACAC,IAAA;QACAC,QAAA;QACAC,aAAA,EAAAC,SAAA;QACAC,MAAA,EAAAD;MACA;MACAE,iBAAA;MACAC,aAAA;MACAC,gBAAA;MACAC,mBAAA;MACAC,UAAA;QACAP,aAAA;QACAQ,OAAA;QACAC,SAAA;QACAC,QAAA;QACAC,WAAA;QACAC,QAAA;QACAC,oBAAA;QACAC,aAAA;QACAC,SAAA;QACAC,WAAA;QACAC,UAAA;QACAC,aAAA;QACAC,eAAA;MACA;MACAC,WAAA;QACAF,aAAA;UAAAG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAvB,aAAA;UAAAqB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAf,OAAA;UAAAa,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAZ,WAAA;UAAAU,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MACA;MACAC,kBAAA;MACAC,SAAA;QACAC,EAAA;QACAd,QAAA;QACAC,oBAAA;MACA;MACAc,UAAA;QACAf,QAAA;UAAAS,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MACA;MACA;MACAK,SAAA;MACAC,gBAAA;MACAC,aAAA;MACA;MACAC,aAAA;MACAC,oBAAA;MACAC,iBAAA;MACA;MACAC,UAAA;MACAC,iBAAA;IACA;EACA;EACAC,OAAA;IACAC,YAAA,WAAAA,aAAA;MACA,KAAAC,OAAA;IACA;IACAC,iBAAA,WAAAA,kBAAA;MACA,KAAA1C,SAAA;QACAC,IAAA;QACAC,QAAA;QACAC,aAAA,EAAAC,SAAA;QACAC,MAAA,EAAAD;MACA;MACA,KAAAqC,OAAA;IACA;IACAE,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,KAAA;MACA;MACAC,OAAA,CAAAC,GAAA,OAAAC,aAAA,SAAAC,cAAA,KAAAC,IAAA;QACAL,KAAA,CAAAlC,UAAA;UACAP,aAAA;UACAQ,OAAA;UACAC,SAAA;UACAC,QAAA;UACAC,WAAA;UACAC,QAAA;UACAC,oBAAA;UACAC,aAAA;UACAC,SAAA;UACAC,WAAA;UACAC,UAAA,EAAAwB,KAAA,CAAAM,IAAA,GAAAN,KAAA,CAAAM,IAAA,CAAArB,EAAA;UACAR,aAAA;UACAC,eAAA;QACA;QACAsB,KAAA,CAAAX,aAAA;QACAW,KAAA,CAAAR,iBAAA;QACAQ,KAAA,CAAAnC,mBAAA;QACAmC,KAAA,CAAAO,SAAA;UACAP,KAAA,CAAAQ,KAAA,eAAAC,aAAA;QACA;MACA;IACA;IACAC,sBAAA,WAAAA,uBAAA;MAAA,IAAAC,MAAA;MACA,KAAAH,KAAA,eAAAI,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA;UACA,IAAAF,MAAA,CAAA7C,UAAA,CAAAQ,SAAA,KAAAqC,MAAA,CAAA7C,UAAA,CAAAS,WAAA;YACAoC,MAAA,CAAAG,QAAA,CAAAC,KAAA;YACA;UACA;UAEA,IAAAC,UAAA,GAAAC,aAAA,KAAAN,MAAA,CAAA7C,UAAA;;UAEA;UACA,IAAA6C,MAAA,CAAAtB,aAAA;YACA2B,UAAA,CAAAhD,SAAA,GAAA2C,MAAA,CAAAtB,aAAA,CAAArB,SAAA;YACAgD,UAAA,CAAA/C,QAAA,GAAA0C,MAAA,CAAAtB,aAAA,CAAApB,QAAA;UACA;;UAEA;UACA,IAAA0C,MAAA,CAAA7C,UAAA,CAAAQ,SAAA,IAAAqC,MAAA,CAAAnB,iBAAA;YACAwB,UAAA,CAAAE,eAAA;cACAC,gBAAA;cAAA;cACAC,WAAA,EAAAT,MAAA,CAAAnB,iBAAA,CAAA6B,QAAA;cACAC,aAAA,EAAAX,MAAA,CAAAnB,iBAAA,CAAA+B,UAAA;cACAC,YAAA,EAAAb,MAAA,CAAAnB,iBAAA,CAAAiC,SAAA;cACAC,UAAA,EAAAf,MAAA,CAAAnB,iBAAA,CAAAtB,WAAA;cACAyD,UAAA,EAAAhB,MAAA,CAAAnB,iBAAA,CAAAmC,UAAA;cACAC,UAAA,EAAAjB,MAAA,CAAAnB,iBAAA,CAAAoC,UAAA;cACAC,UAAA,EAAAlB,MAAA,CAAAnB,iBAAA,CAAAqC,UAAA;cACAC,aAAA,EAAAnB,MAAA,CAAAnB,iBAAA,CAAAuC,QAAA;cACAC,cAAA;YACA;UACA;;UAEA;UACA,IAAArB,MAAA,CAAA7C,UAAA,CAAAO,aAAA;YACA2C,UAAA,CAAAiB,aAAA;cACAC,OAAA;cACAC,MAAA,KAAAC,MAAA,CAAApB,UAAA,CAAAzD,aAAA,CAAA8E,WAAA,GAAAC,OAAA;cACApE,WAAA,EAAA8C,UAAA,CAAA9C,WAAA;cACAqE,UAAA;cACAC,YAAA;YACA;UACA;;UAEA;UACA,IAAAxB,UAAA,CAAA7C,QAAA,QAAA6C,UAAA,CAAA5C,oBAAA;YACA4C,UAAA,CAAA5C,oBAAA,GAAA4C,UAAA,CAAA5C,oBAAA;UACA;;UAEA;UACA,IAAAuC,MAAA,CAAA7C,UAAA,CAAAQ,SAAA,IAAAqC,MAAA,CAAAnB,iBAAA,IAAAwB,UAAA,CAAAE,eAAA;YACAF,UAAA,CAAAyB,oBAAA;cACAC,kBAAA;cACAC,iBAAA,GAAAhC,MAAA,CAAAnB,iBAAA,CAAA6B,QAAA;cACAuB,cAAA;cACAC,QAAA;cACAC,UAAA,EAAAC,IAAA,CAAAC,SAAA,CAAAhC,UAAA,CAAAE,eAAA;YACA;UACA;;UAEA;UACA+B,OAAA,CAAAC,GAAA;YACAC,GAAA,EAAAxC,MAAA,CAAA7C,UAAA,CAAAQ,SAAA;YACA8E,OAAA,EAAAzC,MAAA,CAAA7C,UAAA,CAAAO,aAAA;YACAgF,QAAA,EAAArC,UAAA,CAAA5C,oBAAA;YACAD,QAAA,EAAA6C,UAAA,CAAA7C;UACA;UAEAtC,eAAA,CAAAmF,UAAA,EAAAX,IAAA,WAAAiD,QAAA;YACA3C,MAAA,CAAA9C,mBAAA;YACA,IAAAyF,QAAA,CAAAC,IAAA;cACA,IAAAD,QAAA,CAAAtG,IAAA;gBACA;gBACA,IAAA6B,OAAA;gBAEA,IAAAyE,QAAA,CAAAtG,IAAA,CAAAwG,SAAA;kBACA3E,OAAA,qCAAAuD,MAAA,CAAAkB,QAAA,CAAAtG,IAAA,CAAAwG,SAAA;gBACA,WAAAF,QAAA,CAAAtG,IAAA,CAAAyG,SAAA,IAAAH,QAAA,CAAAtG,IAAA,CAAAyG,SAAA,CAAAD,SAAA;kBACA3E,OAAA,qCAAAuD,MAAA,CAAAkB,QAAA,CAAAtG,IAAA,CAAAyG,SAAA,CAAAD,SAAA;gBACA;gBAEA,IAAAF,QAAA,CAAAtG,IAAA,CAAAoB,oBAAA;kBACAS,OAAA,2CAAAuD,MAAA,CAAAzB,MAAA,CAAA+C,sBAAA,CAAAJ,QAAA,CAAAtG,IAAA,CAAAoB,oBAAA;gBACA;gBAEA,IAAAkF,QAAA,CAAAtG,IAAA,CAAA2G,aAAA;kBACA9E,OAAA,kCAAAuD,MAAA,CAAAzB,MAAA,CAAAnB,iBAAA,CAAA+B,UAAA;gBACA;gBAEAZ,MAAA,CAAAG,QAAA,CAAA8C,OAAA,CAAA/E,OAAA;cACA;gBACA8B,MAAA,CAAAG,QAAA,CAAA8C,OAAA;cACA;cACAjD,MAAA,CAAAd,OAAA;YACA;cACAc,MAAA,CAAAG,QAAA,CAAAC,KAAA,CAAAuC,QAAA,CAAAzE,OAAA;YACA;UACA,GAAAgF,KAAA;YACA;UAAA,CACA;QACA;MACA;IACA;IAEA;IACAzD,cAAA,WAAAA,eAAA;MAAA,IAAA0D,MAAA;MAAA,OAAAC,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAAC,QAAA;QAAA,IAAAC,IAAA,EAAAnH,IAAA,EAAAoH,EAAA;QAAA,OAAAJ,YAAA,GAAAK,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA;YAAA;cACAT,MAAA,CAAApE,iBAAA;cAAA4E,QAAA,CAAAE,CAAA;cAAA,MAIAV,MAAA,CAAAxD,IAAA,IAAAmE,KAAA,CAAAC,OAAA,CAAAZ,MAAA,CAAAxD,IAAA,CAAAqE,KAAA,KAAAb,MAAA,CAAAxD,IAAA,CAAAqE,KAAA,CAAAC,QAAA;gBAAAN,QAAA,CAAAC,CAAA;gBAAA;cAAA;cAAAD,QAAA,CAAAC,CAAA;cAAA,OACA5H,aAAA;YAAA;cAAAwH,IAAA,GAAAG,QAAA,CAAAO,CAAA;cAAAP,QAAA,CAAAC,CAAA;cAAA;YAAA;cAAAD,QAAA,CAAAC,CAAA;cAAA,OAEA7H,uBAAA;YAAA;cAAAyH,IAAA,GAAAG,QAAA,CAAAO,CAAA;YAAA;cAEA,IAAAV,IAAA,IAAAA,IAAA,CAAAZ,IAAA;gBACAvG,IAAA,GAAAyH,KAAA,CAAAC,OAAA,CAAAP,IAAA,CAAAnH,IAAA,IAAAmH,IAAA,CAAAnH,IAAA,GAAAmH,IAAA,CAAAnH,IAAA,IAAAmH,IAAA,CAAAnH,IAAA,CAAA8H,IAAA,GAAAX,IAAA,CAAAnH,IAAA,CAAA8H,IAAA;gBACAhB,MAAA,CAAArE,UAAA,GAAAzC,IAAA,CAAA+H,GAAA,WAAAC,IAAA;kBACA,IAAAA,IAAA,CAAAC,KAAA,IAAAD,IAAA,CAAAE,KAAA,KAAA1H,SAAA,SAAAwH,IAAA;kBACA;oBAAAC,KAAA,EAAAD,IAAA,CAAAlI,IAAA,IAAAkI,IAAA,CAAAtG,eAAA,IAAAsG,IAAA,CAAAC,KAAA;oBAAAC,KAAA,EAAAC,MAAA,CAAAH,IAAA,CAAA/F,EAAA,IAAA+F,IAAA,CAAAE,KAAA;kBAAA;gBACA;cACA;gBACApB,MAAA,CAAArE,UAAA;cACA;cAAA6E,QAAA,CAAAC,CAAA;cAAA;YAAA;cAAAD,QAAA,CAAAE,CAAA;cAAAJ,EAAA,GAAAE,QAAA,CAAAO,CAAA;cAEAf,MAAA,CAAArE,UAAA;YAAA;cAAA6E,QAAA,CAAAE,CAAA;cAEAV,MAAA,CAAApE,iBAAA;cAAA,OAAA4E,QAAA,CAAAc,CAAA;YAAA;cAAA,OAAAd,QAAA,CAAAe,CAAA;UAAA;QAAA,GAAAnB,OAAA;MAAA;IAEA;IACAoB,oBAAA,WAAAA,qBAAAC,GAAA;MAAA,IAAAC,MAAA;MACAzJ,cAAA,CAAAwJ,GAAA,CAAAtG,EAAA,EAAAoB,IAAA;QACAmF,MAAA,CAAA1E,QAAA,CAAA8C,OAAA;QACA4B,MAAA,CAAA3F,OAAA;MACA;IACA;IACA4F,mBAAA,WAAAA,oBAAAF,GAAA;MAAA,IAAAG,MAAA;MACA1J,aAAA,CAAAuJ,GAAA,CAAAtG,EAAA,EAAAoB,IAAA;QACAqF,MAAA,CAAA5E,QAAA,CAAA8C,OAAA;QACA8B,MAAA,CAAA7F,OAAA;MACA;IACA;IACA8F,sBAAA,WAAAA,uBAAAJ,GAAA;MAAA,IAAAK,MAAA;MACA3J,gBAAA,CAAAsJ,GAAA,CAAAtG,EAAA,EAAAoB,IAAA;QACAuF,MAAA,CAAA9E,QAAA,CAAA8C,OAAA;QACAgC,MAAA,CAAA/F,OAAA;MACA;IACA;IACAgG,qBAAA,WAAAA,sBAAAN,GAAA;MAAA,IAAAO,MAAA;MACA,KAAAC,OAAA,CAAAC,OAAA,+CAAA5D,MAAA,CAAAmD,GAAA,CAAAhI,aAAA;QACA0I,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAA9F,IAAA;QACAnE,eAAA,CAAAqJ,GAAA,CAAAtG,EAAA,EAAAoB,IAAA;UACAyF,MAAA,CAAAhF,QAAA,CAAA8C,OAAA;UACAkC,MAAA,CAAAjG,OAAA;QACA;MACA,GAAAgE,KAAA;QACA;MAAA,CACA;IACA;IACAuC,oBAAA,WAAAA,qBAAAb,GAAA;MAAA,IAAAc,MAAA;MACA,KAAArH,SAAA;QACAC,EAAA,EAAAsG,GAAA,CAAAtG,EAAA;QACAd,QAAA,EAAAoH,GAAA,CAAApH,QAAA;QAAA;QACAC,oBAAA,EAAAmH,GAAA,CAAAnH,oBAAA;MACA;MACA,KAAAW,kBAAA;MACA,KAAAwB,SAAA;QACA8F,MAAA,CAAA7F,KAAA,cAAAC,aAAA;MACA;IACA;IACA6F,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,MAAA;MACA,KAAA/F,KAAA,cAAAI,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAxE,cAAA,CAAAkK,MAAA,CAAAvH,SAAA,CAAAC,EAAA,EAAAsH,MAAA,CAAAvH,SAAA,CAAAb,QAAA,EAAAkC,IAAA;YACAkG,MAAA,CAAAxH,kBAAA;YACAwH,MAAA,CAAAzF,QAAA,CAAA8C,OAAA;YACA2C,MAAA,CAAA1G,OAAA;UACA;QACA;MACA;IACA;IACA2G,cAAA,WAAAA,eAAAjB,GAAA;MAAA,IAAAkB,MAAA;MACA,KAAA7I,gBAAA,GAAA2H,GAAA;MACApJ,gBAAA,CAAAoJ,GAAA,CAAAtG,EAAA,EAAAoB,IAAA,WAAAiD,QAAA;QACA,IAAAA,QAAA,CAAAC,IAAA;UACAkD,MAAA,CAAA9I,aAAA,GAAA2F,QAAA,CAAAtG,IAAA;UACAyJ,MAAA,CAAA/I,iBAAA;QACA;MACA;IACA;IACAgJ,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,MAAA;MACAvK,mBAAA,GAAAiE,IAAA;QACAsG,MAAA,CAAA7F,QAAA,CAAA8C,OAAA;QACA+C,MAAA,CAAA9G,OAAA;MACA;IACA;IACAA,OAAA,WAAAA,QAAA;MAAA,IAAA+G,OAAA;MACA,KAAA1J,OAAA;MACAtB,gBAAA,MAAAwB,SAAA,EAAAiD,IAAA,WAAAiD,QAAA;QACA,IAAAA,QAAA,CAAAC,IAAA;UACAqD,OAAA,CAAA3J,SAAA,GAAAqG,QAAA,CAAAtG,IAAA,CAAA8H,IAAA;UACA8B,OAAA,CAAAzJ,KAAA,GAAAmG,QAAA,CAAAtG,IAAA,CAAA6J,UAAA;QACA;QACAD,OAAA,CAAA1J,OAAA;MACA,GAAA2G,KAAA;QACA+C,OAAA,CAAA1J,OAAA;MACA;IACA;IACA;IACA4J,WAAA,WAAAA,YAAAC,YAAA;MACA,KAAAA,YAAA;MACA;QACA,IAAAC,KAAA,GAAAjE,IAAA,CAAAkE,KAAA,CAAAF,YAAA;QACA,IAAAtC,KAAA,CAAAC,OAAA,CAAAsC,KAAA,KAAAA,KAAA,CAAAE,MAAA;UACA,OAAAF,KAAA,CAAAjC,GAAA,WAAAP,CAAA;YAAA,UAAApC,MAAA,CAAAoC,CAAA,CAAA2C,QAAA,aAAA/E,MAAA,CAAAoC,CAAA,CAAA4C,aAAA,OAAAhF,MAAA,CAAAoC,CAAA,CAAA6C,QAAA;UAAA,GAAAC,IAAA;QACA;QACA;MACA,SAAAC,CAAA;QACA,OAAAR,YAAA;MACA;IACA;IACA;IACAS,UAAA,WAAAA,WAAAC,QAAA;MACA,KAAAA,QAAA;MACA,WAAAC,IAAA,CAAAD,QAAA,EAAAE,cAAA;IACA;IAEA;IACAC,eAAA,WAAAA,gBAAAC,IAAA;MAAA,IAAAC,OAAA;MACA,IAAAC,SAAA,CAAAC,SAAA;QACAD,SAAA,CAAAC,SAAA,CAAAC,SAAA,CAAAJ,IAAA,EAAAxH,IAAA;UACAyH,OAAA,CAAAhH,QAAA,CAAA8C,OAAA;QACA;MACA;QACA;QACA,IAAAsE,QAAA,GAAAC,QAAA,CAAAC,aAAA;QACAF,QAAA,CAAAhD,KAAA,GAAA2C,IAAA;QACAM,QAAA,CAAAE,IAAA,CAAAC,WAAA,CAAAJ,QAAA;QACAA,QAAA,CAAAK,MAAA;QACAJ,QAAA,CAAAK,WAAA;QACAL,QAAA,CAAAE,IAAA,CAAAI,WAAA,CAAAP,QAAA;QACA,KAAApH,QAAA,CAAA8C,OAAA;MACA;IACA;IAEA;IACA8E,iBAAA,WAAAA,kBAAAnD,GAAA;MAAA,IAAAoD,OAAA;MACA,KAAAC,OAAA;QACA3C,iBAAA;QACAC,gBAAA;QACA2C,UAAA;QACAC,cAAA,WAAAA,eAAA5D,KAAA;UACA,IAAA6D,IAAA,GAAAC,QAAA,CAAA9D,KAAA;UACA,KAAA6D,IAAA,IAAAA,IAAA,QAAAA,IAAA;YACA;UACA;UACA;QACA;MACA,GAAA1I,IAAA,WAAA4I,IAAA;QAAA,IAAA/D,KAAA,GAAA+D,IAAA,CAAA/D,KAAA;QACA,IAAAgE,WAAA;UACAC,WAAA,EAAA5D,GAAA,CAAAtG,EAAA;UACAf,WAAA,EAAA8K,QAAA,CAAA9D,KAAA;UACA7G,aAAA;QACA;QAEAsK,OAAA,CAAApM,oBAAA,CAAA2M,WAAA,EAAA7I,IAAA,WAAAiD,QAAA;UACA,IAAAA,QAAA,CAAAC,IAAA,cAAAD,QAAA,CAAAtG,IAAA,IAAAsG,QAAA,CAAAtG,IAAA,CAAAwG,SAAA;YACAmF,OAAA,CAAA7H,QAAA,CAAA8C,OAAA,4EAAAxB,MAAA,CAAAkB,QAAA,CAAAtG,IAAA,CAAAwG,SAAA;YACAmF,OAAA,CAAA9I,OAAA;UACA;YACA8I,OAAA,CAAA7H,QAAA,CAAAC,KAAA,CAAAuC,QAAA,CAAAzE,OAAA;UACA;QACA,GAAAgF,KAAA;UACA;QAAA,CACA;MACA,GAAAA,KAAA;QACA;MAAA,CACA;IACA;IAEA;IACAuF,iBAAA,WAAAA,kBAAA7D,GAAA;MAAA,IAAA8D,OAAA;MACA,KAAAT,OAAA;QACA3C,iBAAA;QACAC,gBAAA;QACA4C,cAAA,WAAAA,eAAA5D,KAAA;UACA,IAAA6D,IAAA,GAAAC,QAAA,CAAA9D,KAAA;UACA,KAAA6D,IAAA,IAAAA,IAAA,QAAAA,IAAA;YACA;UACA;UACA;QACA;MACA,GAAA1I,IAAA,WAAAiJ,KAAA;QAAA,IAAApE,KAAA,GAAAoE,KAAA,CAAApE,KAAA;QACA,IAAAgE,WAAA;UACAC,WAAA,EAAA5D,GAAA,CAAAtG,EAAA;UACAf,WAAA,EAAA8K,QAAA,CAAA9D,KAAA;QACA;QAEAmE,OAAA,CAAA7M,oBAAA,CAAA0M,WAAA,EAAA7I,IAAA,WAAAiD,QAAA;UACA,IAAAA,QAAA,CAAAC,IAAA,cAAAD,QAAA,CAAAtG,IAAA,IAAAsG,QAAA,CAAAtG,IAAA,CAAAwG,SAAA;YACA6F,OAAA,CAAAvI,QAAA,CAAA8C,OAAA,sEAAAxB,MAAA,CAAAkB,QAAA,CAAAtG,IAAA,CAAAwG,SAAA;YACA6F,OAAA,CAAAxJ,OAAA;UACA;YACAwJ,OAAA,CAAAvI,QAAA,CAAAC,KAAA,CAAAuC,QAAA,CAAAzE,OAAA;UACA;QACA,GAAAgF,KAAA;UACA;QAAA,CACA;MACA,GAAAA,KAAA;QACA;MAAA,CACA;IACA;IAIA;IACAtH,oBAAA,WAAAA,qBAAA2M,WAAA;MAAA,OAAAnF,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAAsF,SAAA;QAAA,OAAAvF,YAAA,GAAAK,CAAA,WAAAmF,SAAA;UAAA,kBAAAA,SAAA,CAAAjF,CAAA;YAAA;cAAA,OAAAiF,SAAA,CAAAnE,CAAA,IACA9I,qBAAA,CAAA2M,WAAA;UAAA;QAAA,GAAAK,QAAA;MAAA;IACA;IAEA;IACA/M,oBAAA,WAAAA,qBAAA0M,WAAA;MAAA,OAAAnF,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAAwF,SAAA;QAAA,OAAAzF,YAAA,GAAAK,CAAA,WAAAqF,SAAA;UAAA,kBAAAA,SAAA,CAAAnF,CAAA;YAAA;cAAA,OAAAmF,SAAA,CAAArE,CAAA,IACA7I,qBAAA,CAAA0M,WAAA;UAAA;QAAA,GAAAO,QAAA;MAAA;IACA;IAEA;IACAtJ,aAAA,WAAAA,cAAA;MAAA,IAAAwJ,OAAA;MAAA,OAAA5F,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAA2F,SAAA;QAAA,IAAAtG,QAAA,EAAAuG,GAAA;QAAA,OAAA7F,YAAA,GAAAK,CAAA,WAAAyF,SAAA;UAAA,kBAAAA,SAAA,CAAAvF,CAAA;YAAA;cACAoF,OAAA,CAAAvK,gBAAA;cAAA0K,SAAA,CAAAtF,CAAA;cAAAsF,SAAA,CAAAvF,CAAA;cAAA,OAEA9H,YAAA;YAAA;cAAA6G,QAAA,GAAAwG,SAAA,CAAAjF,CAAA;cACA,IAAAvB,QAAA,CAAAC,IAAA;gBACA;gBACA,IAAAD,QAAA,CAAAtG,IAAA,IAAAsG,QAAA,CAAAtG,IAAA,CAAA8H,IAAA;kBACA;kBACA6E,OAAA,CAAAxK,SAAA,GAAAmE,QAAA,CAAAtG,IAAA,CAAA8H,IAAA,CAAAC,GAAA,WAAAgF,KAAA;oBACA,OAAA9I,aAAA,CAAAA,aAAA,KACA8I,KAAA;sBACA;sBACAC,MAAA,SAAAD,KAAA,CAAAC,MAAA,gBAAAhB,QAAA,CAAAe,KAAA,CAAAC,MAAA,IAAAD,KAAA,CAAAC;oBAAA;kBAEA;gBACA,WAAAvF,KAAA,CAAAC,OAAA,CAAApB,QAAA,CAAAtG,IAAA;kBACA;kBACA2M,OAAA,CAAAxK,SAAA,GAAAmE,QAAA,CAAAtG,IAAA,CAAA+H,GAAA,WAAAgF,KAAA;oBACA,OAAA9I,aAAA,CAAAA,aAAA,KACA8I,KAAA;sBACAC,MAAA,SAAAD,KAAA,CAAAC,MAAA,gBAAAhB,QAAA,CAAAe,KAAA,CAAAC,MAAA,IAAAD,KAAA,CAAAC;oBAAA;kBAEA;gBACA;kBACAL,OAAA,CAAAxK,SAAA;gBACA;gBAEA,IAAAwK,OAAA,CAAAxK,SAAA,CAAA+H,MAAA;kBACAyC,OAAA,CAAA7I,QAAA,CAAAmJ,IAAA;gBACA;cACA;gBACAN,OAAA,CAAA7I,QAAA,CAAAC,KAAA,gBAAAuC,QAAA,CAAAzE,OAAA;gBACA8K,OAAA,CAAAxK,SAAA;cACA;cAAA2K,SAAA,CAAAvF,CAAA;cAAA;YAAA;cAAAuF,SAAA,CAAAtF,CAAA;cAAAqF,GAAA,GAAAC,SAAA,CAAAjF,CAAA;cAEA5B,OAAA,CAAAlC,KAAA,cAAA8I,GAAA;cACAF,OAAA,CAAA7I,QAAA,CAAAC,KAAA;cACA4I,OAAA,CAAAxK,SAAA;YAAA;cAAA2K,SAAA,CAAAtF,CAAA;cAEAmF,OAAA,CAAAvK,gBAAA;cAAA,OAAA0K,SAAA,CAAA1E,CAAA;YAAA;cAAA,OAAA0E,SAAA,CAAAzE,CAAA;UAAA;QAAA,GAAAuE,QAAA;MAAA;IAEA;IAEA;IACAM,iBAAA,WAAAA,kBAAAnM,OAAA;MACA,IAAAA,OAAA;QACA,KAAAsB,aAAA,QAAAF,SAAA,CAAAgL,IAAA,WAAAC,GAAA;UAAA,OAAAA,GAAA,CAAAnL,EAAA,KAAAlB,OAAA;QAAA;QACA,SAAAsB,aAAA;UACA;UACA,KAAAvB,UAAA,CAAAE,SAAA,QAAAqB,aAAA,CAAAvC,IAAA;UACA,KAAAgB,UAAA,CAAAG,QAAA,QAAAoB,aAAA,CAAAgL,GAAA;;UAEA;UACA,UAAAvM,UAAA,CAAAP,aAAA;YACA,KAAA+M,oBAAA,MAAAjL,aAAA,CAAAvC,IAAA,OAAAuC,aAAA,CAAAgL,GAAA;UACA;;UAEA;UACA,KAAAE,kBAAA,MAAAlL,aAAA,CAAAvC,IAAA;QACA;MACA;QACA,KAAAuC,aAAA;QACA,KAAAvB,UAAA,CAAAE,SAAA;QACA,KAAAF,UAAA,CAAAG,QAAA;MACA;IACA;IAEA;IACAqM,oBAAA,WAAAA,qBAAAtM,SAAA,EAAAC,QAAA;MACA;MACA,IAAAuM,QAAA,GAAAxM,SAAA,CAAAsE,OAAA,uBAAAD,WAAA;;MAEA;MACA,IAAApE,QAAA,IAAAA,QAAA;QACAuM,QAAA,UAAAvM,QAAA,CAAAqE,OAAA;MACA;;MAEA;MACA,IAAAmI,SAAA,GAAA/C,IAAA,CAAAgD,GAAA,GAAAC,QAAA,GAAAC,KAAA;MACA,KAAA9M,UAAA,CAAAP,aAAA,MAAA6E,MAAA,CAAAoI,QAAA,OAAApI,MAAA,CAAAqI,SAAA;IACA;IAEA;IACAF,kBAAA,WAAAA,mBAAAvM,SAAA;MACA,IAAA6M,OAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;;MAEA;MACA,SAAAC,EAAA,MAAAC,eAAA,GAAAC,MAAA,CAAAC,OAAA,CAAAJ,OAAA,GAAAC,EAAA,GAAAC,eAAA,CAAA7D,MAAA,EAAA4D,EAAA;QAAA,IAAAI,kBAAA,GAAAC,cAAA,CAAAJ,eAAA,CAAAD,EAAA;UAAAM,GAAA,GAAAF,kBAAA;UAAAnC,IAAA,GAAAmC,kBAAA;QACA,IAAAlN,SAAA,CAAAqE,WAAA,GAAAuC,QAAA,CAAAwG,GAAA;UACA,KAAAtN,UAAA,CAAAI,WAAA,GAAA6K,IAAA;UACA;QACA;MACA;IACA;IAEA;IACAsC,eAAA,WAAAA,gBAAArB,MAAA;MACA,KAAAA,MAAA,IAAAA,MAAA;;MAEA;MACA,IAAAsB,IAAA,UAAAtB,MAAA,gBAAAhB,QAAA,CAAAgB,MAAA,IAAAA,MAAA;MACA,IAAAuB,KAAA,CAAAD,IAAA;;MAEA;MACA,IAAAA,IAAA;QACA,UAAAlJ,MAAA,CAAAkJ,IAAA;MACA;;MAEA;MACA,IAAAE,MAAA,GAAAF,IAAA;MACA,UAAAlJ,MAAA,CAAAoJ,MAAA,CAAAC,OAAA;IACA;IAEA;IACA/H,sBAAA,WAAAA,uBAAAL,QAAA;MACA,IAAAqI,WAAA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,WAAA,CAAArI,QAAA,KAAAA,QAAA;IACA;IAEA;IACAsI,kCAAA,WAAAA,mCAAAxN,QAAA;MAAA,OAAA4F,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAA2H,SAAA;QAAA,IAAAtI,QAAA,EAAAuI,GAAA;QAAA,OAAA7H,YAAA,GAAAK,CAAA,WAAAyH,SAAA;UAAA,kBAAAA,SAAA,CAAAvH,CAAA;YAAA;cAAAuH,SAAA,CAAAtH,CAAA;cAAAsH,SAAA,CAAAvH,CAAA;cAAA,OAEAzI,sBAAA,CAAAqC,QAAA;YAAA;cAAAmF,QAAA,GAAAwI,SAAA,CAAAjH,CAAA;cAAA,MACAvB,QAAA,CAAAC,IAAA,cAAAD,QAAA,CAAAtG,IAAA;gBAAA8O,SAAA,CAAAvH,CAAA;gBAAA;cAAA;cAAA,OAAAuH,SAAA,CAAAzG,CAAA,IACA/B,QAAA,CAAAtG,IAAA,CAAA+O,mBAAA;YAAA;cAAAD,SAAA,CAAAvH,CAAA;cAAA;YAAA;cAAAuH,SAAA,CAAAtH,CAAA;cAAAqH,GAAA,GAAAC,SAAA,CAAAjH,CAAA;cAGA5B,OAAA,CAAA+I,IAAA,gBAAAH,GAAA;YAAA;cAAA,OAAAC,SAAA,CAAAzG,CAAA,IAEA;UAAA;QAAA,GAAAuG,QAAA;MAAA;IACA;IAEA;IAEA;IACAK,eAAA,WAAAA,gBAAA/J,OAAA;MACA,IAAAA,OAAA;QACA;QACA,KAAAgK,iBAAA;MACA;QACA;QACA,KAAApO,UAAA,CAAAS,WAAA;QACA,KAAAiB,iBAAA;MACA;IACA;IAEA;IACA0M,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,OAAA;MAAA,OAAApI,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAAmI,SAAA;QAAA,IAAA9I,QAAA,EAAA+I,GAAA;QAAA,OAAArI,YAAA,GAAAK,CAAA,WAAAiI,SAAA;UAAA,kBAAAA,SAAA,CAAA/H,CAAA;YAAA;cACA4H,OAAA,CAAA5M,oBAAA;cAAA+M,SAAA,CAAA9H,CAAA;cAAA8H,SAAA,CAAA/H,CAAA;cAAA,OAEA3H,sBAAA;gBACAa,MAAA;cACA;YAAA;cAFA6F,QAAA,GAAAgJ,SAAA,CAAAzH,CAAA;cAIA,IAAAvB,QAAA,CAAAC,IAAA;gBACA,IAAAD,QAAA,CAAAtG,IAAA,IAAAsG,QAAA,CAAAtG,IAAA,CAAA8H,IAAA;kBACAqH,OAAA,CAAA7M,aAAA,GAAAgE,QAAA,CAAAtG,IAAA,CAAA8H,IAAA;gBACA,WAAAL,KAAA,CAAAC,OAAA,CAAApB,QAAA,CAAAtG,IAAA;kBACAmP,OAAA,CAAA7M,aAAA,GAAAgE,QAAA,CAAAtG,IAAA;gBACA;kBACAmP,OAAA,CAAA7M,aAAA;gBACA;gBAEA,IAAA6M,OAAA,CAAA7M,aAAA,CAAA4H,MAAA;kBACAiF,OAAA,CAAArL,QAAA,CAAAmJ,IAAA;gBACA;cACA;gBACAkC,OAAA,CAAArL,QAAA,CAAAC,KAAA,mBAAAuC,QAAA,CAAAzE,OAAA;gBACAsN,OAAA,CAAA7M,aAAA;cACA;cAAAgN,SAAA,CAAA/H,CAAA;cAAA;YAAA;cAAA+H,SAAA,CAAA9H,CAAA;cAAA6H,GAAA,GAAAC,SAAA,CAAAzH,CAAA;cAEA5B,OAAA,CAAAlC,KAAA,iBAAAsL,GAAA;cACAF,OAAA,CAAArL,QAAA,CAAAC,KAAA;cACAoL,OAAA,CAAA7M,aAAA;YAAA;cAAAgN,SAAA,CAAA9H,CAAA;cAEA2H,OAAA,CAAA5M,oBAAA;cAAA,OAAA+M,SAAA,CAAAlH,CAAA;YAAA;cAAA,OAAAkH,SAAA,CAAAjH,CAAA;UAAA;QAAA,GAAA+G,QAAA;MAAA;IAEA;IAEA;IACAG,qBAAA,WAAAA,sBAAAlL,QAAA;MACA,IAAAA,QAAA;QACA,KAAA7B,iBAAA,QAAAF,aAAA,CAAA6K,IAAA,WAAAqC,MAAA;UAAA,OAAAA,MAAA,CAAAnL,QAAA,KAAAA,QAAA;QAAA;QACA,SAAA7B,iBAAA;UACA;UACA,KAAAiN,mBAAA,CAAApL,QAAA;QACA;MACA;QACA,KAAA7B,iBAAA;MACA;IACA;IAEA;IACAiN,mBAAA,WAAAA,oBAAApL,QAAA;MAAA,IAAAqL,OAAA;MAAA,OAAA3I,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAA0I,SAAA;QAAA,IAAArJ,QAAA,EAAAsJ,GAAA;QAAA,OAAA5I,YAAA,GAAAK,CAAA,WAAAwI,SAAA;UAAA,kBAAAA,SAAA,CAAAtI,CAAA;YAAA;cAAAsI,SAAA,CAAArI,CAAA;cAAAqI,SAAA,CAAAtI,CAAA;cAAA,OAEA1H,kBAAA,CAAAwE,QAAA;YAAA;cAAAiC,QAAA,GAAAuJ,SAAA,CAAAhI,CAAA;cACA,IAAAvB,QAAA,CAAAC,IAAA,cAAAD,QAAA,CAAAtG,IAAA;gBACA0P,OAAA,CAAAlN,iBAAA,GAAA8D,QAAA,CAAAtG,IAAA;cACA;cAAA6P,SAAA,CAAAtI,CAAA;cAAA;YAAA;cAAAsI,SAAA,CAAArI,CAAA;cAAAoI,GAAA,GAAAC,SAAA,CAAAhI,CAAA;cAEA5B,OAAA,CAAA+I,IAAA,mBAAAY,GAAA;YAAA;cAAA,OAAAC,SAAA,CAAAxH,CAAA;UAAA;QAAA,GAAAsH,QAAA;MAAA;IAEA;EAGA;EACAG,OAAA,WAAAA,QAAA;IACA,KAAAjN,OAAA;IACA;IACA,KAAAM,aAAA;IACA;IACA,KAAA+L,iBAAA;EACA;EACAa,QAAA,EAAA9L,aAAA,KACAvF,UAAA,EACA,OACA,EACA;EACAsR,KAAA;IACA;IACA,gCAAAC,mBAAAC,WAAA,EAAAC,WAAA;MAAA,IAAAC,OAAA;MACA,IAAAF,WAAA,QAAAA,WAAA,KAAAC,WAAA;QACA,KAAAxB,kCAAA,CAAAuB,WAAA,EAAA7M,IAAA,WAAAgD,QAAA;UACA+J,OAAA,CAAAtP,UAAA,CAAAM,oBAAA,GAAAiF,QAAA;QACA;MACA,WAAA6J,WAAA;QACA;QACA,KAAApP,UAAA,CAAAM,oBAAA;MACA;IACA;IACA;IACA,+BAAAiP,kBAAAH,WAAA,EAAAC,WAAA;MAAA,IAAAG,OAAA;MACA,IAAAJ,WAAA,QAAAA,WAAA,KAAAC,WAAA;QACA,KAAAxB,kCAAA,CAAAuB,WAAA,EAAA7M,IAAA,WAAAgD,QAAA;UACAiK,OAAA,CAAAtO,SAAA,CAAAZ,oBAAA,GAAAiF,QAAA;QACA;MACA,WAAA6J,WAAA;QACA,KAAAlO,SAAA,CAAAZ,oBAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}