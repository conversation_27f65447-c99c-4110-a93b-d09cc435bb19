package tech.tongdao.ccsp.management.modules.swarm.converter;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;
import tech.tongdao.ccsp.management.modules.swarm.entity.ApplicationContainerConfigEntity;
import tech.tongdao.ccsp.management.modules.swarm.entity.BusinessContainerDeploymentEntity;
import tech.tongdao.ccsp.management.modules.swarm.entity.BusinessContainerRelEntity;
import tech.tongdao.ccsp.management.modules.swarm.entity.ContainerInstanceEntity;
import tech.tongdao.ccsp.management.modules.swarm.entity.DockerImageEntity;
import tech.tongdao.ccsp.management.modules.swarm.entity.SwarmNodeEntity;
import tech.tongdao.ccsp.management.modules.swarm.form.ApplicationContainerConfigForm;
import tech.tongdao.ccsp.management.modules.swarm.form.BusinessContainerDeploymentForm;
import tech.tongdao.ccsp.management.modules.swarm.form.BusinessContainerRelForm;
import tech.tongdao.ccsp.management.modules.swarm.form.ContainerInstanceForm;
import tech.tongdao.ccsp.management.modules.swarm.form.DockerImageForm;
import tech.tongdao.ccsp.management.modules.swarm.form.SwarmNodeForm;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-02T17:47:49+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.9 (Oracle Corporation)"
)
@Component
public class SwarmEntityMapperImpl implements SwarmEntityMapper {

    @Override
    public SwarmNodeForm toSwarmNodeForm(SwarmNodeEntity entity) {
        if ( entity == null ) {
            return null;
        }

        SwarmNodeForm swarmNodeForm = new SwarmNodeForm();

        swarmNodeForm.setId( entity.getId() );
        swarmNodeForm.setNodeId( entity.getNodeId() );
        swarmNodeForm.setNodeName( entity.getNodeName() );
        swarmNodeForm.setRole( entity.getRole() );
        swarmNodeForm.setStatus( entity.getStatus() );
        swarmNodeForm.setAvailability( entity.getAvailability() );
        swarmNodeForm.setIpAddress( entity.getIpAddress() );
        swarmNodeForm.setHostname( entity.getHostname() );
        swarmNodeForm.setDockerVersion( entity.getDockerVersion() );
        swarmNodeForm.setOsType( entity.getOsType() );
        swarmNodeForm.setArchitecture( entity.getArchitecture() );
        swarmNodeForm.setCpuCores( entity.getCpuCores() );
        swarmNodeForm.setMemoryMb( entity.getMemoryMb() );
        swarmNodeForm.setDiskGb( entity.getDiskGb() );
        swarmNodeForm.setLabels( entity.getLabels() );
        swarmNodeForm.setLastHeartbeat( entity.getLastHeartbeat() );
        swarmNodeForm.setCreateTime( entity.getCreateTime() );
        swarmNodeForm.setUpdateTime( entity.getUpdateTime() );

        return swarmNodeForm;
    }

    @Override
    public List<SwarmNodeForm> toSwarmNodeFormList(List<SwarmNodeEntity> entities) {
        if ( entities == null ) {
            return null;
        }

        List<SwarmNodeForm> list = new ArrayList<SwarmNodeForm>( entities.size() );
        for ( SwarmNodeEntity swarmNodeEntity : entities ) {
            list.add( toSwarmNodeForm( swarmNodeEntity ) );
        }

        return list;
    }

    @Override
    public ContainerInstanceForm toContainerInstanceForm(ContainerInstanceEntity entity) {
        if ( entity == null ) {
            return null;
        }

        ContainerInstanceForm containerInstanceForm = new ContainerInstanceForm();

        containerInstanceForm.setApplicationId( entity.getApplicationId() );
        containerInstanceForm.setId( entity.getId() );
        containerInstanceForm.setContainerId( entity.getContainerId() );
        containerInstanceForm.setServiceId( entity.getServiceId() );
        containerInstanceForm.setContainerName( entity.getContainerName() );
        containerInstanceForm.setImageId( entity.getImageId() );
        containerInstanceForm.setImageName( entity.getImageName() );
        containerInstanceForm.setImageTag( entity.getImageTag() );
        containerInstanceForm.setNodeId( entity.getNodeId() );
        containerInstanceForm.setNodeName( entity.getNodeName() );
        containerInstanceForm.setStatus( entity.getStatus() );
        containerInstanceForm.setPortMappings( entity.getPortMappings() );
        containerInstanceForm.setEnvironmentVars( entity.getEnvironmentVars() );
        containerInstanceForm.setVolumeMounts( entity.getVolumeMounts() );
        containerInstanceForm.setResourceLimits( entity.getResourceLimits() );
        containerInstanceForm.setDeployConfig( entity.getDeployConfig() );
        containerInstanceForm.setDeployedBy( entity.getDeployedBy() );
        containerInstanceForm.setDeployTime( entity.getDeployTime() );
        containerInstanceForm.setStartTime( entity.getStartTime() );
        containerInstanceForm.setStopTime( entity.getStopTime() );
        containerInstanceForm.setCreateTime( entity.getCreateTime() );
        containerInstanceForm.setUpdateTime( entity.getUpdateTime() );

        return containerInstanceForm;
    }

    @Override
    public List<ContainerInstanceForm> toContainerInstanceFormList(List<ContainerInstanceEntity> entities) {
        if ( entities == null ) {
            return null;
        }

        List<ContainerInstanceForm> list = new ArrayList<ContainerInstanceForm>( entities.size() );
        for ( ContainerInstanceEntity containerInstanceEntity : entities ) {
            list.add( toContainerInstanceForm( containerInstanceEntity ) );
        }

        return list;
    }

    @Override
    public DockerImageForm toDockerImageForm(DockerImageEntity entity) {
        if ( entity == null ) {
            return null;
        }

        DockerImageForm dockerImageForm = new DockerImageForm();

        dockerImageForm.setId( entity.getId() );
        dockerImageForm.setName( entity.getName() );
        dockerImageForm.setTag( entity.getTag() );
        dockerImageForm.setRepository( entity.getRepository() );
        dockerImageForm.setDescription( entity.getDescription() );
        dockerImageForm.setSizeMb( entity.getSizeMb() );
        dockerImageForm.setArchitecture( entity.getArchitecture() );
        dockerImageForm.setOs( entity.getOs() );
        dockerImageForm.setStatus( entity.getStatus() );
        dockerImageForm.setCreatedBy( entity.getCreatedBy() );
        dockerImageForm.setCreateTime( entity.getCreateTime() );
        dockerImageForm.setUpdateTime( entity.getUpdateTime() );

        return dockerImageForm;
    }

    @Override
    public List<DockerImageForm> toDockerImageFormList(List<DockerImageEntity> entities) {
        if ( entities == null ) {
            return null;
        }

        List<DockerImageForm> list = new ArrayList<DockerImageForm>( entities.size() );
        for ( DockerImageEntity dockerImageEntity : entities ) {
            list.add( toDockerImageForm( dockerImageEntity ) );
        }

        return list;
    }

    @Override
    public ContainerInstanceEntity toContainerInstanceEntity(ContainerInstanceForm form) {
        if ( form == null ) {
            return null;
        }

        ContainerInstanceEntity containerInstanceEntity = new ContainerInstanceEntity();

        containerInstanceEntity.setId( form.getId() );
        containerInstanceEntity.setApplicationId( form.getApplicationId() );
        containerInstanceEntity.setContainerId( form.getContainerId() );
        containerInstanceEntity.setServiceId( form.getServiceId() );
        containerInstanceEntity.setContainerName( form.getContainerName() );
        containerInstanceEntity.setImageId( form.getImageId() );
        containerInstanceEntity.setImageName( form.getImageName() );
        containerInstanceEntity.setImageTag( form.getImageTag() );
        containerInstanceEntity.setNodeId( form.getNodeId() );
        containerInstanceEntity.setNodeName( form.getNodeName() );
        containerInstanceEntity.setStatus( form.getStatus() );
        containerInstanceEntity.setPortMappings( form.getPortMappings() );
        containerInstanceEntity.setEnvironmentVars( form.getEnvironmentVars() );
        containerInstanceEntity.setVolumeMounts( form.getVolumeMounts() );
        containerInstanceEntity.setResourceLimits( form.getResourceLimits() );
        containerInstanceEntity.setDeployConfig( form.getDeployConfig() );
        containerInstanceEntity.setDeployedBy( form.getDeployedBy() );
        containerInstanceEntity.setDeployTime( form.getDeployTime() );
        containerInstanceEntity.setStartTime( form.getStartTime() );
        containerInstanceEntity.setStopTime( form.getStopTime() );
        containerInstanceEntity.setCreateTime( form.getCreateTime() );
        containerInstanceEntity.setUpdateTime( form.getUpdateTime() );

        return containerInstanceEntity;
    }

    @Override
    public DockerImageEntity toDockerImageEntity(DockerImageForm form) {
        if ( form == null ) {
            return null;
        }

        DockerImageEntity dockerImageEntity = new DockerImageEntity();

        dockerImageEntity.setId( form.getId() );
        dockerImageEntity.setName( form.getName() );
        dockerImageEntity.setTag( form.getTag() );
        dockerImageEntity.setRepository( form.getRepository() );
        dockerImageEntity.setDescription( form.getDescription() );
        dockerImageEntity.setSizeMb( form.getSizeMb() );
        dockerImageEntity.setArchitecture( form.getArchitecture() );
        dockerImageEntity.setOs( form.getOs() );
        dockerImageEntity.setStatus( form.getStatus() );
        dockerImageEntity.setCreatedBy( form.getCreatedBy() );
        dockerImageEntity.setCreateTime( form.getCreateTime() );
        dockerImageEntity.setUpdateTime( form.getUpdateTime() );

        return dockerImageEntity;
    }

    @Override
    public SwarmNodeEntity toSwarmNodeEntity(SwarmNodeForm form) {
        if ( form == null ) {
            return null;
        }

        SwarmNodeEntity swarmNodeEntity = new SwarmNodeEntity();

        swarmNodeEntity.setId( form.getId() );
        swarmNodeEntity.setNodeId( form.getNodeId() );
        swarmNodeEntity.setNodeName( form.getNodeName() );
        swarmNodeEntity.setRole( form.getRole() );
        swarmNodeEntity.setStatus( form.getStatus() );
        swarmNodeEntity.setAvailability( form.getAvailability() );
        swarmNodeEntity.setIpAddress( form.getIpAddress() );
        swarmNodeEntity.setHostname( form.getHostname() );
        swarmNodeEntity.setDockerVersion( form.getDockerVersion() );
        swarmNodeEntity.setOsType( form.getOsType() );
        swarmNodeEntity.setArchitecture( form.getArchitecture() );
        swarmNodeEntity.setCpuCores( form.getCpuCores() );
        swarmNodeEntity.setMemoryMb( form.getMemoryMb() );
        swarmNodeEntity.setDiskGb( form.getDiskGb() );
        swarmNodeEntity.setLabels( form.getLabels() );
        swarmNodeEntity.setLastHeartbeat( form.getLastHeartbeat() );
        swarmNodeEntity.setCreateTime( form.getCreateTime() );
        swarmNodeEntity.setUpdateTime( form.getUpdateTime() );

        return swarmNodeEntity;
    }

    @Override
    public BusinessContainerRelForm toBusinessContainerRelForm(BusinessContainerRelEntity entity) {
        if ( entity == null ) {
            return null;
        }

        BusinessContainerRelForm businessContainerRelForm = new BusinessContainerRelForm();

        businessContainerRelForm.setId( entity.getId() );
        businessContainerRelForm.setBusinessId( entity.getBusinessId() );
        businessContainerRelForm.setContainerInstanceId( entity.getContainerInstanceId() );
        businessContainerRelForm.setDeploymentType( entity.getDeploymentType() );
        businessContainerRelForm.setPriority( entity.getPriority() );
        businessContainerRelForm.setHealthCheckEnabled( entity.getHealthCheckEnabled() );
        businessContainerRelForm.setAutoScaleEnabled( entity.getAutoScaleEnabled() );
        businessContainerRelForm.setCreateTime( entity.getCreateTime() );
        businessContainerRelForm.setUpdateTime( entity.getUpdateTime() );
        businessContainerRelForm.setCreatedBy( entity.getCreatedBy() );

        return businessContainerRelForm;
    }

    @Override
    public List<BusinessContainerRelForm> toBusinessContainerRelFormList(List<BusinessContainerRelEntity> entities) {
        if ( entities == null ) {
            return null;
        }

        List<BusinessContainerRelForm> list = new ArrayList<BusinessContainerRelForm>( entities.size() );
        for ( BusinessContainerRelEntity businessContainerRelEntity : entities ) {
            list.add( toBusinessContainerRelForm( businessContainerRelEntity ) );
        }

        return list;
    }

    @Override
    public BusinessContainerRelEntity toBusinessContainerRelEntity(BusinessContainerRelForm form) {
        if ( form == null ) {
            return null;
        }

        BusinessContainerRelEntity businessContainerRelEntity = new BusinessContainerRelEntity();

        businessContainerRelEntity.setId( form.getId() );
        businessContainerRelEntity.setBusinessId( form.getBusinessId() );
        businessContainerRelEntity.setContainerInstanceId( form.getContainerInstanceId() );
        businessContainerRelEntity.setDeploymentType( form.getDeploymentType() );
        businessContainerRelEntity.setPriority( form.getPriority() );
        businessContainerRelEntity.setHealthCheckEnabled( form.getHealthCheckEnabled() );
        businessContainerRelEntity.setAutoScaleEnabled( form.getAutoScaleEnabled() );
        businessContainerRelEntity.setCreateTime( form.getCreateTime() );
        businessContainerRelEntity.setUpdateTime( form.getUpdateTime() );
        businessContainerRelEntity.setCreatedBy( form.getCreatedBy() );

        return businessContainerRelEntity;
    }

    @Override
    public BusinessContainerDeploymentForm toBusinessContainerDeploymentForm(BusinessContainerDeploymentEntity entity) {
        if ( entity == null ) {
            return null;
        }

        BusinessContainerDeploymentForm businessContainerDeploymentForm = new BusinessContainerDeploymentForm();

        businessContainerDeploymentForm.setId( entity.getId() );
        businessContainerDeploymentForm.setBusinessId( entity.getBusinessId() );
        businessContainerDeploymentForm.setDeploymentName( entity.getDeploymentName() );
        businessContainerDeploymentForm.setDeploymentVersion( entity.getDeploymentVersion() );
        businessContainerDeploymentForm.setImageId( entity.getImageId() );
        businessContainerDeploymentForm.setTargetReplicas( entity.getTargetReplicas() );
        businessContainerDeploymentForm.setCurrentReplicas( entity.getCurrentReplicas() );
        businessContainerDeploymentForm.setDeploymentConfig( entity.getDeploymentConfig() );
        businessContainerDeploymentForm.setStatus( entity.getStatus() );
        businessContainerDeploymentForm.setDeployedBy( entity.getDeployedBy() );
        businessContainerDeploymentForm.setDeployStartTime( entity.getDeployStartTime() );
        businessContainerDeploymentForm.setDeployCompleteTime( entity.getDeployCompleteTime() );
        businessContainerDeploymentForm.setLastScaleTime( entity.getLastScaleTime() );
        businessContainerDeploymentForm.setRollbackVersion( entity.getRollbackVersion() );
        businessContainerDeploymentForm.setCreateTime( entity.getCreateTime() );
        businessContainerDeploymentForm.setUpdateTime( entity.getUpdateTime() );

        return businessContainerDeploymentForm;
    }

    @Override
    public List<BusinessContainerDeploymentForm> toBusinessContainerDeploymentFormList(List<BusinessContainerDeploymentEntity> entities) {
        if ( entities == null ) {
            return null;
        }

        List<BusinessContainerDeploymentForm> list = new ArrayList<BusinessContainerDeploymentForm>( entities.size() );
        for ( BusinessContainerDeploymentEntity businessContainerDeploymentEntity : entities ) {
            list.add( toBusinessContainerDeploymentForm( businessContainerDeploymentEntity ) );
        }

        return list;
    }

    @Override
    public BusinessContainerDeploymentEntity toBusinessContainerDeploymentEntity(BusinessContainerDeploymentForm form) {
        if ( form == null ) {
            return null;
        }

        BusinessContainerDeploymentEntity businessContainerDeploymentEntity = new BusinessContainerDeploymentEntity();

        businessContainerDeploymentEntity.setId( form.getId() );
        businessContainerDeploymentEntity.setBusinessId( form.getBusinessId() );
        businessContainerDeploymentEntity.setDeploymentName( form.getDeploymentName() );
        businessContainerDeploymentEntity.setDeploymentVersion( form.getDeploymentVersion() );
        businessContainerDeploymentEntity.setImageId( form.getImageId() );
        businessContainerDeploymentEntity.setTargetReplicas( form.getTargetReplicas() );
        businessContainerDeploymentEntity.setCurrentReplicas( form.getCurrentReplicas() );
        businessContainerDeploymentEntity.setDeploymentConfig( form.getDeploymentConfig() );
        businessContainerDeploymentEntity.setStatus( form.getStatus() );
        businessContainerDeploymentEntity.setDeployedBy( form.getDeployedBy() );
        businessContainerDeploymentEntity.setDeployStartTime( form.getDeployStartTime() );
        businessContainerDeploymentEntity.setDeployCompleteTime( form.getDeployCompleteTime() );
        businessContainerDeploymentEntity.setLastScaleTime( form.getLastScaleTime() );
        businessContainerDeploymentEntity.setRollbackVersion( form.getRollbackVersion() );
        businessContainerDeploymentEntity.setCreateTime( form.getCreateTime() );
        businessContainerDeploymentEntity.setUpdateTime( form.getUpdateTime() );

        return businessContainerDeploymentEntity;
    }

    @Override
    public ApplicationContainerConfigForm toApplicationContainerConfigForm(ApplicationContainerConfigEntity entity) {
        if ( entity == null ) {
            return null;
        }

        ApplicationContainerConfigForm applicationContainerConfigForm = new ApplicationContainerConfigForm();

        applicationContainerConfigForm.setId( entity.getId() );
        applicationContainerConfigForm.setApplicationId( entity.getApplicationId() );
        applicationContainerConfigForm.setDefaultImageId( entity.getDefaultImageId() );
        applicationContainerConfigForm.setDefaultNodeSelector( entity.getDefaultNodeSelector() );
        applicationContainerConfigForm.setDefaultResourceLimits( entity.getDefaultResourceLimits() );
        applicationContainerConfigForm.setDefaultEnvironmentVars( entity.getDefaultEnvironmentVars() );
        applicationContainerConfigForm.setDeploymentStrategy( entity.getDeploymentStrategy() );
        applicationContainerConfigForm.setMaxInstancesPerBusiness( entity.getMaxInstancesPerBusiness() );
        applicationContainerConfigForm.setAutoScalingConfig( entity.getAutoScalingConfig() );
        applicationContainerConfigForm.setMonitoringConfig( entity.getMonitoringConfig() );
        applicationContainerConfigForm.setBackupStrategy( entity.getBackupStrategy() );
        applicationContainerConfigForm.setCreateTime( entity.getCreateTime() );
        applicationContainerConfigForm.setUpdateTime( entity.getUpdateTime() );
        applicationContainerConfigForm.setCreatedBy( entity.getCreatedBy() );

        return applicationContainerConfigForm;
    }

    @Override
    public List<ApplicationContainerConfigForm> toApplicationContainerConfigFormList(List<ApplicationContainerConfigEntity> entities) {
        if ( entities == null ) {
            return null;
        }

        List<ApplicationContainerConfigForm> list = new ArrayList<ApplicationContainerConfigForm>( entities.size() );
        for ( ApplicationContainerConfigEntity applicationContainerConfigEntity : entities ) {
            list.add( toApplicationContainerConfigForm( applicationContainerConfigEntity ) );
        }

        return list;
    }

    @Override
    public ApplicationContainerConfigEntity toApplicationContainerConfigEntity(ApplicationContainerConfigForm form) {
        if ( form == null ) {
            return null;
        }

        ApplicationContainerConfigEntity applicationContainerConfigEntity = new ApplicationContainerConfigEntity();

        applicationContainerConfigEntity.setId( form.getId() );
        applicationContainerConfigEntity.setApplicationId( form.getApplicationId() );
        applicationContainerConfigEntity.setDefaultImageId( form.getDefaultImageId() );
        applicationContainerConfigEntity.setDefaultNodeSelector( form.getDefaultNodeSelector() );
        applicationContainerConfigEntity.setDefaultResourceLimits( form.getDefaultResourceLimits() );
        applicationContainerConfigEntity.setDefaultEnvironmentVars( form.getDefaultEnvironmentVars() );
        applicationContainerConfigEntity.setDeploymentStrategy( form.getDeploymentStrategy() );
        applicationContainerConfigEntity.setMaxInstancesPerBusiness( form.getMaxInstancesPerBusiness() );
        applicationContainerConfigEntity.setAutoScalingConfig( form.getAutoScalingConfig() );
        applicationContainerConfigEntity.setMonitoringConfig( form.getMonitoringConfig() );
        applicationContainerConfigEntity.setBackupStrategy( form.getBackupStrategy() );
        applicationContainerConfigEntity.setCreateTime( form.getCreateTime() );
        applicationContainerConfigEntity.setUpdateTime( form.getUpdateTime() );
        applicationContainerConfigEntity.setCreatedBy( form.getCreatedBy() );

        return applicationContainerConfigEntity;
    }
}
