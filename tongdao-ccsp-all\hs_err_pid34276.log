#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 528482304 bytes. Error detail: G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3898), pid=34276, tid=12640
#
# JRE version:  (21.0.7+6) (build )
# Java VM: OpenJDK 64-Bit Server VM (21.0.7+6-b1038.58, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: git4idea.http.GitAskPassApp Username for 'http://git.tongdao.tech': 

Host: AMD Ryzen 7 PRO 7840HS w/ Radeon 780M Graphics , 16 cores, 31G,  Windows 11 , 64 bit Build 22621 (10.0.22621.5415)
Time: Tue Sep  2 16:48:26 2025  Windows 11 , 64 bit Build 22621 (10.0.22621.5415) elapsed time: 0.017572 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x00000204ada1afd0):  JavaThread "Unknown thread" [_thread_in_vm, id=12640, stack(0x0000007f52900000,0x0000007f52a00000) (1024K)]

Stack: [0x0000007f52900000,0x0000007f52a00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6e7c19]
V  [jvm.dll+0x8c5f53]
V  [jvm.dll+0x8c84ae]
V  [jvm.dll+0x8c8b93]
V  [jvm.dll+0x2899e6]
V  [jvm.dll+0x6e4455]
V  [jvm.dll+0x6d7f1a]
V  [jvm.dll+0x3640db]
V  [jvm.dll+0x36bca6]
V  [jvm.dll+0x3be046]
V  [jvm.dll+0x3be318]
V  [jvm.dll+0x33681c]
V  [jvm.dll+0x33750b]
V  [jvm.dll+0x88d3b9]
V  [jvm.dll+0x3cb218]
V  [jvm.dll+0x876408]
V  [jvm.dll+0x45ff5e]
V  [jvm.dll+0x461c41]
C  [jli.dll+0x52ab]
C  [ucrtbase.dll+0x29333]
C  [KERNEL32.DLL+0x1259d]
C  [ntdll.dll+0x5af78]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00007fff2f44d188, length=0, elements={
}

Java Threads: ( => current thread )
Total: 0

Other Threads:
  0x00000204afde9920 WorkerThread "GC Thread#0"                     [id=33916, stack(0x0000007f52a00000,0x0000007f52b00000) (1024K)]
  0x00000204afdfa400 ConcurrentGCThread "G1 Main Marker"            [id=30880, stack(0x0000007f52b00000,0x0000007f52c00000) (1024K)]
  0x00000204afdfae10 WorkerThread "G1 Conc#0"                       [id=30588, stack(0x0000007f52c00000,0x0000007f52d00000) (1024K)]

[error occurred during error reporting (printing all threads), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007fff2eb398f7]
VM state: not at safepoint (not fully initialized)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007fff2f4c1bc8] Heap_lock - owner thread: 0x00000204ada1afd0

Heap address: 0x000000060b800000, size: 8008 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000000000000000-0x0000000000000000-0x0000000000000000), size 0, SharedBaseAddress: 0x0000000800000000, ArchiveRelocationMode: 1.
Narrow klass base: 0x0000000000000000, Narrow klass shift: 0, Narrow klass range: 0x0

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 32 size 80 Howl #buckets 8 coarsen threshold 7372 Howl Bitmap #cards 1024 size 144 coarsen threshold 921 Card regions per heap region 1 cards per card region 8192

Heap:
 garbage-first heap   total 0K, used 0K [0x000000060b800000, 0x0000000800000000)
  region size 4096K, 0 young (0K), 0 survivors (0K)
 Metaspace       used 0K, committed 0K, reserved 0K
  class space    used 0K, committed 0K, reserved 0K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom

Card table byte_map: [0x00000204c45c0000,0x00000204c5570000] _byte_map_base: 0x00000204c1564000

Marking Bits: (CMBitMap*) 0x00000204afde9f30
 Bits: [0x00000204c5570000, 0x00000204cd290000)

GC Heap History (0 events):
No events

Dll operation events (1 events):
Event: 0.010 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2025.1\jbr\bin\java.dll

Deoptimization events (0 events):
No events

Classes loaded (0 events):
No events

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

ZGC Phase Switch (0 events):
No events

VM Operations (0 events):
No events

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (0 events):
No events


Dynamic libraries:
0x00007ff71e730000 - 0x00007ff71e73a000 	D:\Program Files\JetBrains\IntelliJ IDEA 2025.1\jbr\bin\java.exe
0x00007ffff3870000 - 0x00007ffff3a87000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffff3190000 - 0x00007ffff3254000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffff0f20000 - 0x00007ffff12f2000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffff0cd0000 - 0x00007ffff0de1000 	C:\Windows\System32\ucrtbase.dll
0x00007fffe8e20000 - 0x00007fffe8e38000 	D:\Program Files\JetBrains\IntelliJ IDEA 2025.1\jbr\bin\jli.dll
0x00007fffe8e70000 - 0x00007fffe8e8b000 	D:\Program Files\JetBrains\IntelliJ IDEA 2025.1\jbr\bin\VCRUNTIME140.dll
0x00007ffff2fd0000 - 0x00007ffff3181000 	C:\Windows\System32\USER32.dll
0x00007ffff1300000 - 0x00007ffff1326000 	C:\Windows\System32\win32u.dll
0x00007fffdc630000 - 0x00007fffdc8cb000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5547_none_27104afb73855772\COMCTL32.dll
0x00007ffff3750000 - 0x00007ffff3779000 	C:\Windows\System32\GDI32.dll
0x00007ffff3780000 - 0x00007ffff3827000 	C:\Windows\System32\msvcrt.dll
0x00007ffff1330000 - 0x00007ffff1453000 	C:\Windows\System32\gdi32full.dll
0x00007ffff1460000 - 0x00007ffff14fa000 	C:\Windows\System32\msvcp_win.dll
0x00007ffff1a10000 - 0x00007ffff1a41000 	C:\Windows\System32\IMM32.DLL
0x00007fffeb350000 - 0x00007fffeb35c000 	D:\Program Files\JetBrains\IntelliJ IDEA 2025.1\jbr\bin\vcruntime140_1.dll
0x00007fff4eee0000 - 0x00007fff4ef6d000 	D:\Program Files\JetBrains\IntelliJ IDEA 2025.1\jbr\bin\msvcp140.dll
0x00007fff2e7f0000 - 0x00007fff2f5b4000 	D:\Program Files\JetBrains\IntelliJ IDEA 2025.1\jbr\bin\server\jvm.dll
0x00007ffff1bd0000 - 0x00007ffff1c81000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffff1b20000 - 0x00007ffff1bc8000 	C:\Windows\System32\sechost.dll
0x00007ffff0ef0000 - 0x00007ffff0f18000 	C:\Windows\System32\bcrypt.dll
0x00007ffff2190000 - 0x00007ffff22a4000 	C:\Windows\System32\RPCRT4.dll
0x00007ffff1c90000 - 0x00007ffff1d01000 	C:\Windows\System32\WS2_32.dll
0x00007fffef7a0000 - 0x00007fffef7ed000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007fffe7b50000 - 0x00007fffe7b5a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007fffea020000 - 0x00007fffea054000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007fffef780000 - 0x00007fffef793000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007fffefa40000 - 0x00007fffefa58000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007fffe8ef0000 - 0x00007fffe8efa000 	D:\Program Files\JetBrains\IntelliJ IDEA 2025.1\jbr\bin\jimage.dll
0x00007fffee080000 - 0x00007fffee2b3000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffff22b0000 - 0x00007ffff2643000 	C:\Windows\System32\combase.dll
0x00007ffff34c0000 - 0x00007ffff3597000 	C:\Windows\System32\OLEAUT32.dll
0x00007fffcbb00000 - 0x00007fffcbb32000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ffff0e70000 - 0x00007ffff0eeb000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007fffe8e40000 - 0x00007fffe8e60000 	D:\Program Files\JetBrains\IntelliJ IDEA 2025.1\jbr\bin\java.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;D:\Program Files\JetBrains\IntelliJ IDEA 2025.1\jbr\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5547_none_27104afb73855772;D:\Program Files\JetBrains\IntelliJ IDEA 2025.1\jbr\bin\server

VM Arguments:
java_command: git4idea.http.GitAskPassApp Username for 'http://git.tongdao.tech': 
java_class_path (initial): D:/Program Files/JetBrains/IntelliJ IDEA 2025.1/plugins/vcs-git/lib/git4idea-rt.jar;D:/Program Files/JetBrains/IntelliJ IDEA 2025.1/lib/externalProcess-rt.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 13                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 528482304                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8396996608                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 7602480                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122027880                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122027880                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8396996608                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=D:\Program Files\Android\Android Studio\jbr
PATH=C:/Program Files/Git/mingw64/libexec/git-core;C:/Program Files/Git/mingw64/libexec/git-core;C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\bin;C:\Users\<USER>\bin;D:\server\uv-x86_64-pc-windows-msvc;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\dotnet\;D:\server\apache-maven-3.6.3\bin;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\Program Files\TortoiseGit\bin;D:\server\caddy;C:\Program Files\Tailscale\;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;D:\server\cloc;D:\server\iperf-3.1.3-win64;E:\WezTerm;C:\Windows\system32\config\systemprofile\.pyenv\pyenv-win\shims;C:\Windows\system32\config\systemprofile\.pyenv\pyenv-win\bin;C:\Program Files\Pandoc\;D:\server\docker;D:\Program Files\Android\Android Studio\jbr\bin;D:\android\sdk\platform-tools;D:\android\sdk\build-tools\36.0.0;D:\Program Files\Android\Android Studio\jbr\bin;C:\Users\<USER>\.local\bin;C:\Program Files\PowerShell\7-preview\preview;C:\Program Files\PowerShell\7-preview;D:\mcp-grafana;D:\server\cunzhi-cli-v0.3.3-windows-x86_64;C:\Users\<USER>\.rye\shims;C:\Program Files\Git\cmd;C:\Program Files\Git\usr\bin;C:\Users\<USER>\.local\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\scoop\shims;D:\Program Files\VanDyke Software\Clients\;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;D:\server\jdk\jdk-17.0.9\bin;C:\Program Files\dotnet\;C:\Program Files\Git\cmd;D:\server\apache-maven-3.6.3\bin;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\Program Files\TortoiseGit\bin;D:\server\caddy;C:\Program Files\Tailscale\;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;D:\server\cloc;D:\server\iperf-3.1.3-win64;D:\server\Python313\Scripts\;D:\server\Python313\;C:\Users\<USER>\AppData\Local\pnpm;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Trae\bin;d:\Trae\bin;D:\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\astral-sh.uv_Microsoft.Winget.Source_8wekyb3d8bbwe;D:\Program Files\JetBrains\IntelliJ IDEA 2025.1\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\.deno\bin;D:\CodeBuddy\bin;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\AppData\Local\Programs\nu\bin\;C:\Users\<USER>\AppData\Local\Programs\Kiro\bin;C:\Users\<USER>\AppData\Local\Programs\Qoder\bin
USERNAME=zhang
DISPLAY=:0.0
LC_ALL=en_US.UTF-8
TERM=xterm-256color
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
OS=Windows_NT
PROCESSOR_IDENTIFIER=AMD64 Family 25 Model 116 Stepping 1, AuthenticAMD
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

JNI global refs:
JNI global refs: 0, weak refs: 0

JNI global refs memory usage: 0, weak refs: 0

Process memory usage:
Resident Set Size: 13364K (0% of 32798088K total physical memory with 13011444K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.5415)
OS uptime: 0 days 23:30 hours
Hyper-V role detected

CPU: total 16 (initial active 16) (16 cores per cpu, 2 threads per core) family 25 model 116 stepping 1 microcode 0x0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4a, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, avx512f, avx512dq, avx512cd, avx512bw, avx512vl, sha, fma, vzeroupper, avx512_vpopcntdq, avx512_vpclmulqdq, avx512_vaes, avx512_vnni, clflush, clflushopt, avx512_vbmi2, avx512_vbmi, hv, rdtscp, rdpid, fsrm, gfni, avx512_bitalg, f16c, cet_ss, avx512_ifma
Processor Information for the first 16 processors :
  Max Mhz: 3801, Current Mhz: 3801, Mhz Limit: 3801

Memory: 4k page, system-wide physical 32029M (12706M free)
TotalPageFile size 40221M (AvailPageFile size 183M)
current process WorkingSet (physical memory assigned to process): 13M, peak: 13M
current process commit charge ("private bytes"): 71M, peak: 575M

vm_info: OpenJDK 64-Bit Server VM (21.0.7+6-b1038.58) for windows-amd64 JRE (21.0.7+6-b1038.58), built on 2025-07-07 by "builduser" with MS VC++ 16.10 / 16.11 (VS2019)

END.
