package tech.tongdao.ccsp.management.modules.swarm.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import tech.tongdao.ccsp.management.modules.device.form.ChsmForm;
import tech.tongdao.ccsp.management.modules.device.form.VsmForm;
import tech.tongdao.ccsp.management.modules.device.service.ChsmService;
import tech.tongdao.ccsp.management.modules.device.service.VsmService;
import tech.tongdao.ccsp.management.modules.swarm.entity.ContainerDeviceResourceRelEntity;
import tech.tongdao.ccsp.management.modules.swarm.entity.DeviceResourceUsageStatsEntity;
import tech.tongdao.ccsp.management.modules.swarm.form.ContainerDeviceResourceRelForm;
import tech.tongdao.ccsp.management.modules.swarm.form.DeviceResourceForm;
import tech.tongdao.ccsp.management.modules.swarm.mapper.ContainerDeviceResourceRelMapper;
import tech.tongdao.ccsp.management.modules.swarm.mapper.DeviceResourceUsageStatsMapper;
import tech.tongdao.ccsp.management.modules.swarm.service.ContainerDeviceResourceService;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 容器设备资源关联服务实现类
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Service
public class ContainerDeviceResourceServiceImpl extends ServiceImpl<ContainerDeviceResourceRelMapper, ContainerDeviceResourceRelEntity>
        implements ContainerDeviceResourceService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ContainerDeviceResourceServiceImpl.class);

    @Autowired
    private DeviceResourceUsageStatsMapper deviceResourceUsageStatsMapper;

    @Autowired
    private VsmService vsmService;

    @Autowired
    private ChsmService chsmService;

    @Override
    public List<DeviceResourceForm> getAvailableDeviceResources(String deviceResourceType, Long excludeContainerId) {
        try {
            LOGGER.info("获取可用设备资源列表，类型：{}，排除容器：{}", deviceResourceType, excludeContainerId);

            List<DeviceResourceForm> availableResources = new ArrayList<>();

            if ("VSM".equalsIgnoreCase(deviceResourceType)) {
                availableResources.addAll(getAvailableVsmResources(excludeContainerId));
            } else if ("CHSM".equalsIgnoreCase(deviceResourceType)) {
                availableResources.addAll(getAvailableChsmResources(excludeContainerId));
            } else if (StringUtils.isEmpty(deviceResourceType)) {
                // 获取所有类型的可用资源
                availableResources.addAll(getAvailableVsmResources(excludeContainerId));
                availableResources.addAll(getAvailableChsmResources(excludeContainerId));
            }

            LOGGER.info("获取到{}个可用设备资源", availableResources.size());
            return availableResources;

        } catch (Exception e) {
            LOGGER.error("获取可用设备资源列表失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public boolean isDeviceResourceAvailable(Long deviceResourceId, String deviceResourceType, String allocationType) {
        try {
            LOGGER.info("检查设备资源可用性，ID：{}，类型：{}，分配类型：{}", deviceResourceId, deviceResourceType, allocationType);

            // 查询设备资源使用统计
            LambdaQueryWrapper<DeviceResourceUsageStatsEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(DeviceResourceUsageStatsEntity::getDeviceResourceId, deviceResourceId)
                    .eq(DeviceResourceUsageStatsEntity::getDeviceResourceType, deviceResourceType);

            DeviceResourceUsageStatsEntity stats = deviceResourceUsageStatsMapper.selectOne(wrapper);
            if (stats == null) {
                LOGGER.warn("未找到设备资源使用统计，ID：{}", deviceResourceId);
                return false;
            }

            // 独占分配：检查是否有可用容量
            if ("exclusive".equalsIgnoreCase(allocationType)) {
                return stats.getAvailableCapacity() > 0;
            }

            // 共享分配：总是可用（根据业务需求调整）
            if ("shared".equalsIgnoreCase(allocationType)) {
                return true;
            }

            return stats.getAvailableCapacity() > 0;

        } catch (Exception e) {
            LOGGER.error("检查设备资源可用性失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean allocateDeviceResources(Long containerInstanceId, List<Long> deviceResourceIds,
                                           String deviceResourceType, String allocationType,
                                           String configData, Long createdBy) {
        try {
            LOGGER.info("为容器分配设备资源，容器ID：{}，设备资源：{}，类型：{}，分配类型：{}",
                    containerInstanceId, deviceResourceIds, deviceResourceType, allocationType);

            if (CollectionUtils.isEmpty(deviceResourceIds)) {
                LOGGER.warn("设备资源ID列表为空");
                return true; // 空列表认为分配成功
            }

            LocalDateTime now = LocalDateTime.now();
            List<ContainerDeviceResourceRelEntity> relEntities = new ArrayList<>();

            for (Long deviceResourceId : deviceResourceIds) {
                // 检查资源是否可用
                if (!isDeviceResourceAvailable(deviceResourceId, deviceResourceType, allocationType)) {
                    LOGGER.error("设备资源不可用，ID：{}", deviceResourceId);
                    return false;
                }

                // 创建关联记录
                ContainerDeviceResourceRelEntity relEntity = new ContainerDeviceResourceRelEntity();
                relEntity.setContainerInstanceId(containerInstanceId);
                relEntity.setDeviceResourceId(deviceResourceId);
                relEntity.setDeviceResourceType(deviceResourceType);
                relEntity.setAllocationType(allocationType);
                relEntity.setPriority(1);
                relEntity.setStatus("active");
                relEntity.setConfigData(configData);
                relEntity.setAllocatedAt(now);
                relEntity.setCreateTime(now);
                relEntity.setCreatedBy(createdBy);

                relEntities.add(relEntity);
            }

            // 批量插入关联记录
            boolean result = this.saveBatch(relEntities);
            if (result) {
                // 更新设备资源使用统计
                for (Long deviceResourceId : deviceResourceIds) {
                    updateDeviceResourceUsageStats(deviceResourceId, deviceResourceType);
                }
            }

            LOGGER.info("设备资源分配结果：{}", result);
            return result;

        } catch (Exception e) {
            LOGGER.error("分配设备资源失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean releaseDeviceResources(Long containerInstanceId) {
        try {
            LOGGER.info("释放容器的所有设备资源，容器ID：{}", containerInstanceId);

            // 查询容器关联的设备资源
            LambdaQueryWrapper<ContainerDeviceResourceRelEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ContainerDeviceResourceRelEntity::getContainerInstanceId, containerInstanceId)
                    .eq(ContainerDeviceResourceRelEntity::getStatus, "active");

            List<ContainerDeviceResourceRelEntity> relEntities = this.list(wrapper);
            if (CollectionUtils.isEmpty(relEntities)) {
                LOGGER.info("容器没有关联的设备资源");
                return true;
            }

            LocalDateTime now = LocalDateTime.now();

            // 更新关联记录状态
            LambdaUpdateWrapper<ContainerDeviceResourceRelEntity> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(ContainerDeviceResourceRelEntity::getContainerInstanceId, containerInstanceId)
                    .eq(ContainerDeviceResourceRelEntity::getStatus, "active")
                    .set(ContainerDeviceResourceRelEntity::getStatus, "inactive")
                    .set(ContainerDeviceResourceRelEntity::getReleasedAt, now)
                    .set(ContainerDeviceResourceRelEntity::getUpdateTime, now);

            boolean result = this.update(updateWrapper);
            if (result) {
                // 更新设备资源使用统计
                for (ContainerDeviceResourceRelEntity relEntity : relEntities) {
                    updateDeviceResourceUsageStats(relEntity.getDeviceResourceId(), relEntity.getDeviceResourceType());
                }
            }

            LOGGER.info("释放设备资源结果：{}", result);
            return result;

        } catch (Exception e) {
            LOGGER.error("释放设备资源失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean releaseDeviceResource(Long containerInstanceId, Long deviceResourceId) {
        try {
            LOGGER.info("释放指定设备资源，容器ID：{}，设备资源ID：{}", containerInstanceId, deviceResourceId);

            LocalDateTime now = LocalDateTime.now();

            LambdaUpdateWrapper<ContainerDeviceResourceRelEntity> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(ContainerDeviceResourceRelEntity::getContainerInstanceId, containerInstanceId)
                    .eq(ContainerDeviceResourceRelEntity::getDeviceResourceId, deviceResourceId)
                    .eq(ContainerDeviceResourceRelEntity::getStatus, "active")
                    .set(ContainerDeviceResourceRelEntity::getStatus, "inactive")
                    .set(ContainerDeviceResourceRelEntity::getReleasedAt, now)
                    .set(ContainerDeviceResourceRelEntity::getUpdateTime, now);

            boolean result = this.update(updateWrapper);
            if (result) {
                // 查询设备资源类型并更新统计
                LambdaQueryWrapper<ContainerDeviceResourceRelEntity> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(ContainerDeviceResourceRelEntity::getContainerInstanceId, containerInstanceId)
                        .eq(ContainerDeviceResourceRelEntity::getDeviceResourceId, deviceResourceId)
                        .last("LIMIT 1");

                ContainerDeviceResourceRelEntity relEntity = this.getOne(queryWrapper);
                if (relEntity != null) {
                    updateDeviceResourceUsageStats(deviceResourceId, relEntity.getDeviceResourceType());
                }
            }

            LOGGER.info("释放指定设备资源结果：{}", result);
            return result;

        } catch (Exception e) {
            LOGGER.error("释放指定设备资源失败", e);
            return false;
        }
    }

    @Override
    public List<ContainerDeviceResourceRelForm> getContainerDeviceResources(Long containerInstanceId) {
        try {
            LOGGER.info("获取容器关联的设备资源列表，容器ID：{}", containerInstanceId);

            LambdaQueryWrapper<ContainerDeviceResourceRelEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ContainerDeviceResourceRelEntity::getContainerInstanceId, containerInstanceId)
                    .eq(ContainerDeviceResourceRelEntity::getStatus, "active")
                    .orderByDesc(ContainerDeviceResourceRelEntity::getCreateTime);

            List<ContainerDeviceResourceRelEntity> relEntities = this.list(wrapper);
            List<ContainerDeviceResourceRelForm> result = new ArrayList<>();

            for (ContainerDeviceResourceRelEntity entity : relEntities) {
                ContainerDeviceResourceRelForm form = convertToForm(entity);
                result.add(form);
            }

            LOGGER.info("获取到{}个设备资源关联", result.size());
            return result;

        } catch (Exception e) {
            LOGGER.error("获取容器设备资源关联失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<ContainerDeviceResourceRelForm> getDeviceResourceContainers(Long deviceResourceId, String deviceResourceType) {
        try {
            LOGGER.info("获取设备资源关联的容器列表，设备资源ID：{}，类型：{}", deviceResourceId, deviceResourceType);

            LambdaQueryWrapper<ContainerDeviceResourceRelEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ContainerDeviceResourceRelEntity::getDeviceResourceId, deviceResourceId)
                    .eq(ContainerDeviceResourceRelEntity::getDeviceResourceType, deviceResourceType)
                    .eq(ContainerDeviceResourceRelEntity::getStatus, "active")
                    .orderByDesc(ContainerDeviceResourceRelEntity::getCreateTime);

            List<ContainerDeviceResourceRelEntity> relEntities = this.list(wrapper);
            List<ContainerDeviceResourceRelForm> result = new ArrayList<>();

            for (ContainerDeviceResourceRelEntity entity : relEntities) {
                ContainerDeviceResourceRelForm form = convertToForm(entity);
                result.add(form);
            }

            LOGGER.info("获取到{}个容器关联", result.size());
            return result;

        } catch (Exception e) {
            LOGGER.error("获取设备资源容器关联失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public void updateDeviceResourceUsageStats(Long deviceResourceId, String deviceResourceType) {
        try {
            LOGGER.debug("更新设备资源使用统计，ID：{}，类型：{}", deviceResourceId, deviceResourceType);

            // 统计活跃的容器关联数量
            LambdaQueryWrapper<ContainerDeviceResourceRelEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ContainerDeviceResourceRelEntity::getDeviceResourceId, deviceResourceId)
                    .eq(ContainerDeviceResourceRelEntity::getDeviceResourceType, deviceResourceType)
                    .eq(ContainerDeviceResourceRelEntity::getStatus, "active");

            int activeCount = (int) this.count(wrapper);

            // 查询或创建统计记录
            LambdaQueryWrapper<DeviceResourceUsageStatsEntity> statsWrapper = new LambdaQueryWrapper<>();
            statsWrapper.eq(DeviceResourceUsageStatsEntity::getDeviceResourceId, deviceResourceId)
                    .eq(DeviceResourceUsageStatsEntity::getDeviceResourceType, deviceResourceType);

            DeviceResourceUsageStatsEntity stats = deviceResourceUsageStatsMapper.selectOne(statsWrapper);
            if (stats == null) {
                // 创建新的统计记录
                stats = new DeviceResourceUsageStatsEntity();
                stats.setDeviceResourceId(deviceResourceId);
                stats.setDeviceResourceType(deviceResourceType);
                stats.setTotalCapacity(getDeviceResourceTotalCapacity(deviceResourceId, deviceResourceType));
                stats.setCreateTime(LocalDateTime.now());
            }

            // 更新统计数据
            stats.setActiveContainersCount(activeCount);
            stats.setUsedCapacity(activeCount);
            stats.setAvailableCapacity(Math.max(0, stats.getTotalCapacity() - activeCount));
            stats.setUpdateTime(LocalDateTime.now());

            if (activeCount > 0) {
                stats.setLastAllocatedAt(LocalDateTime.now());
            } else {
                stats.setLastReleasedAt(LocalDateTime.now());
            }

            // 保存或更新统计记录
            if (stats.getId() == null) {
                deviceResourceUsageStatsMapper.insert(stats);
            } else {
                deviceResourceUsageStatsMapper.updateById(stats);
            }

        } catch (Exception e) {
            LOGGER.error("更新设备资源使用统计失败", e);
        }
    }

    @Override
    public void batchUpdateDeviceResourceUsageStats(String deviceResourceType) {
        try {
            LOGGER.info("批量更新设备资源使用统计，类型：{}", deviceResourceType);

            LambdaQueryWrapper<DeviceResourceUsageStatsEntity> wrapper = new LambdaQueryWrapper<>();
            if (StringUtils.hasText(deviceResourceType)) {
                wrapper.eq(DeviceResourceUsageStatsEntity::getDeviceResourceType, deviceResourceType);
            }

            List<DeviceResourceUsageStatsEntity> statsList = deviceResourceUsageStatsMapper.selectList(wrapper);
            for (DeviceResourceUsageStatsEntity stats : statsList) {
                updateDeviceResourceUsageStats(stats.getDeviceResourceId(), stats.getDeviceResourceType());
            }

            LOGGER.info("批量更新设备资源使用统计完成，更新{}条记录", statsList.size());

        } catch (Exception e) {
            LOGGER.error("批量更新设备资源使用统计失败", e);
        }
    }

    @Override
    public Map<String, Object> getDeviceResourceUsageStats(Long deviceResourceId, String deviceResourceType) {
        try {
            LOGGER.debug("获取设备资源使用统计，ID：{}，类型：{}", deviceResourceId, deviceResourceType);

            LambdaQueryWrapper<DeviceResourceUsageStatsEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(DeviceResourceUsageStatsEntity::getDeviceResourceId, deviceResourceId)
                    .eq(DeviceResourceUsageStatsEntity::getDeviceResourceType, deviceResourceType);

            DeviceResourceUsageStatsEntity stats = deviceResourceUsageStatsMapper.selectOne(wrapper);
            if (stats == null) {
                return new HashMap<>();
            }

            Map<String, Object> result = new HashMap<>();
            result.put("deviceResourceId", stats.getDeviceResourceId());
            result.put("deviceResourceType", stats.getDeviceResourceType());
            result.put("totalCapacity", stats.getTotalCapacity());
            result.put("usedCapacity", stats.getUsedCapacity());
            result.put("availableCapacity", stats.getAvailableCapacity());
            result.put("activeContainersCount", stats.getActiveContainersCount());
            result.put("lastAllocatedAt", stats.getLastAllocatedAt());
            result.put("lastReleasedAt", stats.getLastReleasedAt());
            result.put("updateTime", stats.getUpdateTime());

            return result;

        } catch (Exception e) {
            LOGGER.error("获取设备资源使用统计失败", e);
            return new HashMap<>();
        }
    }

    @Override
    public String validateDeviceResourceAllocation(Long containerInstanceId, List<Long> deviceResourceIds,
                                                   String deviceResourceType, String allocationType) {
        try {
            LOGGER.debug("验证设备资源分配配置，容器ID：{}，设备资源：{}，类型：{}，分配类型：{}",
                    containerInstanceId, deviceResourceIds, deviceResourceType, allocationType);

            if (CollectionUtils.isEmpty(deviceResourceIds)) {
                return null; // 空列表认为验证通过
            }

            for (Long deviceResourceId : deviceResourceIds) {
                if (!isDeviceResourceAvailable(deviceResourceId, deviceResourceType, allocationType)) {
                    return "设备资源ID " + deviceResourceId + " 不可用";
                }
            }

            return null; // 验证通过

        } catch (Exception e) {
            LOGGER.error("验证设备资源分配配置失败", e);
            return "验证设备资源分配配置失败：" + e.getMessage();
        }
    }

    @Override
    public List<DeviceResourceForm> getRecommendedDeviceResources(Long applicationId, String deviceResourceType, Integer requiredCount) {
        try {
            LOGGER.info("获取推荐的设备资源，应用ID：{}，类型：{}，需要数量：{}", applicationId, deviceResourceType, requiredCount);

            List<DeviceResourceForm> availableResources = getAvailableDeviceResources(deviceResourceType, null);

            if (requiredCount == null || requiredCount <= 0) {
                return availableResources;
            }

            // 按可用容量和最后分配时间排序，优先推荐可用容量大且最近较少使用的资源
            availableResources.sort((a, b) -> {
                int capacityCompare = Integer.compare(b.getAvailableCapacity(), a.getAvailableCapacity());
                if (capacityCompare != 0) {
                    return capacityCompare;
                }

                if (a.getLastAllocatedAt() == null && b.getLastAllocatedAt() == null) {
                    return 0;
                }
                if (a.getLastAllocatedAt() == null) {
                    return -1; // 从未分配的优先
                }
                if (b.getLastAllocatedAt() == null) {
                    return 1;
                }
                return a.getLastAllocatedAt().compareTo(b.getLastAllocatedAt()); // 最早分配的优先
            });

            return availableResources.stream()
                    .limit(requiredCount)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            LOGGER.error("获取推荐设备资源失败", e);
            return new ArrayList<>();
        }
    }

    // 私有辅助方法

    private List<DeviceResourceForm> getAvailableVsmResources(Long excludeContainerId) {
        try {
            List<VsmForm> vsmList = vsmService.listAvailableHsmDevices();
            List<DeviceResourceForm> result = new ArrayList<>();

            for (VsmForm vsm : vsmList) {
                if (!"running".equals(vsm.getStatus())) {
                    continue; // 跳过非正常状态的设备
                }

                DeviceResourceForm resource = new DeviceResourceForm();
                resource.setId(vsm.getId());
                resource.setName(vsm.getName());
                resource.setType("VSM");
                resource.setIpAddress(vsm.getIp());
                resource.setPort(vsm.getManagementPort());
                resource.setManagementPort(vsm.getManagementPort());
                resource.setStatus(vsm.getStatus());
                resource.setVendorId(vsm.getVendorId());
                resource.setCreateTime(vsm.getCreateTime());

                // 检查可用性
                boolean available = isDeviceResourceAvailable(vsm.getId(), "VSM", "exclusive");
                resource.setAvailable(available);

                // 获取使用统计
                Map<String, Object> stats = getDeviceResourceUsageStats(vsm.getId(), "VSM");
                if (!stats.isEmpty()) {
                    resource.setTotalCapacity((Integer) stats.get("totalCapacity"));
                    resource.setUsedCapacity((Integer) stats.get("usedCapacity"));
                    resource.setAvailableCapacity((Integer) stats.get("availableCapacity"));
                    resource.setActiveContainersCount((Integer) stats.get("activeContainersCount"));
                    resource.setLastAllocatedAt((LocalDateTime) stats.get("lastAllocatedAt"));
                    resource.setLastReleasedAt((LocalDateTime) stats.get("lastReleasedAt"));
                } else {
                    resource.setTotalCapacity(1);
                    resource.setUsedCapacity(0);
                    resource.setAvailableCapacity(1);
                    resource.setActiveContainersCount(0);
                }

                result.add(resource);
            }

            return result;

        } catch (Exception e) {
            LOGGER.error("获取可用VSM资源失败", e);
            return new ArrayList<>();
        }
    }

    private List<DeviceResourceForm> getAvailableChsmResources(Long excludeContainerId) {
        try {
            List<ChsmForm> chsmList = chsmService.listAll();
            List<DeviceResourceForm> result = new ArrayList<>();

            for (ChsmForm chsm : chsmList) {
                if (!"running".equals(chsm.getStatus())) {
                    continue; // 跳过非正常状态的设备
                }

                DeviceResourceForm resource = new DeviceResourceForm();
                resource.setId(chsm.getId());
                resource.setName(chsm.getName());
                resource.setType("CHSM");
                resource.setIpAddress(chsm.getManagementIp());
                resource.setManagementPort(chsm.getManagementPort());
                resource.setStatus(chsm.getStatus());
                resource.setVendorId(chsm.getVendorId());
                resource.setCreateTime(chsm.getCreateTime());

                // 检查可用性
                boolean available = isDeviceResourceAvailable(chsm.getId(), "CHSM", "shared");
                resource.setAvailable(available);

                // 获取使用统计
                Map<String, Object> stats = getDeviceResourceUsageStats(chsm.getId(), "CHSM");
                if (!stats.isEmpty()) {
                    resource.setTotalCapacity((Integer) stats.get("totalCapacity"));
                    resource.setUsedCapacity((Integer) stats.get("usedCapacity"));
                    resource.setAvailableCapacity((Integer) stats.get("availableCapacity"));
                    resource.setActiveContainersCount((Integer) stats.get("activeContainersCount"));
                    resource.setLastAllocatedAt((LocalDateTime) stats.get("lastAllocatedAt"));
                    resource.setLastReleasedAt((LocalDateTime) stats.get("lastReleasedAt"));
                } else {
                    // 默认CHSM容量为10
                    resource.setTotalCapacity(10);
                    resource.setUsedCapacity(0);
                    resource.setAvailableCapacity(10);
                    resource.setActiveContainersCount(0);
                }

                result.add(resource);
            }

            return result;

        } catch (Exception e) {
            LOGGER.error("获取可用CHSM资源失败", e);
            return new ArrayList<>();
        }
    }

    private ContainerDeviceResourceRelForm convertToForm(ContainerDeviceResourceRelEntity entity) {
        if (entity == null) {
            return null;
        }

        ContainerDeviceResourceRelForm form = new ContainerDeviceResourceRelForm();
        form.setId(entity.getId());
        form.setContainerInstanceId(entity.getContainerInstanceId());
        form.setDeviceResourceId(entity.getDeviceResourceId());
        form.setDeviceResourceType(entity.getDeviceResourceType());
        form.setAllocationType(entity.getAllocationType());
        form.setPriority(entity.getPriority());
        form.setStatus(entity.getStatus());
        form.setConfigData(entity.getConfigData());
        form.setAllocatedAt(entity.getAllocatedAt());
        form.setReleasedAt(entity.getReleasedAt());
        form.setCreateTime(entity.getCreateTime());
        form.setUpdateTime(entity.getUpdateTime());
        form.setCreatedBy(entity.getCreatedBy());
        form.setRemark(entity.getRemark());

        return form;
    }

    private Integer getDeviceResourceTotalCapacity(Long deviceResourceId, String deviceResourceType) {
        try {
            if ("VSM".equalsIgnoreCase(deviceResourceType)) {
                return 1; // VSM默认容量为1
            } else if ("CHSM".equalsIgnoreCase(deviceResourceType)) {
                // 通过实体服务获取CHSM信息
                try {
                    return 10; // 暂时返回默认值，后续可以通过其他方式获取实际容量
                } catch (Exception e) {
                    LOGGER.warn("获取CHSM容量失败，使用默认值", e);
                    return 10;
                }
            }
            return 1;
        } catch (Exception e) {
            LOGGER.error("获取设备资源总容量失败", e);
            return 1;
        }
    }
}
