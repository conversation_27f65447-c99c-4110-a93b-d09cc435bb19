# Docker Swarm环境中Traefik初始化部署指南

## 概述

本指南提供在Docker Swarm集群中部署Traefik反向代理的完整方案，包括网络配置、服务发现和与现有容器部署API的集成。

## 1. 部署架构

### 1.1 部署节点选择
**推荐方案**：Traefik应该部署在**Manager节点**上，原因如下：
- Manager节点可以访问Swarm API进行服务发现
- 具有集群管理权限，能够监听服务变化
- 通常Manager节点数量较少且稳定，便于管理

### 1.2 网络架构
```
Internet/Intranet
       ↓
   Load Balancer (可选)
       ↓
Traefik (Manager节点)
       ↓
ccsp-network (Overlay网络)
       ↓
Application Services (Worker节点)
```

## 2. 前置准备

### 2.1 创建Overlay网络
```bash
# 创建ccsp-network overlay网络（如果不存在）
docker network create \
  --driver overlay \
  --attachable \
  --subnet=172.20.0.0/16 \
  ccsp-network

# 验证网络创建
docker network ls | grep ccsp-network
```

### 2.2 创建配置目录
```bash
# 在Manager节点上创建Traefik配置目录
sudo mkdir -p /opt/traefik/{config,data,logs}
sudo chmod 755 /opt/traefik
sudo chown -R $(whoami):$(whoami) /opt/traefik
```

## 3. Traefik配置文件

### 3.1 静态配置文件
创建 `/opt/traefik/config/traefik.yml`：

```yaml
# Traefik静态配置
global:
  checkNewVersion: false
  sendAnonymousUsage: false

# API和Dashboard配置
api:
  dashboard: true
  debug: true
  insecure: true  # 生产环境建议设置为false并配置TLS

# 入口点配置
entryPoints:
  web:
    address: ":80"
  websecure:
    address: ":443"
  traefik:
    address: ":8080"  # Dashboard端口

# 提供者配置
providers:
  # Docker Swarm提供者
  swarm:
    endpoint: "unix:///var/run/docker.sock"
    network: "ccsp-network"
    exposedByDefault: false
    watch: true
    swarmMode: true
    swarmModeRefreshSeconds: 15
    
  # 文件提供者（用于静态路由）
  file:
    filename: /etc/traefik/dynamic.yml
    watch: true

# 日志配置
log:
  level: INFO
  filePath: "/var/log/traefik/traefik.log"
  format: json

# 访问日志
accessLog:
  filePath: "/var/log/traefik/access.log"
  format: json

# 指标配置（可选）
metrics:
  prometheus:
    addEntryPointsLabels: true
    addServicesLabels: true

# 证书解析器（如果需要HTTPS）
certificatesResolvers:
  letsencrypt:
    acme:
      email: <EMAIL>
      storage: /data/acme.json
      httpChallenge:
        entryPoint: web
```

### 3.2 动态配置文件
创建 `/opt/traefik/config/dynamic.yml`：

```yaml
# Traefik动态配置
http:
  # 中间件定义
  middlewares:
    # 默认头部中间件
    default-headers:
      headers:
        frameDeny: true
        sslRedirect: true
        browserXssFilter: true
        contentTypeNosniff: true
        forceSTSHeader: true
        stsIncludeSubdomains: true
        stsPreload: true
        stsSeconds: 31536000
        
    # CORS中间件
    cors-headers:
      headers:
        accessControlAllowMethods:
          - GET
          - OPTIONS
          - PUT
          - POST
          - DELETE
        accessControlAllowOriginList:
          - "*"
        accessControlMaxAge: 100
        addVaryHeader: true
        
    # 认证中间件（示例）
    auth-basic:
      basicAuth:
        users:
          - "admin:$2y$10$..."  # 使用htpasswd生成

  # 路由定义
  routers:
    # Traefik Dashboard路由
    traefik-dashboard:
      rule: "Host(`traefik.ccsp.local`)"
      service: api@internal
      entryPoints:
        - web
      middlewares:
        - auth-basic

# TLS配置
tls:
  options:
    default:
      sslProtocols:
        - "TLSv1.2"
        - "TLSv1.3"
      minVersion: "VersionTLS12"
      cipherSuites:
        - "TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384"
        - "TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305"
        - "TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256"
```

## 4. Docker Compose部署文件

创建 `/opt/traefik/docker-compose.yml`：

```yaml
version: '3.8'

services:
  traefik:
    image: traefik:v3.0
    command:
      - --configfile=/etc/traefik/traefik.yml
    ports:
      - "80:80"      # HTTP
      - "443:443"    # HTTPS
      - "8080:8080"  # Dashboard
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./config/traefik.yml:/etc/traefik/traefik.yml:ro
      - ./config/dynamic.yml:/etc/traefik/dynamic.yml:ro
      - ./data:/data
      - ./logs:/var/log/traefik
    networks:
      - ccsp-network
    deploy:
      mode: replicated
      replicas: 1
      placement:
        constraints:
          - node.role == manager
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s
      update_config:
        parallelism: 1
        delay: 10s
        failure_action: rollback
        monitor: 60s
        max_failure_ratio: 0.3
      labels:
        - "traefik.enable=true"
        - "traefik.http.routers.traefik.rule=Host(`traefik.ccsp.local`)"
        - "traefik.http.routers.traefik.entrypoints=web"
        - "traefik.http.services.traefik.loadbalancer.server.port=8080"
        - "traefik.docker.network=ccsp-network"
    environment:
      - TRAEFIK_LOG_LEVEL=INFO
    healthcheck:
      test: ["CMD", "traefik", "healthcheck", "--ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  ccsp-network:
    external: true

volumes:
  traefik-data:
    driver: local
```

## 5. 部署步骤

### 5.1 部署Traefik服务
```bash
# 进入配置目录
cd /opt/traefik

# 部署Traefik服务
docker stack deploy -c docker-compose.yml traefik

# 验证部署状态
docker service ls | grep traefik
docker service logs traefik_traefik -f
```

### 5.2 验证部署
```bash
# 检查服务状态
docker service ps traefik_traefik

# 检查网络连接
docker network inspect ccsp-network

# 测试Dashboard访问
curl -H "Host: traefik.ccsp.local" http://localhost:8080/api/rawdata
```

### 5.3 配置DNS解析（可选）
```bash
# 在/etc/hosts中添加域名解析
echo "127.0.0.1 traefik.ccsp.local" | sudo tee -a /etc/hosts

# 或者配置内网DNS服务器
# *.ccsp.local -> Traefik所在节点IP
```

## 6. 与容器部署API集成

### 6.1 验证TraefikConfigHelper
确保`TraefikConfigHelper`类能正确生成标签：

```java
public class TraefikConfigHelper {
    
    public static Map<String, String> generateTraefikLabels(String serviceName, TraefikConfig config) {
        Map<String, String> labels = new HashMap<>();
        
        if (config == null || !Boolean.TRUE.equals(config.getEnabled())) {
            return labels;
        }
        
        // 启用Traefik
        labels.put("traefik.enable", "true");
        
        // 指定网络
        labels.put("traefik.docker.network", "ccsp-network");
        
        // 路由规则
        String routerName = serviceName.toLowerCase().replaceAll("[^a-z0-9-]", "-");
        labels.put("traefik.http.routers." + routerName + ".rule", 
                  "Host(`" + config.getDomain() + "`)");
        
        // 入口点
        labels.put("traefik.http.routers." + routerName + ".entrypoints", 
                  config.getHttpsEnabled() ? "websecure" : "web");
        
        // 服务端口
        labels.put("traefik.http.services." + routerName + ".loadbalancer.server.port", 
                  String.valueOf(config.getServicePort()));
        
        // 路径前缀（如果有）
        if (StringUtils.hasText(config.getPathPrefix()) && !"/".equals(config.getPathPrefix())) {
            labels.put("traefik.http.routers." + routerName + ".rule", 
                      "Host(`" + config.getDomain() + "`) && PathPrefix(`" + config.getPathPrefix() + "`)");
        }
        
        // HTTPS配置
        if (config.getHttpsEnabled()) {
            labels.put("traefik.http.routers." + routerName + ".tls", "true");
            labels.put("traefik.http.routers." + routerName + ".tls.certresolver", "letsencrypt");
        }
        
        return labels;
    }
    
    public static String generateAccessUrl(TraefikConfig config) {
        if (config == null || !Boolean.TRUE.equals(config.getEnabled())) {
            return null;
        }
        
        String protocol = config.getHttpsEnabled() ? "https" : "http";
        String path = StringUtils.hasText(config.getPathPrefix()) ? config.getPathPrefix() : "";
        
        return protocol + "://" + config.getDomain() + path;
    }
    
    public static boolean isValidConfig(TraefikConfig config) {
        return config != null 
            && Boolean.TRUE.equals(config.getEnabled())
            && StringUtils.hasText(config.getDomain())
            && config.getServicePort() != null 
            && config.getServicePort() > 0;
    }
}
```

### 6.2 测试容器部署
```bash
# 测试部署带Traefik配置的容器
curl -X POST http://localhost:8080/api-management/swarm/container/v1/deploy \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "containerName": "test-traefik-app",
    "imageId": 1,
    "applicationId": 1,
    "traefikConfig": {
      "enabled": true,
      "domain": "test-app.ccsp.local",
      "servicePort": 80,
      "pathPrefix": "/",
      "httpsEnabled": false
    },
    "deployedBy": 1
  }'
```

### 6.3 验证路由生效
```bash
# 检查Traefik是否发现了新服务
curl -H "Host: traefik.ccsp.local" http://localhost:8080/api/http/services

# 测试应用访问
curl -H "Host: test-app.ccsp.local" http://localhost
```

## 7. 监控和维护

### 7.1 日志监控
```bash
# 查看Traefik日志
docker service logs traefik_traefik -f

# 查看访问日志
tail -f /opt/traefik/logs/access.log
```

### 7.2 健康检查
```bash
# 检查Traefik健康状态
curl http://localhost:8080/ping

# 检查API状态
curl http://localhost:8080/api/rawdata
```

### 7.3 备份配置
```bash
# 备份Traefik配置
tar -czf traefik-config-$(date +%Y%m%d).tar.gz /opt/traefik/config/
```

## 8. 故障排除

### 8.1 常见问题
1. **服务发现不工作**：检查Docker socket权限和网络配置
2. **路由不生效**：验证标签格式和网络连接
3. **Dashboard无法访问**：检查端口映射和防火墙设置

### 8.2 调试命令
```bash
# 检查Traefik配置
docker exec $(docker ps -q -f name=traefik) traefik version

# 查看路由配置
curl http://localhost:8080/api/http/routers

# 检查服务发现
curl http://localhost:8080/api/http/services
```

## 9. 安全建议

1. **生产环境**：关闭Dashboard的insecure模式，配置认证
2. **TLS配置**：启用HTTPS和证书自动更新
3. **访问控制**：配置IP白名单和认证中间件
4. **日志审计**：启用详细的访问日志记录

这个部署方案提供了完整的Traefik集成解决方案，确保与现有的容器部署API无缝集成。
