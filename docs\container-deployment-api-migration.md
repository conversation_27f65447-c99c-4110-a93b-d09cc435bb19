# 容器部署API合并迁移指南

## 概述

为了简化API接口和提高维护性，我们将原有的4个容器部署接口合并为一个统一的部署接口。

## 原有接口（已弃用）

1. `/deploy` - 基础部署
2. `/deploy-with-hsm` - 带HSM设备资源配置的部署
3. `/deploy-with-strategy` - 带分布策略的部署  
4. `/deploy-with-traefik` - 带Traefik路由配置的部署

## 新的统一接口

### 接口地址
```
POST /api-management/swarm/container/v1/deploy
```

### 请求参数

统一的`ContainerDeployForm`现在支持所有配置选项：

```json
{
  "containerName": "my-app",
  "imageId": 1,
  "imageName": "nginx",
  "imageTag": "latest",
  "applicationId": 1,
  "replicas": 2,
  "servicePort": 80,
  
  // HSM设备配置（可选）
  "hsmDeviceConfig": {
    "encryptorId": 1,
    "encryptorName": "HSM-001",
    "serverIpAddr": "*************",
    "serverPort": 1024,
    "tcpConnNum": 5,
    "msgHeadLen": 4,
    "msgTailLen": 0,
    "asciiOrEbcdic": 0,
    "dynamicLibPath": "./libdeviceapi.so"
  },
  
  // 分布策略配置（可选）
  "distributionStrategy": "SPREAD_ACROSS_NODES",
  
  // Traefik路由配置（可选）
  "traefikConfig": {
    "enabled": true,
    "domain": "my-app.ccsp.local",
    "servicePort": 80,
    "pathPrefix": "/",
    "httpsEnabled": false
  },
  
  // 设备资源配置（可选）
  "deviceResourceConfig": {
    "deviceResourceType": "VSM",
    "deviceResourceIds": [1, 2],
    "allocationType": "exclusive",
    "priority": 1,
    "configData": "{\"key\": \"value\"}"
  },
  
  // 其他配置
  "environmentVars": {
    "ENV": "production"
  },
  "constraints": ["node.role==worker"],
  "labels": {
    "app": "my-app"
  }
}
```

### 响应格式

统一的响应格式包含所有相关配置信息：

```json
{
  "code": 20000,
  "message": "success",
  "data": {
    "container": {
      "id": 1,
      "containerName": "my-app",
      "status": "running",
      // ... 其他容器信息
    },
    
    // 如果配置了HSM
    "hsmConfigured": true,
    "hsmDeviceConfig": { /* HSM配置信息 */ },
    
    // 如果配置了分布策略
    "distributionStrategy": "SPREAD_ACROSS_NODES",
    "appliedConstraints": ["node.role==worker"],
    
    // 如果配置了Traefik
    "traefikConfigured": true,
    "traefikConfig": { /* Traefik配置信息 */ },
    "accessUrl": "http://my-app.ccsp.local",
    
    // 如果配置了设备资源
    "deviceResourceConfigured": true,
    "deviceResourceConfig": { /* 设备资源配置信息 */ }
  }
}
```

## 迁移指南

### 后端迁移

原有的特殊部署接口已标记为`@Deprecated`，但仍然可用以保持向后兼容性：

```java
// 旧方式（已弃用）
@PostMapping("/deploy-with-hsm")
@Deprecated
public Object deployContainerWithHsm(@RequestBody ContainerDeployForm deployForm) {
    // 内部调用统一接口
    return deployContainer(deployForm);
}

// 新方式（推荐）
@PostMapping("/deploy")
public Object deployContainer(@RequestBody ContainerDeployForm deployForm) {
    // 统一处理所有配置
}
```

### 前端迁移

#### JavaScript/Vue.js

```javascript
// 旧方式（已弃用）
import { deployContainerWithHsm, deployContainerWithStrategy, deployContainerWithTraefik } from '@/api/docker/container'

// 新方式（推荐）
import { deployContainer } from '@/api/docker/container'

// 统一调用
const deployData = {
  containerName: 'my-app',
  imageId: 1,
  // 根据需要添加各种配置
  hsmDeviceConfig: { /* HSM配置 */ },
  distributionStrategy: 'SPREAD_ACROSS_NODES',
  traefikConfig: { /* Traefik配置 */ }
}

deployContainer(deployData).then(response => {
  // 处理响应
  if (response.data.hsmConfigured) {
    console.log('HSM已配置')
  }
  if (response.data.accessUrl) {
    console.log('访问地址：', response.data.accessUrl)
  }
})
```

### 配置示例

#### 1. 基础部署
```json
{
  "containerName": "simple-app",
  "imageId": 1,
  "replicas": 1
}
```

#### 2. 带HSM设备的部署
```json
{
  "containerName": "secure-app",
  "imageId": 1,
  "hsmDeviceConfig": {
    "encryptorId": 1,
    "serverIpAddr": "*************",
    "serverPort": 1024
  }
}
```

#### 3. 高可用部署（多副本 + 分布策略）
```json
{
  "containerName": "ha-app",
  "imageId": 1,
  "replicas": 3,
  "distributionStrategy": "SPREAD_ACROSS_NODES"
}
```

#### 4. 完整配置部署
```json
{
  "containerName": "full-app",
  "imageId": 1,
  "replicas": 2,
  "hsmDeviceConfig": { /* HSM配置 */ },
  "distributionStrategy": "SPREAD_ACROSS_NODES",
  "traefikConfig": {
    "enabled": true,
    "domain": "full-app.ccsp.local",
    "servicePort": 8080
  },
  "deviceResourceConfig": {
    "deviceResourceType": "VSM",
    "deviceResourceIds": [1],
    "allocationType": "exclusive"
  }
}
```

## 兼容性说明

- 原有的特殊部署接口仍然可用，但已标记为`@Deprecated`
- 建议在新项目中使用统一的`/deploy`接口
- 原有接口将在未来版本中移除，请及时迁移

## 优势

1. **简化API**：只需要记住一个部署接口
2. **统一配置**：所有配置选项都在一个请求中
3. **更好的维护性**：减少代码重复，便于维护
4. **向后兼容**：原有接口仍然可用
5. **灵活组合**：可以任意组合不同的配置选项

## 注意事项

1. 确保前端代码中移除对已弃用API的直接调用
2. 测试各种配置组合的兼容性
3. 更新相关文档和示例代码
4. 在日志中会看到使用已弃用接口的警告信息
